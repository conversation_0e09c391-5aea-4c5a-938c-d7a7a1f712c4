127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /demo.css?version=1631964550726 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:10 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:22235963 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/System/Network/interfaces HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:16 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:18 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:18 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:27 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:28 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:28 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:28 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:29 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:29 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:30 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:30 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:34 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:40 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:43 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:45 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:46 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:29:47 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:16 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:37 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=c7e4c4848b4d3f50ffe70fcca78200e5 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /demo.css?version=1631964638477 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:38 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:40185834 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/System/Network/interfaces HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:43 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:44 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:44 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:48 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:48 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:48 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:48 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:30:51 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:08 +0800] "GET /codebase//playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:13 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:19 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:36 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:41 +0800] "PUT /ISAPI/PTZCtrl/channels/1/position3D HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:31:43 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:32:13 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:32:43 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:33:13 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:33:43 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:33:50 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=ce098bdf1907a689e6cfe494c814a777 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:04 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:06 +0800] "GET /demo.css?version=1631964846759 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:06 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:06 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:12 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:12 +0800] "GET /demo.css?version=1631964852530 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:13 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:13 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:40012663 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:13 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/System/Network/interfaces HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:14 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:16 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:18 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=7b7bf682fedfbdc7306eee4e4955f59a HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:19 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:19 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:20 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:20 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:21 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:21 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:22 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:22 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:23 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:23 +0800] "PUT /ISAPI/PTZCtrl/channels/1/continuous HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:44 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [18/Sep/2021:19:34:56 +0800] "GET /webSocketVideoCtrlProxy/?version=1.0&cipherSuites=1&token=2a77356a98196f9b430b1ed8508d9085 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /demo.css?version=1634214159789 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:39 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:30974424 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:41 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:44 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:22:49 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=IXPzgGzMPCONYquKolskUbB9Y4KjMD9GL3WhXxhnA7FfPQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:03 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:03 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:06 +0800] "GET /demo.css?version=1634214159789 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:06 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:11 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:41 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:47 +0800] "GET /webSocketVideoCtrlProxy/?version=1.0&cipherSuites=1&token=OnH5xS1DPWo3OdQTz8pJmHVu4+WJhKYexNrXJt9rVkhxPQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /demo.css?version=1634214229739 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:49 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:16136992 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:51 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:52 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=lBg/7UuEXXzyV9Hfpd6rPgn6l/gj6+lbQNQu5bM5W66iPQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /demo.css?version=1634214238200 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:23:58 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:08 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /demo.css?version=1634214249014 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:09 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:28355910 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:10 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:14 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:24:40 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:10 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:40 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=IT2uJIOBfKQm1j53JP/DevfQJMwyQq5ufZjWO9PRuj24PQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /demo.css?version=1634214354132 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:54 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:10192639 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:56 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:25:58 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=Z7AxqwpBqSCz2EkOCs8lpn/VJY1Tqk2AS+2NBXUoFxggPgQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /demo.css?version=1634214368351 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:26:08 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /demo.css?version=1634214987886 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:27 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:28 +0800] "GET /demo.css?version=1634214987886 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /demo.css?version=1634214990702 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:30 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:71732984 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:32 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:35 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:36 +0800] "GET /?version=0.1&cipherSuites=0&sessionID= HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:40 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:40 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:40 +0800] "GET /?version=0.1&cipherSuites=0&sessionID= HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:45 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:45 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:36:45 +0800] "GET /?version=0.1&cipherSuites=0&sessionID= HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:02 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:02 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:02 +0800] "GET /?version=0.1&cipherSuites=0&sessionID= HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:02 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:33 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /demo.css?version=1634215071949 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:51 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:17683278 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:52 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:53 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=tDmsCoR//DiQAXnJk1FCyaqeHK1ys1bQKhR3GyXppDLrQAQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /demo.css?version=1634215079305 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:37:59 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:31 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:31 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:21194445 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:31 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:32 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=GJLGzJaC9jCoUHCR2xMn61LP7iOQGluZBG8n91pD7KxOQQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /demo.css?version=1634215177927 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:39:37 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /demo.css?version=1634215582408 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:22 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /demo.css?version=1634215586185 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:26 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:12776068 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:27 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:30 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:31 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:31 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:31 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:46:57 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:47:27 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:47:57 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=Cg6VKKwrIK+6Pvy9HuAkQIsWA2pPq2rKtKXWS90OBjnxQgQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /demo.css?version=1634215686224 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:06 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:26518072 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:07 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:08 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=n5Yh3RVaGen3j+3Lhzi1sDgKTqO+VB5MIe7IPvlGqqNSQwQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /demo.css?version=1634215707653 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:27 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:38357816 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:48:29 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:49:03 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:49:33 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:50:03 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:50:33 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:51:03 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:51:33 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:03 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=dXm2H256vvwZ9h3eExxRr7yDg1zQN80on8/q6UNs1Q5oQwQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /demo.css?version=1634215928734 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:52:08 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /demo.css?version=1634215986222 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:06 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /demo.css?version=1634216022820 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:42 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:32318071 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:44 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:53:47 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=zwPPOelU8KUy7IUnMe7qJcRhJSs51l/gasMFW3FKhQGmRAQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /demo.css?version=1634216051370 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:11 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:16806426 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:14 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/SuperRender_20.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:17 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:54:45 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:55:15 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:55:45 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:15 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=7wuMfZ1M7eVB/JlG2j67amY2LBReECnlP70H//dOVszDRAQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /demo.css?version=1634216183091 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:20:56:23 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /demo.css?version=1634216820579 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:00 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:15941883 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:02 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:04 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /demo.css?version=1634216835683 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:15 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:18574927 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:19 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:20 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:30 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:30 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:31 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:31 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:31 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:31 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:07:49 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:08:19 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:08:26 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:08:26 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:08:49 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:09:19 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:09:49 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:19 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:32 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:32 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:34 +0800] "GET /demo.css?version=1634216835683 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:34 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:10:49 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:11:04 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:11:04 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:11:41 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:12:59 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:13:33 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:04 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:34 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /demo.css?version=1634217293494 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:53 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /demo.css?version=1634217298608 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:14:58 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:27061551 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:00 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:02 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:12 +0800] "GET /webSocketVideoCtrlProxy?version=0.1&cipherSuites=0&token=6MsovydCeC8k2TT+qaQ3jQaqnm80cdwyn/DRGHhY7T2fSQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:15 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:15 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:21 +0800] "GET /webSocketVideoCtrlProxy?version=1.0&cipherSuites=1&token=QQnZNQDQZl0eg8Cm/nr3Mfz3976OSQw0evkLInaXzQCsSQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:28 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:28 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:29 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:29 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:29 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:29 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:30 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:15:34 +0800] "GET /webSocketVideoCtrlProxy?version=1.0&cipherSuites=1&token=0qEMJduzaLXo6lSJbdm1QgL7dMm3D3Hvqfa7Meo/OTu5SQQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:00 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:30 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:37 +0800] "GET /demo.css?version=1634217298608 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:37 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /cn/demo.html HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /codebase/encryption/AES.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /jquery-1.7.1.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /codebase/encryption/cryptico.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /codebase/encryption/crypto-3.1.2.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /codebase/webVideoCtrl.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /cn/demo.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /demo.css?version=1634217399774 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:39 +0800] "GET /codebase//jsPlugin-1.2.0.min.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/Security/sessionLogin/capabilities?username=admin&random:10640032 HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "POST /ISAPI/Security/sessionLogin HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/System/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/System/Video/inputs/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/ContentMgmt/InputProxy/channels/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/ContentMgmt/ZeroVideo/channels HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/System/Network/PPPoE/1/status HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/System/Network/Bond HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:44 +0800] "GET /ISAPI/Security/adminAccesses HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/AudioRenderer.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/SuperRender_10.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:45 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /SDK/capabilities HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /ISAPI/Security/token?format=json HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /codebase//playctrl/DecodeWorker.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /codebase//playctrl/Decoder.js HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:16:53 +0800] "GET /codebase//playctrl/Decoder.wasm HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:17:04 +0800] "GET /webSocketVideoCtrlProxy?version=1.0&cipherSuites=1&token=eIwMKaAjjnAaNvj5P1AAFe4NmqC5RASQljOp1yho2V4OSgQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:17:05 +0800] "GET /webSocketVideoCtrlProxy?version=0.1&cipherSuites=0&token=wHV62aDqAXQFV1Kh+jQeP7L5XKSvtTKS5GkH5tZ9u88GSgQAAAAAAA== HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [14/Oct/2021:21:17:14 +0800] "PUT /ISAPI/Security/sessionHeartbeat HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
127.0.0.1 - - [31/Jul/2025:22:32:10 +0800] "GET /favicon.ico HTTP/1.1" access_loglogs/access.logmain
