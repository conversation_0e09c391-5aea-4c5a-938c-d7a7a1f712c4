﻿<!doctype html>
<html>
<head>
    <title></title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-Control" content="no-cache, must-revalidate" />
    <meta http-equiv="Expires" content="0" />
    <script>
        document.write("<link type='text/css' href='../demo.css?version=" + new Date().getTime() + "' rel='stylesheet' />");
    </script>
</head>
<body>
<div class="left">
    <div id="divPlugin" class="plugin"></div>
    <fieldset class="login">
        <legend>Login</legend>
        <table cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td class="tt">IP address</td>
                <td><input id="loginip" type="text" class="txt" value="************" /></td>
                <td class="tt">Port</td>
                <td><input id="port" type="text" class="txt" value="80" /></td>
            </tr>
            <tr>
                <td class="tt">User name</td>
                <td><input id="username" type="text" class="txt" value="admin" /></td>
                <td class="tt">Password</td>
                <td><input id="password" type="password" class="txt" value="hik12345" /></td>
            </tr>
            <tr>
                <td class="tt">Device port</td>
                <td colspan="2"><input id="deviceport" type="text" class="txt" />（optional）</td>
                <td>
                    Split screen&nbsp;
                    <select class="sel2" onchange="changeWndNum(this.value);">
                        <option value="1" selected>1x1</option>
                        <option value="2">2x2</option>
                        <option value="3">3x3</option>
                        <option value="4">4x4</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">RTSP port</td>
                <td colspan="3"><input id="rtspport" type="text" class="txt" />（optional）</td>
            </tr>
            <tr>
                <td colspan="4">
                    <input type="button" class="btn" value="Login" onclick="clickLogin();" />
                    <input type="button" class="btn" value="Logout" onclick="clickLogout();" />
                    <input type="button" class="btn2" value="Get basic info" onclick="clickGetDeviceInfo();" />
                </td>
            </tr>
            <tr>
                <td class="tt">Logined devices</td>
                <td>
                    <select id="ip" class="sel" onchange="getChannelInfo();getDevicePort();"></select>
                </td>
                <td class="tt">Channel list</td>
                <td>
                    <select id="channels" class="sel"></select>
                </td>
            </tr>
            <tr>
                    <td>
                            <input type="button" class="btn2" value="Start preview" onclick="clickStartRealPlay();" />
                        </td>
                        <td>
                            <input type="button" class="btn2" value="Stop preview" onclick="clickStopRealPlay();" />
                        </td>
                        <td>
                            <input type="button" class="btn2" value="set osd" onclick="setTextOverlay();" />
                        </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="ipchannel">
        <legend>Digital channel</legend>
        <table width="100%" cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td><input type="button" class="btn" value="Get digital channel list" onclick="clickGetDigitalChannelInfo();" /></td>
            </tr>
            <tr>
                <td>
                    <div class="digitaltdiv">
                        <table id="digitalchannellist" class="digitalchannellist" cellpadding="0" cellspacing="0" border="0"></table>
                    </div>
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="localconfig">
        <legend>Local configuration</legend>
        <table cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td class="tt">Play performance</td>
                <td>
                    <select id="netsPreach" name="netsPreach" class="sel">
                        <option value="0">Shortest delay</option>
                        <option value="1">Real-time</option>
                        <option value="2">Balance</option>
                        <option value="3">Smooth</option>
                    </select>
                </td>
                <td class="tt">Image size</td>
                <td>
                    <select id="wndSize" name="wndSize" class="sel">
                        <option value="0">Full</option>
                        <option value="1">4:3</option>
                        <option value="2">16:9</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Rules</td>
                <td>
                    <select id="rulesInfo" name="rulesInfo" class="sel">
                        <option value="1">Enable</option>
                        <option value="0">Disable</option>
                    </select>
                </td>
                <td class="tt">Snapshot format</td>
                <td>
                    <select id="captureFileFormat" name="captureFileFormat" class="sel">
                        <option value="0">JPEG</option>
                        <option value="1">BMP</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Package size</td>
                <td>
                    <select id="packSize" name="packSize" class="sel">
                        <option value="0">256M</option>
                        <option value="1">512M</option>
                        <option value="2">1G</option>
                    </select>
                </td>
                <td class="tt">Protocol</td>
                <td>
                    <select id="protocolType" name="protocolType" class="sel">
                        <option value="0">TCP</option>
                        <option value="2">UDP</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Save record files to</td>
                <td colspan="3"><input id="recordPath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('recordPath', 0);" /></td>
            </tr>
            <tr>
                <td class="tt">Save downloaded files to</td>
                <td colspan="3"><input id="downloadPath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('downloadPath', 0);" /></td>
            </tr>
            <tr>
                <td class="tt">Save snapshots in live view to</td>
                <td colspan="3"><input id="previewPicPath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('previewPicPath', 0);" /></td>
            </tr>
            <tr>
                <td class="tt">Save snapshots when playback to</td>
                <td colspan="3"><input id="playbackPicPath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('playbackPicPath', 0);" /></td>
            </tr>
            <tr>
                <td class="tt">Save clips to</td>
                <td colspan="3"><input id="playbackFilePath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('playbackFilePath', 0);" /></td>
            </tr>
            <tr>
                <td class="tt">Save device snapshots to</td>
                <td colspan="3"><input id="devicePicPath" type="text" class="txt2" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('devicePicPath', 0);" /></td>
            </tr>
            <tr>
                <td colspan="4"><input type="button" class="btn" value="Get" onclick="clickGetLocalCfg();" />&nbsp;<input type="button" class="btn" value="Set" onclick="clickSetLocalCfg();" /></td>
            </tr>
        </table>
    </fieldset>
</div>
<div class="left">
    <fieldset class="preview">
        <legend>Browse</legend>
        <table cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td class="tt">Stream type</td>
                <td>
                    <select id="streamtype" class="sel">
                        <option value="1">Main stream</option>
                        <option value="2">Sub stream</option>
                        <option value="3">Third stream</option>
                        <option value="4">Transcode stream</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Volume</td>
                <td>
                    <input type="text" id="volume" class="txt" value="50" maxlength="3" />&nbsp;<input type="button" class="btn" value="Set" onclick="clickSetVolume();" />(Range：0~100)
                </td>
                <td>
                    <input type="button" class="btn" value="Audio On" onclick="clickOpenSound();" />
                    <input type="button" class="btn" value="Audio Off" onclick="clickCloseSound();" />
                </td>
            </tr>
            <tr>
                <td class="tt">Voice channel</td>
                <td>
                    <select id="audiochannels" class="sel">
                        
                    </select>
                    <input type="button" class="btn" value="Get channel" onclick="clickGetAudioInfo();" />
                </td>
                <td>
                    <input type="button" class="btn" value="Voice Talk" onclick="clickStartVoiceTalk();" />
                    <input type="button" class="btn" value="Stop Voice Talk" onclick="clickStopVoiceTalk();" />
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <input type="button" class="btn2" value="Capture" onclick="clickCapturePic();" />
                    <input type="button" class="btn" value="capture onload" onclick="clickCapturePicData();" />
                    <input type="button" class="btn2" value="Start recording" onclick="clickStartRecord('realplay');" />
                    <input type="button" class="btn2" value="Stop recording" onclick="clickStopRecord('realplay');" />
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    <input type="button" class="btn2" value="Enable E zoom" onclick="clickEnableEZoom();" />
                    <input type="button" class="btn2" value="Disable E zoom" onclick="clickDisableEZoom();" />
                    <input type="button" class="btn2" value="Enable 3D zoom" onclick="clickEnable3DZoom();" />
                    <input type="button" class="btn2" value="Disable 3D zoom" onclick="clickDisable3DZoom();" />
                    <input type="button" class="btn2" value="Full screen" onclick="clickFullScreen();" />
                </td>
            </tr>
            <tr>
                <td colspan="3">
                    Resolution:<input id="resolutionWidth" type="text" class="txt" /> x <input id="resolutionHeight" type="text" class="txt" />
                    <input type="button" class="btn2" value="Device capture" onclick="clickDeviceCapturePic();" />
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="draw">
        <legend>drawing</legend>
        <table cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td>
                    <input type="button" class="btn2" value="Enable drawing" onclick="clickEnableDraw();" />
                    <input type="button" class="btn2" value="Disable drawing" onclick="clickDisableDraw();" />
                </td>
            </tr>
            <tr>
                <td>
                    Graph ID：<input id="snapId" type="text" class="txt" />
                    Name：<input id="snapName" type="text" class="txt" />
                </td>
            </tr>
            <tr>
                <td>
                    <input type="button" class="btn2" value="Add the graph" onclick="clickAddSnapPolygon()" />
                    <input type="button" class="btn2" value="Delete the graph" onclick="clickDelSnapPolygon()" />
                    <input type="button" class="btn2" value="Edit the graph" onclick="clickEditSnapPolygon()" />
                    <input type="button" class="btn2" value="Stop editing" onclick="clickStopSnapPolygon()" />
                    <input type="button" class="btn2" value="Get the graph" onclick="clickGetSnapPolygon()" />
                    <input type="button" class="btn2" value="Set the graph" onclick="clickSetSnapPolygon()" />
                </td>
            </tr>
            <tr>
                <td>
                    <input type="button" class="btn2" value="Clear the graph" onclick="clickDelAllSnapPolygon()" />
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="ptz">
        <legend>PTZ control</legend>
        <table cellpadding="0" cellspacing="3" border="0" class="left">
            <tr>
                <td>
                    <input type="button" class="btn" value="Up-left" onmousedown="mouseDownPTZControl(5);" onmouseup="mouseUpPTZControl();" />
                    <input type="button" class="btn" value="Up" onmousedown="mouseDownPTZControl(1);" onmouseup="mouseUpPTZControl();" />
                    <input type="button" class="btn" value="Up-right" onmousedown="mouseDownPTZControl(7);" onmouseup="mouseUpPTZControl();" />
                </td>
            </tr>
            <tr>
                <td>
                    <input type="button" class="btn" value="Left" onmousedown="mouseDownPTZControl(3);" onmouseup="mouseUpPTZControl();" />
                    <input type="button" class="btn" value="Auto" onclick="mouseDownPTZControl(9);" />
                    <input type="button" class="btn" value="Right" onmousedown="mouseDownPTZControl(4);" onmouseup="mouseUpPTZControl();" />
                </td>
            </tr>
            <tr>
                <td>
                    <input type="button" class="btn" value="Down-left" onmousedown="mouseDownPTZControl(6);" onmouseup="mouseUpPTZControl();" />
                    <input type="button" class="btn" value="Down" onmousedown="mouseDownPTZControl(2);" onmouseup="mouseUpPTZControl();" />
                    <input type="button" class="btn" value="Down-right" onmousedown="mouseDownPTZControl(8);" onmouseup="mouseUpPTZControl();" />
                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="3" border="0" class="left">
            <tr>
                <td class="tt">PTZ speed</td>
                <td>
                    <select id="ptzspeed" class="sel">
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4" selected>4</option>
                        <option value="5">5</option>
                        <option value="6">6</option>
                        <option value="7">7</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Preset</td>
                <td><input id="preset" type="text" class="txt" value="1" /></td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="button" class="btn" value="Set" onclick="clickSetPreset();" />
                    <input type="button" class="btn" value="Call" onclick="clickGoPreset();" />
                </td>
            </tr>
        </table>
        <table cellpadding="0" cellspacing="3" border="0" class="left">
            <tr>
                <td class="tt"><input type="button" class="btn2" value="Zoom+" onmousedown="PTZZoomIn()" onmouseup="PTZZoomStop()"></td>
                <td><input type="button" class="btn2" value="Zoom-" onmousedown="PTZZoomout()" onmouseup="PTZZoomStop()"></td>
            </tr>
            <tr>
                <td class="tt"><input type="button" class="btn2" value="Focus+" onmousedown="PTZFocusIn()" onmouseup="PTZFoucusStop()"></td>
                <td><input type="button" class="btn2" value="Focus-" onmousedown="PTZFoucusOut()" onmouseup="PTZFoucusStop()"></td>
            </tr>
            <tr>
                <td class="tt"><input type="button" class="btn2" value="Iris+" onmousedown="PTZIrisIn()" onmouseup="PTZIrisStop()"></td>
                <td><input type="button" class="btn2" value="Iris-" onmousedown="PTZIrisOut()" onmouseup="PTZIrisStop()"></td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="playback">
        <legend>Playback</legend>
        <table width="100%" cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td class="tt">Stream type</td>
                <td>
                    <select id="record_streamtype" class="sel">
                        <option value="1">Main stream</option>
                        <option value="2">Sub stream</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Start time</td>
                <td>
                    <input id="starttime" type="text" class="txt" value="2013-12-10 00:00:00" />(Time format:2013-11-11 12:34:56)
                </td>
            </tr>
            <tr>
                <td class="tt">End time</td>
                <td>
                    <input id="endtime" type="text" class="txt" value="2013-12-11 23:59:59" />
                    <input type="button" class="btn" value="Search" onclick="clickRecordSearch(0);" />
                </td>
            </tr>
            <tr>
                <td class="tt"> downloadBytime starttime</td>
                <td>
                    <input id="downloadstarttime" type="text" class="txt" value="2013-12-10 00:00:00" />（Time format：2013-11-11 12:34:56）
                </td>
            </tr>
            <tr>
                <td class="tt">downloadBytime endtime</td>
                <td>
                    <input id="downloadendtime" type="text" class="txt" value="2013-12-11 23:59:59" />
                    <input type="button" class="btn" value="download" onclick="clickStartDownloadRecordByTime();" />
                </td>
            </tr>
            <tr>
            <tr>
                <td colspan="2">
                    <div id="searchdiv" class="searchdiv">
                        <table id="searchlist" class="searchlist" cellpadding="0" cellspacing="0" border="0"></table>
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="button" class="btn2" value="Start play" onclick="clickStartPlayback();" />
                    <input type="button" class="btn2" value="Stop play" onclick="clickStopPlayback();" />
                    <input  id="btnReverse" type="button" class="btn" value="Reverse play" onclick="clickReversePlayback();" />
                    <input type="button" class="btn" value="Single frame" onclick="clickFrame();" />
                    <input id="transstream" type="checkbox" class="vtop" />&nbsp;Enable TransCode Stream
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="button" class="btn" value="Pause" onclick="clickPause();" />
                    <input type="button" class="btn" value="Resume" onclick="clickResume();" />
                    <input type="button" class="btn" value="Slow forward" onclick="clickPlaySlow();" />
                    <input type="button" class="btn" value="Fast forward" onclick="clickPlayFast();" />
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="button" class="btn2" value="Capture" onclick="clickCapturePic();" />
                    <input type="button" class="btn2" value="Start clip" onclick="clickStartRecord('playback');" />
                    <input type="button" class="btn2" value="Stop clip" onclick="clickStopRecord('playback');" />
                    <input type="button" class="btn2" value="OSD time" onclick="clickGetOSDTime();" />&nbsp;<input id="osdtime" type="text" class="txt" readonly />
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="maintain">
        <legend>System maintenance</legend>
        <table width="100%" cellpadding="0" cellspacing="3" border="0">
                <tr>
                        <td>
                            <input id="edfpassword" type="password">
                            <input type="button" class="btn2" value="Export configuring files" onclick="clickExportDeviceConfig();" />
                        </td>
                    </tr>
            <tr>
                <td>
                    <input type="button" class="btn2" id="remoteconfig" title="Detect plugin version" value="Detect plugin version" onclick="clickCheckPluginVersion();" />
                    <input type="button" class="btn2" title="Remote configure" value="Remote configure" onclick="clickRemoteConfig();" />
                    <input type="button" class="btn2" title="Restore the default parameters" value="Restore the default parameters" onclick="clickRestoreDefault();" />
                </td>
            </tr>
            <tr>
                <td>
                    <input id="configFile" type="text" class="txt" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('configFile', 1);" />&nbsp;<input type="button" class="btn2" value="Import configuring files" onclick="clickImportDeviceConfig();" />
                </td>
            </tr>
            <tr>
                <td>
                    <input id="upgradeFile" type="text" class="txt" />&nbsp;<input type="button" class="btn" value="Browse" onclick="clickOpenFileDlg('upgradeFile', 1);" />&nbsp;<input type="button" class="btn2" value="Upgrade" onclick="clickStartUpgrade();" />
                </td>
            </tr>
        </table>
    </fieldset>
    <fieldset class="ipparse">
        <legend>Device IP Resolve</legend>
        <table cellpadding="0" cellspacing="3" border="0">
            <tr>
                <td class="tt">Mode</td>
                <td colspan="3">
                    <select id="devicemode" class="sel" onchange="changeIPMode(this.value);">
                        <option value="1">IPServer</option>
                        <option value="2">HiDDNS</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="tt">Server Address</td>
                <td><input id="serveraddress" type="text" class="txt" value="" /></td>
                <td class="tt">Server Port</td>
                <td><input id="serverport" type="text" class="txt" value="7071" /></td>
            </tr>
            <tr>
                <td class="tt">Device Name</td>
                <td><input id="deviceid" type="text" class="txt" value="" /></td>
                <td class="tt">&nbsp;</td>
                <td><input type="button" class="btn" value="GetDeviceIP" onclick="clickGetDeviceIP();" /></td>
            </tr>
        </table>
    </fieldset>
</div>
<div class="left">
    <fieldset class="operate">
        <legend>Operation information</legend>
        <div id="opinfo" class="opinfo"></div>
    </fieldset>
    <fieldset class="callback">
        <legend>Event callback information</legend>
        <div id="cbinfo" class="cbinfo"></div>
    </fieldset>
</div>
</body>
<script src="../jquery-1.7.1.min.js"></script>
<script src="../codebase/encryption/AES.js"></script>
<script src="../codebase/encryption/cryptico.min.js"></script>
<!-- <script src="../codebase/encryption/encryption.js"></script> -->
<script src="../codebase/encryption/crypto-3.1.2.min.js"></script>
<script id="videonode" src="../codebase/webVideoCtrl.js"></script>
<script src="demo.js"></script>
</html>