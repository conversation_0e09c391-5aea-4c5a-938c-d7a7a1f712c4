(function e(t,i){if(typeof exports==="object"&&typeof module==="object")module.exports=i();else if(typeof define==="function"&&define.amd)define([],i);else{var n=i();for(var r in n)(typeof exports==="object"?exports:t)[r]=n[r]}})(window,function(){return function(i){var n={};function r(e){if(n[e]){return n[e].exports}var t=n[e]={i:e,l:false,exports:{}};i[e].call(t.exports,t,t.exports,r);t.l=true;return t.exports}r.m=i;r.c=n;r.d=function(e,t,i){if(!r.o(e,t)){Object.defineProperty(e,t,{enumerable:true,get:i})}};r.r=function(e){if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})};r.t=function(t,e){if(e&1)t=r(t);if(e&8)return t;if(e&4&&typeof t==="object"&&t&&t.__esModule)return t;var i=Object.create(null);r.r(i);Object.defineProperty(i,"default",{enumerable:true,value:t});if(e&2&&typeof t!="string")for(var n in t)r.d(i,n,function(e){return t[e]}.bind(null,n));return i};r.n=function(t){var e=t&&t.__esModule?function e(){return t["default"]}:function e(){return t};r.d(e,"a",e);return e};r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};r.p="";return r(r.s=5)}([function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){function e(){r(this,e);this._keyStr="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}n(e,[{key:"$",value:function e(t){var i=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/;var n=/^(?:\s*(<[\w\W]+>)[^>]*|.([\w-]*))$/;if(i.test(t)){var r=i.exec(t);return document.getElementById(r[2])}else if(n.test(t)){var o=n.exec(t);var a=document.getElementsByTagName("*");var s=[];for(var l=0,u=a.length;l<u;l++){if(a[l].className.match(new RegExp("(\\s|^)"+o[2]+"(\\s|$)"))){s.push(a[l])}}return s}}},{key:"dateFormat",value:function e(t,i){var n={"M+":t.getMonth()+1,"d+":t.getDate(),"h+":t.getHours(),"m+":t.getMinutes(),"s+":t.getSeconds(),"q+":Math.floor((t.getMonth()+3)/3),S:t.getMilliseconds()};if(/(y+)/.test(i)){i=i.replace(RegExp.$1,(t.getFullYear()+"").substr(4-RegExp.$1.length))}for(var r in n){if(new RegExp("("+r+")").test(i)){i=i.replace(RegExp.$1,RegExp.$1.length===1?n[r]:("00"+n[r]).substr((""+n[r]).length))}}return i}},{key:"downloadFile",value:function e(t,i){var n=t;if(!(t instanceof Blob||t instanceof File)){n=new Blob([t])}var r=window.URL.createObjectURL(n);var o=window.document.createElement("a");o.href=r;o.download=i;var a=document.createEvent("MouseEvents");a.initEvent("click",true,true);o.dispatchEvent(a)}},{key:"createxmlDoc",value:function e(){var t;var i=["MSXML2.DOMDocument","MSXML2.DOMDocument.5.0","MSXML2.DOMDocument.4.0","MSXML2.DOMDocument.3.0","Microsoft.XmlDom"];for(var n=0,r=i.length;n<r;n++){try{t=new ActiveXObject(i[n]);break}catch(e){t=document.implementation.createDocument("","",null);break}}t.async="false";return t}},{key:"parseXmlFromStr",value:function e(t){if(null===t||""===t){return null}var i=this.createxmlDoc();if(navigator.appName==="Netscape"||navigator.appName==="Opera"){var n=new DOMParser;i=n.parseFromString(t,"text/xml")}else{i.loadXML(t)}return i}},{key:"encode",value:function e(t){var i="";var n;var r;var o;var a;var s;var l;var u;var f=0;t=this._utf8_encode(t);while(f<t.length){n=t.charCodeAt(f++);r=t.charCodeAt(f++);o=t.charCodeAt(f++);a=n>>2;s=(n&3)<<4|r>>4;l=(r&15)<<2|o>>6;u=o&63;if(isNaN(r)){l=u=64}else if(isNaN(o)){u=64}i=i+this._keyStr.charAt(a)+this._keyStr.charAt(s)+this._keyStr.charAt(l)+this._keyStr.charAt(u)}return i}},{key:"decode",value:function e(t){var i="";var n;var r;var o;var a;var s;var l;var u;var f=0;t=t.replace(/[^A-Za-z0-9+/=]/g,"");while(f<t.length){a=this._keyStr.indexOf(t.charAt(f++));s=this._keyStr.indexOf(t.charAt(f++));l=this._keyStr.indexOf(t.charAt(f++));u=this._keyStr.indexOf(t.charAt(f++));n=a<<2|s>>4;r=(s&15)<<4|l>>2;o=(l&3)<<6|u;i=i+String.fromCharCode(n);if(l!==64){i=i+String.fromCharCode(r)}if(u!==64){i=i+String.fromCharCode(o)}}i=this._utf8_decode(i);return i}},{key:"_utf8_encode",value:function e(t){t=t.replace(/\r\n/g,"\n");var i="";for(var n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128){i+=String.fromCharCode(r)}else if(r>127&&r<2048){i+=String.fromCharCode(r>>6|192);i+=String.fromCharCode(r&63|128)}else{i+=String.fromCharCode(r>>12|224);i+=String.fromCharCode(r>>6&63|128);i+=String.fromCharCode(r&63|128)}}return i}},{key:"_utf8_decode",value:function e(t){var i="";var n=0;var r=0;var o=0;while(n<t.length){r=t.charCodeAt(n);if(r<128){i+=String.fromCharCode(r);n++}else if(r>191&&r<224){o=t.charCodeAt(n+1);i+=String.fromCharCode((r&31)<<6|o&63);n+=2}else{o=t.charCodeAt(n+1);var a=t.charCodeAt(n+2);i+=String.fromCharCode((r&15)<<12|(o&63)<<6|a&63);n+=3}}return i}},{key:"isFirefox",value:function e(){var t=false;var i=navigator.userAgent.toLowerCase();var n="";var r=-1;if(i.match(/firefox\/([\d.]+)/)){n=i.match(/firefox\/([\d.]+)/)[1];r=parseInt(n.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isSafari",value:function e(){var t=false;var i=navigator.userAgent.toLowerCase();var n="";var r=-1;if(i.match(/version\/([\d.]+).safari./)){n=i.match(/version\/([\d.]+).safari./)[1];r=parseInt(n.split(".")[0],10);if(r>-1){t=true}}return t}},{key:"isEdge",value:function e(){return navigator.userAgent.toLowerCase().indexOf("edge")>-1}},{key:"isIOS",value:function e(){var t=navigator.userAgent;var i=!!t.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);return i}}]);return e}();var a=t.oTool=new o},function(e,t,i){var n=i(8);var r=i(9);var o=r;o.v1=n;o.v4=r;e.exports=o},function(e,t){var i=typeof crypto!="undefined"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto!="undefined"&&typeof window.msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto);if(i){var n=new Uint8Array(16);e.exports=function e(){i(n);return n}}else{var r=new Array(16);e.exports=function e(){for(var t=0,i;t<16;t++){if((t&3)===0)i=Math.random()*4294967296;r[t]=i>>>((t&3)<<3)&255}return r}}},function(e,t){var r=[];for(var i=0;i<256;++i){r[i]=(i+256).toString(16).substr(1)}function n(e,t){var i=t||0;var n=r;return[n[e[i++]],n[e[i++]],n[e[i++]],n[e[i++]],"-",n[e[i++]],n[e[i++]],"-",n[e[i++]],n[e[i++]],"-",n[e[i++]],n[e[i++]],"-",n[e[i++]],n[e[i++]],n[e[i++]],n[e[i++]],n[e[i++]],n[e[i++]]].join("")}e.exports=n},function(ri,oi,e){var ai,si;
/*!
 * jQuery JavaScript Library v3.5.0
 * https://jquery.com/
 *
 * Includes Sizzle.js
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://jquery.org/license
 *
 * Date: 2020-04-10T15:07Z
 */
(function(e,t){"use strict";if(true&&typeof ri.exports==="object"){ri.exports=e.document?t(e,true):function(e){if(!e.document){throw new Error("jQuery requires a window with a document")}return t(e)}}else{t(e)}})(typeof window!=="undefined"?window:this,function(P,e){"use strict";var t=[];var n=Object.getPrototypeOf;var s=t.slice;var v=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)};var l=t.push;var r=t.indexOf;var i={};var o=i.toString;var y=i.hasOwnProperty;var a=y.toString;var u=a.call(Object);var m={};var g=function e(t){return typeof t==="function"&&typeof t.nodeType!=="number"};var S=function e(t){return t!=null&&t===t.window};var C=P.document;var f={type:true,src:true,nonce:true,noModule:true};function b(e,t,i){i=i||C;var n,r,o=i.createElement("script");o.text=e;if(t){for(n in f){r=t[n]||t.getAttribute&&t.getAttribute(n);if(r){o.setAttribute(n,r)}}}i.head.appendChild(o).parentNode.removeChild(o)}function w(e){if(e==null){return e+""}return typeof e==="object"||typeof e==="function"?i[o.call(e)]||"object":typeof e}var h="3.5.0",x=function(e,t){return new x.fn.init(e,t)};x.fn=x.prototype={jquery:h,constructor:x,length:0,toArray:function(){return s.call(this)},get:function(e){if(e==null){return s.call(this)}return e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=x.merge(this.constructor(),e);t.prevObject=this;return t},each:function(e){return x.each(this,e)},map:function(i){return this.pushStack(x.map(this,function(e,t){return i.call(e,t,e)}))},slice:function(){return this.pushStack(s.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(x.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(x.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,i=+e+(e<0?t:0);return this.pushStack(i>=0&&i<t?[this[i]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice};x.extend=x.fn.extend=function(){var e,t,i,n,r,o,a=arguments[0]||{},s=1,l=arguments.length,u=false;if(typeof a==="boolean"){u=a;a=arguments[s]||{};s++}if(typeof a!=="object"&&!g(a)){a={}}if(s===l){a=this;s--}for(;s<l;s++){if((e=arguments[s])!=null){for(t in e){n=e[t];if(t==="__proto__"||a===n){continue}if(u&&n&&(x.isPlainObject(n)||(r=Array.isArray(n)))){i=a[t];if(r&&!Array.isArray(i)){o=[]}else if(!r&&!x.isPlainObject(i)){o={}}else{o=i}r=false;a[t]=x.extend(u,o,n)}else if(n!==undefined){a[t]=n}}}}return a};x.extend({expando:"jQuery"+(h+Math.random()).replace(/\D/g,""),isReady:true,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,i;if(!e||o.call(e)!=="[object Object]"){return false}t=n(e);if(!t){return true}i=y.call(t,"constructor")&&t.constructor;return typeof i==="function"&&a.call(i)===u},isEmptyObject:function(e){var t;for(t in e){return false}return true},globalEval:function(e,t,i){b(e,{nonce:t&&t.nonce},i)},each:function(e,t){var i,n=0;if(c(e)){i=e.length;for(;n<i;n++){if(t.call(e[n],n,e[n])===false){break}}}else{for(n in e){if(t.call(e[n],n,e[n])===false){break}}}return e},makeArray:function(e,t){var i=t||[];if(e!=null){if(c(Object(e))){x.merge(i,typeof e==="string"?[e]:e)}else{l.call(i,e)}}return i},inArray:function(e,t,i){return t==null?-1:r.call(t,e,i)},merge:function(e,t){var i=+t.length,n=0,r=e.length;for(;n<i;n++){e[r++]=t[n]}e.length=r;return e},grep:function(e,t,i){var n,r=[],o=0,a=e.length,s=!i;for(;o<a;o++){n=!t(e[o],o);if(n!==s){r.push(e[o])}}return r},map:function(e,t,i){var n,r,o=0,a=[];if(c(e)){n=e.length;for(;o<n;o++){r=t(e[o],o,i);if(r!=null){a.push(r)}}}else{for(o in e){r=t(e[o],o,i);if(r!=null){a.push(r)}}}return v(a)},guid:1,support:m});if(typeof Symbol==="function"){x.fn[Symbol.iterator]=t[Symbol.iterator]}x.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){i["[object "+t+"]"]=t.toLowerCase()});function c(e){var t=!!e&&"length"in e&&e.length,i=w(e);if(g(e)||S(e)){return false}return i==="array"||t===0||typeof t==="number"&&t>0&&t-1 in e}var d=
/*!
 * Sizzle CSS Selector Engine v2.3.5
 * https://sizzlejs.com/
 *
 * Copyright JS Foundation and other contributors
 * Released under the MIT license
 * https://js.foundation/
 *
 * Date: 2020-03-14
 */
function(i){var e,d,b,o,r,p,h,v,w,l,u,_,P,a,C,y,s,f,m,x="sizzle"+1*new Date,c=i.document,T=0,n=0,g=le(),S=le(),k=le(),D=le(),M=function(e,t){if(e===t){u=true}return 0},R={}.hasOwnProperty,t=[],E=t.pop,I=t.push,z=t.push,A=t.slice,O=function(e,t){var i=0,n=e.length;for(;i<n;i++){if(e[i]===t){return i}}return-1},F="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|"+"ismap|loop|multiple|open|readonly|required|scoped",B="[\\x20\\t\\r\\n\\f]",L="(?:\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",q="\\["+B+"*("+L+")(?:"+B+"*([*^$|!~]?=)"+B+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+L+"))|)"+B+"*\\]",N=":("+L+")(?:\\(("+"('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|"+"((?:\\\\.|[^\\\\()[\\]]|"+q+")*)|"+".*"+")\\)|)",H=new RegExp(B+"+","g"),j=new RegExp("^"+B+"+|((?:^|[^\\\\])(?:\\\\.)*)"+B+"+$","g"),U=new RegExp("^"+B+"*,"+B+"*"),W=new RegExp("^"+B+"*([>+~]|"+B+")"+B+"*"),G=new RegExp(B+"|>"),V=new RegExp(N),Y=new RegExp("^"+L+"$"),X={ID:new RegExp("^#("+L+")"),CLASS:new RegExp("^\\.("+L+")"),TAG:new RegExp("^("+L+"|[*])"),ATTR:new RegExp("^"+q),PSEUDO:new RegExp("^"+N),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+B+"*(even|odd|(([+-]|)(\\d*)n|)"+B+"*(?:([+-]|)"+B+"*(\\d+)|))"+B+"*\\)|)","i"),bool:new RegExp("^(?:"+F+")$","i"),needsContext:new RegExp("^"+B+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+B+"*((?:-\\d)?\\d*)"+B+"*\\)|)(?=[^-]|$)","i")},J=/HTML$/i,K=/^(?:input|select|textarea|button)$/i,$=/^h\d$/i,Z=/^[^{]+\{\s*\[native \w/,Q=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ee=/[+~]/,te=new RegExp("\\\\[\\da-fA-F]{1,6}"+B+"?|\\\\([^\\r\\n\\f])","g"),ie=function(e,t){var i="0x"+e.slice(1)-65536;return t?t:i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,i&1023|56320)},ne=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,re=function(e,t){if(t){if(e==="\0"){return"�"}return e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" "}return"\\"+e},oe=function(){_()},ae=be(function(e){return e.disabled===true&&e.nodeName.toLowerCase()==="fieldset"},{dir:"parentNode",next:"legend"});try{z.apply(t=A.call(c.childNodes),c.childNodes);t[c.childNodes.length].nodeType}catch(e){z={apply:t.length?function(e,t){I.apply(e,A.call(t))}:function(e,t){var i=e.length,n=0;while(e[i++]=t[n++]){}e.length=i-1}}}function se(t,e,i,n){var r,o,a,s,l,u,f,h=e&&e.ownerDocument,c=e?e.nodeType:9;i=i||[];if(typeof t!=="string"||!t||c!==1&&c!==9&&c!==11){return i}if(!n){_(e);e=e||P;if(C){if(c!==11&&(l=Q.exec(t))){if(r=l[1]){if(c===9){if(a=e.getElementById(r)){if(a.id===r){i.push(a);return i}}else{return i}}else{if(h&&(a=h.getElementById(r))&&m(e,a)&&a.id===r){i.push(a);return i}}}else if(l[2]){z.apply(i,e.getElementsByTagName(t));return i}else if((r=l[3])&&d.getElementsByClassName&&e.getElementsByClassName){z.apply(i,e.getElementsByClassName(r));return i}}if(d.qsa&&!D[t+" "]&&(!y||!y.test(t))&&(c!==1||e.nodeName.toLowerCase()!=="object")){f=t;h=e;if(c===1&&(G.test(t)||W.test(t))){h=ee.test(t)&&me(e.parentNode)||e;if(h!==e||!d.scope){if(s=e.getAttribute("id")){s=s.replace(ne,re)}else{e.setAttribute("id",s=x)}}u=p(t);o=u.length;while(o--){u[o]=(s?"#"+s:":scope")+" "+Se(u[o])}f=u.join(",")}try{z.apply(i,h.querySelectorAll(f));return i}catch(e){D(t,true)}finally{if(s===x){e.removeAttribute("id")}}}}}return v(t.replace(j,"$1"),e,i,n)}function le(){var i=[];function n(e,t){if(i.push(e+" ")>b.cacheLength){delete n[i.shift()]}return n[e+" "]=t}return n}function ue(e){e[x]=true;return e}function fe(e){var t=P.createElement("fieldset");try{return!!e(t)}catch(e){return false}finally{if(t.parentNode){t.parentNode.removeChild(t)}t=null}}function he(e,t){var i=e.split("|"),n=i.length;while(n--){b.attrHandle[i[n]]=t}}function ce(e,t){var i=t&&e,n=i&&e.nodeType===1&&t.nodeType===1&&e.sourceIndex-t.sourceIndex;if(n){return n}if(i){while(i=i.nextSibling){if(i===t){return-1}}}return e?1:-1}function de(i){return function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type===i}}function pe(i){return function(e){var t=e.nodeName.toLowerCase();return(t==="input"||t==="button")&&e.type===i}}function ve(t){return function(e){if("form"in e){if(e.parentNode&&e.disabled===false){if("label"in e){if("label"in e.parentNode){return e.parentNode.disabled===t}else{return e.disabled===t}}return e.isDisabled===t||e.isDisabled!==!t&&ae(e)===t}return e.disabled===t}else if("label"in e){return e.disabled===t}return false}}function ye(a){return ue(function(o){o=+o;return ue(function(e,t){var i,n=a([],e.length,o),r=n.length;while(r--){if(e[i=n[r]]){e[i]=!(t[i]=e[i])}}})})}function me(e){return e&&typeof e.getElementsByTagName!=="undefined"&&e}d=se.support={};r=se.isXML=function(e){var t=e.namespaceURI,i=(e.ownerDocument||e).documentElement;return!J.test(t||i&&i.nodeName||"HTML")};_=se.setDocument=function(e){var t,i,n=e?e.ownerDocument||e:c;if(n==P||n.nodeType!==9||!n.documentElement){return P}P=n;a=P.documentElement;C=!r(P);if(c!=P&&(i=P.defaultView)&&i.top!==i){if(i.addEventListener){i.addEventListener("unload",oe,false)}else if(i.attachEvent){i.attachEvent("onunload",oe)}}d.scope=fe(function(e){a.appendChild(e).appendChild(P.createElement("div"));return typeof e.querySelectorAll!=="undefined"&&!e.querySelectorAll(":scope fieldset div").length});d.attributes=fe(function(e){e.className="i";return!e.getAttribute("className")});d.getElementsByTagName=fe(function(e){e.appendChild(P.createComment(""));return!e.getElementsByTagName("*").length});d.getElementsByClassName=Z.test(P.getElementsByClassName);d.getById=fe(function(e){a.appendChild(e).id=x;return!P.getElementsByName||!P.getElementsByName(x).length});if(d.getById){b.filter["ID"]=function(e){var t=e.replace(te,ie);return function(e){return e.getAttribute("id")===t}};b.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&C){var i=t.getElementById(e);return i?[i]:[]}}}else{b.filter["ID"]=function(e){var i=e.replace(te,ie);return function(e){var t=typeof e.getAttributeNode!=="undefined"&&e.getAttributeNode("id");return t&&t.value===i}};b.find["ID"]=function(e,t){if(typeof t.getElementById!=="undefined"&&C){var i,n,r,o=t.getElementById(e);if(o){i=o.getAttributeNode("id");if(i&&i.value===e){return[o]}r=t.getElementsByName(e);n=0;while(o=r[n++]){i=o.getAttributeNode("id");if(i&&i.value===e){return[o]}}}return[]}}}b.find["TAG"]=d.getElementsByTagName?function(e,t){if(typeof t.getElementsByTagName!=="undefined"){return t.getElementsByTagName(e)}else if(d.qsa){return t.querySelectorAll(e)}}:function(e,t){var i,n=[],r=0,o=t.getElementsByTagName(e);if(e==="*"){while(i=o[r++]){if(i.nodeType===1){n.push(i)}}return n}return o};b.find["CLASS"]=d.getElementsByClassName&&function(e,t){if(typeof t.getElementsByClassName!=="undefined"&&C){return t.getElementsByClassName(e)}};s=[];y=[];if(d.qsa=Z.test(P.querySelectorAll)){fe(function(e){var t;a.appendChild(e).innerHTML="<a id='"+x+"'></a>"+"<select id='"+x+"-\r\\' msallowcapture=''>"+"<option selected=''></option></select>";if(e.querySelectorAll("[msallowcapture^='']").length){y.push("[*^$]="+B+"*(?:''|\"\")")}if(!e.querySelectorAll("[selected]").length){y.push("\\["+B+"*(?:value|"+F+")")}if(!e.querySelectorAll("[id~="+x+"-]").length){y.push("~=")}t=P.createElement("input");t.setAttribute("name","");e.appendChild(t);if(!e.querySelectorAll("[name='']").length){y.push("\\["+B+"*name"+B+"*="+B+"*(?:''|\"\")")}if(!e.querySelectorAll(":checked").length){y.push(":checked")}if(!e.querySelectorAll("a#"+x+"+*").length){y.push(".#.+[+~]")}e.querySelectorAll("\\\f");y.push("[\\r\\n\\f]")});fe(function(e){e.innerHTML="<a href='' disabled='disabled'></a>"+"<select disabled='disabled'><option/></select>";var t=P.createElement("input");t.setAttribute("type","hidden");e.appendChild(t).setAttribute("name","D");if(e.querySelectorAll("[name=d]").length){y.push("name"+B+"*[*^$|!~]?=")}if(e.querySelectorAll(":enabled").length!==2){y.push(":enabled",":disabled")}a.appendChild(e).disabled=true;if(e.querySelectorAll(":disabled").length!==2){y.push(":enabled",":disabled")}e.querySelectorAll("*,:x");y.push(",.*:")})}if(d.matchesSelector=Z.test(f=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector)){fe(function(e){d.disconnectedMatch=f.call(e,"*");f.call(e,"[s!='']:x");s.push("!=",N)})}y=y.length&&new RegExp(y.join("|"));s=s.length&&new RegExp(s.join("|"));t=Z.test(a.compareDocumentPosition);m=t||Z.test(a.contains)?function(e,t){var i=e.nodeType===9?e.documentElement:e,n=t&&t.parentNode;return e===n||!!(n&&n.nodeType===1&&(i.contains?i.contains(n):e.compareDocumentPosition&&e.compareDocumentPosition(n)&16))}:function(e,t){if(t){while(t=t.parentNode){if(t===e){return true}}}return false};M=t?function(e,t){if(e===t){u=true;return 0}var i=!e.compareDocumentPosition-!t.compareDocumentPosition;if(i){return i}i=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1;if(i&1||!d.sortDetached&&t.compareDocumentPosition(e)===i){if(e==P||e.ownerDocument==c&&m(c,e)){return-1}if(t==P||t.ownerDocument==c&&m(c,t)){return 1}return l?O(l,e)-O(l,t):0}return i&4?-1:1}:function(e,t){if(e===t){u=true;return 0}var i,n=0,r=e.parentNode,o=t.parentNode,a=[e],s=[t];if(!r||!o){return e==P?-1:t==P?1:r?-1:o?1:l?O(l,e)-O(l,t):0}else if(r===o){return ce(e,t)}i=e;while(i=i.parentNode){a.unshift(i)}i=t;while(i=i.parentNode){s.unshift(i)}while(a[n]===s[n]){n++}return n?ce(a[n],s[n]):a[n]==c?-1:s[n]==c?1:0};return P};se.matches=function(e,t){return se(e,null,null,t)};se.matchesSelector=function(e,t){_(e);if(d.matchesSelector&&C&&!D[t+" "]&&(!s||!s.test(t))&&(!y||!y.test(t))){try{var i=f.call(e,t);if(i||d.disconnectedMatch||e.document&&e.document.nodeType!==11){return i}}catch(e){D(t,true)}}return se(t,P,null,[e]).length>0};se.contains=function(e,t){if((e.ownerDocument||e)!=P){_(e)}return m(e,t)};se.attr=function(e,t){if((e.ownerDocument||e)!=P){_(e)}var i=b.attrHandle[t.toLowerCase()],n=i&&R.call(b.attrHandle,t.toLowerCase())?i(e,t,!C):undefined;return n!==undefined?n:d.attributes||!C?e.getAttribute(t):(n=e.getAttributeNode(t))&&n.specified?n.value:null};se.escape=function(e){return(e+"").replace(ne,re)};se.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)};se.uniqueSort=function(e){var t,i=[],n=0,r=0;u=!d.detectDuplicates;l=!d.sortStable&&e.slice(0);e.sort(M);if(u){while(t=e[r++]){if(t===e[r]){n=i.push(r)}}while(n--){e.splice(i[n],1)}}l=null;return e};o=se.getText=function(e){var t,i="",n=0,r=e.nodeType;if(!r){while(t=e[n++]){i+=o(t)}}else if(r===1||r===9||r===11){if(typeof e.textContent==="string"){return e.textContent}else{for(e=e.firstChild;e;e=e.nextSibling){i+=o(e)}}}else if(r===3||r===4){return e.nodeValue}return i};b=se.selectors={cacheLength:50,createPseudo:ue,match:X,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:true}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:true},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){e[1]=e[1].replace(te,ie);e[3]=(e[3]||e[4]||e[5]||"").replace(te,ie);if(e[2]==="~="){e[3]=" "+e[3]+" "}return e.slice(0,4)},CHILD:function(e){e[1]=e[1].toLowerCase();if(e[1].slice(0,3)==="nth"){if(!e[3]){se.error(e[0])}e[4]=+(e[4]?e[5]+(e[6]||1):2*(e[3]==="even"||e[3]==="odd"));e[5]=+(e[7]+e[8]||e[3]==="odd")}else if(e[3]){se.error(e[0])}return e},PSEUDO:function(e){var t,i=!e[6]&&e[2];if(X["CHILD"].test(e[0])){return null}if(e[3]){e[2]=e[4]||e[5]||""}else if(i&&V.test(i)&&(t=p(i,true))&&(t=i.indexOf(")",i.length-t)-i.length)){e[0]=e[0].slice(0,t);e[2]=i.slice(0,t)}return e.slice(0,3)}},filter:{TAG:function(e){var t=e.replace(te,ie).toLowerCase();return e==="*"?function(){return true}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=g[e+" "];return t||(t=new RegExp("(^|"+B+")"+e+"("+B+"|$)"))&&g(e,function(e){return t.test(typeof e.className==="string"&&e.className||typeof e.getAttribute!=="undefined"&&e.getAttribute("class")||"")})},ATTR:function(i,n,r){return function(e){var t=se.attr(e,i);if(t==null){return n==="!="}if(!n){return true}t+="";return n==="="?t===r:n==="!="?t!==r:n==="^="?r&&t.indexOf(r)===0:n==="*="?r&&t.indexOf(r)>-1:n==="$="?r&&t.slice(-r.length)===r:n==="~="?(" "+t.replace(H," ")+" ").indexOf(r)>-1:n==="|="?t===r||t.slice(0,r.length+1)===r+"-":false}},CHILD:function(p,e,t,v,y){var m=p.slice(0,3)!=="nth",g=p.slice(-4)!=="last",S=e==="of-type";return v===1&&y===0?function(e){return!!e.parentNode}:function(e,t,i){var n,r,o,a,s,l,u=m!==g?"nextSibling":"previousSibling",f=e.parentNode,h=S&&e.nodeName.toLowerCase(),c=!i&&!S,d=false;if(f){if(m){while(u){a=e;while(a=a[u]){if(S?a.nodeName.toLowerCase()===h:a.nodeType===1){return false}}l=u=p==="only"&&!l&&"nextSibling"}return true}l=[g?f.firstChild:f.lastChild];if(g&&c){a=f;o=a[x]||(a[x]={});r=o[a.uniqueID]||(o[a.uniqueID]={});n=r[p]||[];s=n[0]===T&&n[1];d=s&&n[2];a=s&&f.childNodes[s];while(a=++s&&a&&a[u]||(d=s=0)||l.pop()){if(a.nodeType===1&&++d&&a===e){r[p]=[T,s,d];break}}}else{if(c){a=e;o=a[x]||(a[x]={});r=o[a.uniqueID]||(o[a.uniqueID]={});n=r[p]||[];s=n[0]===T&&n[1];d=s}if(d===false){while(a=++s&&a&&a[u]||(d=s=0)||l.pop()){if((S?a.nodeName.toLowerCase()===h:a.nodeType===1)&&++d){if(c){o=a[x]||(a[x]={});r=o[a.uniqueID]||(o[a.uniqueID]={});r[p]=[T,d]}if(a===e){break}}}}}d-=y;return d===v||d%v===0&&d/v>=0}}},PSEUDO:function(e,o){var t,a=b.pseudos[e]||b.setFilters[e.toLowerCase()]||se.error("unsupported pseudo: "+e);if(a[x]){return a(o)}if(a.length>1){t=[e,e,"",o];return b.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,t){var i,n=a(e,o),r=n.length;while(r--){i=O(e,n[r]);e[i]=!(t[i]=n[r])}}):function(e){return a(e,0,t)}}return a}},pseudos:{not:ue(function(e){var n=[],r=[],s=h(e.replace(j,"$1"));return s[x]?ue(function(e,t,i,n){var r,o=s(e,null,n,[]),a=e.length;while(a--){if(r=o[a]){e[a]=!(t[a]=r)}}}):function(e,t,i){n[0]=e;s(n,null,i,r);n[0]=null;return!r.pop()}}),has:ue(function(t){return function(e){return se(t,e).length>0}}),contains:ue(function(t){t=t.replace(te,ie);return function(e){return(e.textContent||o(e)).indexOf(t)>-1}}),lang:ue(function(i){if(!Y.test(i||"")){se.error("unsupported lang: "+i)}i=i.replace(te,ie).toLowerCase();return function(e){var t;do{if(t=C?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang")){t=t.toLowerCase();return t===i||t.indexOf(i+"-")===0}}while((e=e.parentNode)&&e.nodeType===1);return false}}),target:function(e){var t=i.location&&i.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===P.activeElement&&(!P.hasFocus||P.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:ve(false),disabled:ve(true),checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){if(e.parentNode){e.parentNode.selectedIndex}return e.selected===true},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling){if(e.nodeType<6){return false}}return true},parent:function(e){return!b.pseudos["empty"](e)},header:function(e){return $.test(e.nodeName)},input:function(e){return K.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},text:function(e){var t;return e.nodeName.toLowerCase()==="input"&&e.type==="text"&&((t=e.getAttribute("type"))==null||t.toLowerCase()==="text")},first:ye(function(){return[0]}),last:ye(function(e,t){return[t-1]}),eq:ye(function(e,t,i){return[i<0?i+t:i]}),even:ye(function(e,t){var i=0;for(;i<t;i+=2){e.push(i)}return e}),odd:ye(function(e,t){var i=1;for(;i<t;i+=2){e.push(i)}return e}),lt:ye(function(e,t,i){var n=i<0?i+t:i>t?t:i;for(;--n>=0;){e.push(n)}return e}),gt:ye(function(e,t,i){var n=i<0?i+t:i;for(;++n<t;){e.push(n)}return e})}};b.pseudos["nth"]=b.pseudos["eq"];for(e in{radio:true,checkbox:true,file:true,password:true,image:true}){b.pseudos[e]=de(e)}for(e in{submit:true,reset:true}){b.pseudos[e]=pe(e)}function ge(){}ge.prototype=b.filters=b.pseudos;b.setFilters=new ge;p=se.tokenize=function(e,t){var i,n,r,o,a,s,l,u=S[e+" "];if(u){return t?0:u.slice(0)}a=e;s=[];l=b.preFilter;while(a){if(!i||(n=U.exec(a))){if(n){a=a.slice(n[0].length)||a}s.push(r=[])}i=false;if(n=W.exec(a)){i=n.shift();r.push({value:i,type:n[0].replace(j," ")});a=a.slice(i.length)}for(o in b.filter){if((n=X[o].exec(a))&&(!l[o]||(n=l[o](n)))){i=n.shift();r.push({value:i,type:o,matches:n});a=a.slice(i.length)}}if(!i){break}}return t?a.length:a?se.error(e):S(e,s).slice(0)};function Se(e){var t=0,i=e.length,n="";for(;t<i;t++){n+=e[t].value}return n}function be(s,e,t){var l=e.dir,u=e.next,f=u||l,h=t&&f==="parentNode",c=n++;return e.first?function(e,t,i){while(e=e[l]){if(e.nodeType===1||h){return s(e,t,i)}}return false}:function(e,t,i){var n,r,o,a=[T,c];if(i){while(e=e[l]){if(e.nodeType===1||h){if(s(e,t,i)){return true}}}}else{while(e=e[l]){if(e.nodeType===1||h){o=e[x]||(e[x]={});r=o[e.uniqueID]||(o[e.uniqueID]={});if(u&&u===e.nodeName.toLowerCase()){e=e[l]||e}else if((n=r[f])&&n[0]===T&&n[1]===c){return a[2]=n[2]}else{r[f]=a;if(a[2]=s(e,t,i)){return true}}}}}return false}}function we(r){return r.length>1?function(e,t,i){var n=r.length;while(n--){if(!r[n](e,t,i)){return false}}return true}:r[0]}function _e(e,t,i){var n=0,r=t.length;for(;n<r;n++){se(e,t[n],i)}return i}function Pe(e,t,i,n,r){var o,a=[],s=0,l=e.length,u=t!=null;for(;s<l;s++){if(o=e[s]){if(!i||i(o,n,r)){a.push(o);if(u){t.push(s)}}}}return a}function Ce(d,p,v,y,m,e){if(y&&!y[x]){y=Ce(y)}if(m&&!m[x]){m=Ce(m,e)}return ue(function(e,t,i,n){var r,o,a,s=[],l=[],u=t.length,f=e||_e(p||"*",i.nodeType?[i]:i,[]),h=d&&(e||!p)?Pe(f,s,d,i,n):f,c=v?m||(e?d:u||y)?[]:t:h;if(v){v(h,c,i,n)}if(y){r=Pe(c,l);y(r,[],i,n);o=r.length;while(o--){if(a=r[o]){c[l[o]]=!(h[l[o]]=a)}}}if(e){if(m||d){if(m){r=[];o=c.length;while(o--){if(a=c[o]){r.push(h[o]=a)}}m(null,c=[],r,n)}o=c.length;while(o--){if((a=c[o])&&(r=m?O(e,a):s[o])>-1){e[r]=!(t[r]=a)}}}}else{c=Pe(c===t?c.splice(u,c.length):c);if(m){m(null,t,c,n)}else{z.apply(t,c)}}})}function xe(e){var r,t,i,n=e.length,o=b.relative[e[0].type],a=o||b.relative[" "],s=o?1:0,l=be(function(e){return e===r},a,true),u=be(function(e){return O(r,e)>-1},a,true),f=[function(e,t,i){var n=!o&&(i||t!==w)||((r=t).nodeType?l(e,t,i):u(e,t,i));r=null;return n}];for(;s<n;s++){if(t=b.relative[e[s].type]){f=[be(we(f),t)]}else{t=b.filter[e[s].type].apply(null,e[s].matches);if(t[x]){i=++s;for(;i<n;i++){if(b.relative[e[i].type]){break}}return Ce(s>1&&we(f),s>1&&Se(e.slice(0,s-1).concat({value:e[s-2].type===" "?"*":""})).replace(j,"$1"),t,s<i&&xe(e.slice(s,i)),i<n&&xe(e=e.slice(i)),i<n&&Se(e))}f.push(t)}}return we(f)}function Te(y,m){var g=m.length>0,S=y.length>0,e=function(e,t,i,n,r){var o,a,s,l=0,u="0",f=e&&[],h=[],c=w,d=e||S&&b.find["TAG"]("*",r),p=T+=c==null?1:Math.random()||.1,v=d.length;if(r){w=t==P||t||r}for(;u!==v&&(o=d[u])!=null;u++){if(S&&o){a=0;if(!t&&o.ownerDocument!=P){_(o);i=!C}while(s=y[a++]){if(s(o,t||P,i)){n.push(o);break}}if(r){T=p}}if(g){if(o=!s&&o){l--}if(e){f.push(o)}}}l+=u;if(g&&u!==l){a=0;while(s=m[a++]){s(f,h,t,i)}if(e){if(l>0){while(u--){if(!(f[u]||h[u])){h[u]=E.call(n)}}}h=Pe(h)}z.apply(n,h);if(r&&!e&&h.length>0&&l+m.length>1){se.uniqueSort(n)}}if(r){T=p;w=c}return f};return g?ue(e):e}h=se.compile=function(e,t){var i,n=[],r=[],o=k[e+" "];if(!o){if(!t){t=p(e)}i=t.length;while(i--){o=xe(t[i]);if(o[x]){n.push(o)}else{r.push(o)}}o=k(e,Te(r,n));o.selector=e}return o};v=se.select=function(e,t,i,n){var r,o,a,s,l,u=typeof e==="function"&&e,f=!n&&p(e=u.selector||e);i=i||[];if(f.length===1){o=f[0]=f[0].slice(0);if(o.length>2&&(a=o[0]).type==="ID"&&t.nodeType===9&&C&&b.relative[o[1].type]){t=(b.find["ID"](a.matches[0].replace(te,ie),t)||[])[0];if(!t){return i}else if(u){t=t.parentNode}e=e.slice(o.shift().value.length)}r=X["needsContext"].test(e)?0:o.length;while(r--){a=o[r];if(b.relative[s=a.type]){break}if(l=b.find[s]){if(n=l(a.matches[0].replace(te,ie),ee.test(o[0].type)&&me(t.parentNode)||t)){o.splice(r,1);e=n.length&&Se(o);if(!e){z.apply(i,n);return i}break}}}}(u||h(e,f))(n,t,!C,i,!t||ee.test(e)&&me(t.parentNode)||t);return i};d.sortStable=x.split("").sort(M).join("")===x;d.detectDuplicates=!!u;_();d.sortDetached=fe(function(e){return e.compareDocumentPosition(P.createElement("fieldset"))&1});if(!fe(function(e){e.innerHTML="<a href='#'></a>";return e.firstChild.getAttribute("href")==="#"})){he("type|href|height|width",function(e,t,i){if(!i){return e.getAttribute(t,t.toLowerCase()==="type"?1:2)}})}if(!d.attributes||!fe(function(e){e.innerHTML="<input/>";e.firstChild.setAttribute("value","");return e.firstChild.getAttribute("value")===""})){he("value",function(e,t,i){if(!i&&e.nodeName.toLowerCase()==="input"){return e.defaultValue}})}if(!fe(function(e){return e.getAttribute("disabled")==null})){he(F,function(e,t,i){var n;if(!i){return e[t]===true?t.toLowerCase():(n=e.getAttributeNode(t))&&n.specified?n.value:null}})}return se}(P);x.find=d;x.expr=d.selectors;x.expr[":"]=x.expr.pseudos;x.uniqueSort=x.unique=d.uniqueSort;x.text=d.getText;x.isXMLDoc=d.isXML;x.contains=d.contains;x.escapeSelector=d.escape;var p=function(e,t,i){var n=[],r=i!==undefined;while((e=e[t])&&e.nodeType!==9){if(e.nodeType===1){if(r&&x(e).is(i)){break}n.push(e)}}return n};var _=function(e,t){var i=[];for(;e;e=e.nextSibling){if(e.nodeType===1&&e!==t){i.push(e)}}return i};var T=x.expr.match.needsContext;function k(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var D=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function M(e,i,n){if(g(i)){return x.grep(e,function(e,t){return!!i.call(e,t,e)!==n})}if(i.nodeType){return x.grep(e,function(e){return e===i!==n})}if(typeof i!=="string"){return x.grep(e,function(e){return r.call(i,e)>-1!==n})}return x.filter(i,e,n)}x.filter=function(e,t,i){var n=t[0];if(i){e=":not("+e+")"}if(t.length===1&&n.nodeType===1){return x.find.matchesSelector(n,e)?[n]:[]}return x.find.matches(e,x.grep(t,function(e){return e.nodeType===1}))};x.fn.extend({find:function(e){var t,i,n=this.length,r=this;if(typeof e!=="string"){return this.pushStack(x(e).filter(function(){for(t=0;t<n;t++){if(x.contains(r[t],this)){return true}}}))}i=this.pushStack([]);for(t=0;t<n;t++){x.find(e,r[t],i)}return n>1?x.uniqueSort(i):i},filter:function(e){return this.pushStack(M(this,e||[],false))},not:function(e){return this.pushStack(M(this,e||[],true))},is:function(e){return!!M(this,typeof e==="string"&&T.test(e)?x(e):e||[],false).length}});var R,E=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,I=x.fn.init=function(e,t,i){var n,r;if(!e){return this}i=i||R;if(typeof e==="string"){if(e[0]==="<"&&e[e.length-1]===">"&&e.length>=3){n=[null,e,null]}else{n=E.exec(e)}if(n&&(n[1]||!t)){if(n[1]){t=t instanceof x?t[0]:t;x.merge(this,x.parseHTML(n[1],t&&t.nodeType?t.ownerDocument||t:C,true));if(D.test(n[1])&&x.isPlainObject(t)){for(n in t){if(g(this[n])){this[n](t[n])}else{this.attr(n,t[n])}}}return this}else{r=C.getElementById(n[2]);if(r){this[0]=r;this.length=1}return this}}else if(!t||t.jquery){return(t||i).find(e)}else{return this.constructor(t).find(e)}}else if(e.nodeType){this[0]=e;this.length=1;return this}else if(g(e)){return i.ready!==undefined?i.ready(e):e(x)}return x.makeArray(e,this)};I.prototype=x.fn;R=x(C);var z=/^(?:parents|prev(?:Until|All))/,A={children:true,contents:true,next:true,prev:true};x.fn.extend({has:function(e){var t=x(e,this),i=t.length;return this.filter(function(){var e=0;for(;e<i;e++){if(x.contains(this,t[e])){return true}}})},closest:function(e,t){var i,n=0,r=this.length,o=[],a=typeof e!=="string"&&x(e);if(!T.test(e)){for(;n<r;n++){for(i=this[n];i&&i!==t;i=i.parentNode){if(i.nodeType<11&&(a?a.index(i)>-1:i.nodeType===1&&x.find.matchesSelector(i,e))){o.push(i);break}}}}return this.pushStack(o.length>1?x.uniqueSort(o):o)},index:function(e){if(!e){return this[0]&&this[0].parentNode?this.first().prevAll().length:-1}if(typeof e==="string"){return r.call(x(e),this[0])}return r.call(this,e.jquery?e[0]:e)},add:function(e,t){return this.pushStack(x.uniqueSort(x.merge(this.get(),x(e,t))))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}});function O(e,t){while((e=e[t])&&e.nodeType!==1){}return e}x.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return p(e,"parentNode")},parentsUntil:function(e,t,i){return p(e,"parentNode",i)},next:function(e){return O(e,"nextSibling")},prev:function(e){return O(e,"previousSibling")},nextAll:function(e){return p(e,"nextSibling")},prevAll:function(e){return p(e,"previousSibling")},nextUntil:function(e,t,i){return p(e,"nextSibling",i)},prevUntil:function(e,t,i){return p(e,"previousSibling",i)},siblings:function(e){return _((e.parentNode||{}).firstChild,e)},children:function(e){return _(e.firstChild)},contents:function(e){if(e.contentDocument!=null&&n(e.contentDocument)){return e.contentDocument}if(k(e,"template")){e=e.content||e}return x.merge([],e.childNodes)}},function(n,r){x.fn[n]=function(e,t){var i=x.map(this,r,e);if(n.slice(-5)!=="Until"){t=e}if(t&&typeof t==="string"){i=x.filter(t,i)}if(this.length>1){if(!A[n]){x.uniqueSort(i)}if(z.test(n)){i.reverse()}}return this.pushStack(i)}});var F=/[^\x20\t\r\n\f]+/g;function B(e){var i={};x.each(e.match(F)||[],function(e,t){i[t]=true});return i}x.Callbacks=function(n){n=typeof n==="string"?B(n):x.extend({},n);var i,e,t,r,o=[],a=[],s=-1,l=function(){r=r||n.once;t=i=true;for(;a.length;s=-1){e=a.shift();while(++s<o.length){if(o[s].apply(e[0],e[1])===false&&n.stopOnFalse){s=o.length;e=false}}}if(!n.memory){e=false}i=false;if(r){if(e){o=[]}else{o=""}}},u={add:function(){if(o){if(e&&!i){s=o.length-1;a.push(e)}(function i(e){x.each(e,function(e,t){if(g(t)){if(!n.unique||!u.has(t)){o.push(t)}}else if(t&&t.length&&w(t)!=="string"){i(t)}})})(arguments);if(e&&!i){l()}}return this},remove:function(){x.each(arguments,function(e,t){var i;while((i=x.inArray(t,o,i))>-1){o.splice(i,1);if(i<=s){s--}}});return this},has:function(e){return e?x.inArray(e,o)>-1:o.length>0},empty:function(){if(o){o=[]}return this},disable:function(){r=a=[];o=e="";return this},disabled:function(){return!o},lock:function(){r=a=[];if(!e&&!i){o=e=""}return this},locked:function(){return!!r},fireWith:function(e,t){if(!r){t=t||[];t=[e,t.slice?t.slice():t];a.push(t);if(!i){l()}}return this},fire:function(){u.fireWith(this,arguments);return this},fired:function(){return!!t}};return u};function L(e){return e}function q(e){throw e}function N(e,t,i,n){var r;try{if(e&&g(r=e.promise)){r.call(e).done(t).fail(i)}else if(e&&g(r=e.then)){r.call(e,t,i)}else{t.apply(undefined,[e].slice(n))}}catch(e){i.apply(undefined,[e])}}x.extend({Deferred:function(e){var o=[["notify","progress",x.Callbacks("memory"),x.Callbacks("memory"),2],["resolve","done",x.Callbacks("once memory"),x.Callbacks("once memory"),0,"resolved"],["reject","fail",x.Callbacks("once memory"),x.Callbacks("once memory"),1,"rejected"]],r="pending",a={state:function(){return r},always:function(){s.done(arguments).fail(arguments);return this},catch:function(e){return a.then(null,e)},pipe:function(){var r=arguments;return x.Deferred(function(n){x.each(o,function(e,t){var i=g(r[t[4]])&&r[t[4]];s[t[1]](function(){var e=i&&i.apply(this,arguments);if(e&&g(e.promise)){e.promise().progress(n.notify).done(n.resolve).fail(n.reject)}else{n[t[0]+"With"](this,i?[e]:arguments)}})});r=null}).promise()},then:function(t,i,n){var l=0;function u(r,o,a,s){return function(){var i=this,n=arguments,e=function(){var e,t;if(r<l){return}e=a.apply(i,n);if(e===o.promise()){throw new TypeError("Thenable self-resolution")}t=e&&(typeof e==="object"||typeof e==="function")&&e.then;if(g(t)){if(s){t.call(e,u(l,o,L,s),u(l,o,q,s))}else{l++;t.call(e,u(l,o,L,s),u(l,o,q,s),u(l,o,L,o.notifyWith))}}else{if(a!==L){i=undefined;n=[e]}(s||o.resolveWith)(i,n)}},t=s?e:function(){try{e()}catch(e){if(x.Deferred.exceptionHook){x.Deferred.exceptionHook(e,t.stackTrace)}if(r+1>=l){if(a!==q){i=undefined;n=[e]}o.rejectWith(i,n)}}};if(r){t()}else{if(x.Deferred.getStackHook){t.stackTrace=x.Deferred.getStackHook()}P.setTimeout(t)}}}return x.Deferred(function(e){o[0][3].add(u(0,e,g(n)?n:L,e.notifyWith));o[1][3].add(u(0,e,g(t)?t:L));o[2][3].add(u(0,e,g(i)?i:q))}).promise()},promise:function(e){return e!=null?x.extend(e,a):a}},s={};x.each(o,function(e,t){var i=t[2],n=t[5];a[t[1]]=i.add;if(n){i.add(function(){r=n},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock)}i.add(t[3].fire);s[t[0]]=function(){s[t[0]+"With"](this===s?undefined:this,arguments);return this};s[t[0]+"With"]=i.fireWith});a.promise(s);if(e){e.call(s,s)}return s},when:function(e){var i=arguments.length,t=i,n=Array(t),r=s.call(arguments),o=x.Deferred(),a=function(t){return function(e){n[t]=this;r[t]=arguments.length>1?s.call(arguments):e;if(!--i){o.resolveWith(n,r)}}};if(i<=1){N(e,o.done(a(t)).resolve,o.reject,!i);if(o.state()==="pending"||g(r[t]&&r[t].then)){return o.then()}}while(t--){N(r[t],a(t),o.reject)}return o.promise()}});var H=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;x.Deferred.exceptionHook=function(e,t){if(P.console&&P.console.warn&&e&&H.test(e.name)){P.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)}};x.readyException=function(e){P.setTimeout(function(){throw e})};var j=x.Deferred();x.fn.ready=function(e){j.then(e).catch(function(e){x.readyException(e)});return this};x.extend({isReady:false,readyWait:1,ready:function(e){if(e===true?--x.readyWait:x.isReady){return}x.isReady=true;if(e!==true&&--x.readyWait>0){return}j.resolveWith(C,[x])}});x.ready.then=j.then;function U(){C.removeEventListener("DOMContentLoaded",U);P.removeEventListener("load",U);x.ready()}if(C.readyState==="complete"||C.readyState!=="loading"&&!C.documentElement.doScroll){P.setTimeout(x.ready)}else{C.addEventListener("DOMContentLoaded",U);P.addEventListener("load",U)}var W=function(e,t,i,n,r,o,a){var s=0,l=e.length,u=i==null;if(w(i)==="object"){r=true;for(s in i){W(e,t,s,i[s],true,o,a)}}else if(n!==undefined){r=true;if(!g(n)){a=true}if(u){if(a){t.call(e,n);t=null}else{u=t;t=function(e,t,i){return u.call(x(e),i)}}}if(t){for(;s<l;s++){t(e[s],i,a?n:n.call(e[s],s,t(e[s],i)))}}}if(r){return e}if(u){return t.call(e)}return l?t(e[0],i):o};var G=/^-ms-/,V=/-([a-z])/g;function Y(e,t){return t.toUpperCase()}function X(e){return e.replace(G,"ms-").replace(V,Y)}var J=function(e){return e.nodeType===1||e.nodeType===9||!+e.nodeType};function K(){this.expando=x.expando+K.uid++}K.uid=1;K.prototype={cache:function(e){var t=e[this.expando];if(!t){t=Object.create(null);if(J(e)){if(e.nodeType){e[this.expando]=t}else{Object.defineProperty(e,this.expando,{value:t,configurable:true})}}}return t},set:function(e,t,i){var n,r=this.cache(e);if(typeof t==="string"){r[X(t)]=i}else{for(n in t){r[X(n)]=t[n]}}return r},get:function(e,t){return t===undefined?this.cache(e):e[this.expando]&&e[this.expando][X(t)]},access:function(e,t,i){if(t===undefined||t&&typeof t==="string"&&i===undefined){return this.get(e,t)}this.set(e,t,i);return i!==undefined?i:t},remove:function(e,t){var i,n=e[this.expando];if(n===undefined){return}if(t!==undefined){if(Array.isArray(t)){t=t.map(X)}else{t=X(t);t=t in n?[t]:t.match(F)||[]}i=t.length;while(i--){delete n[t[i]]}}if(t===undefined||x.isEmptyObject(n)){if(e.nodeType){e[this.expando]=undefined}else{delete e[this.expando]}}},hasData:function(e){var t=e[this.expando];return t!==undefined&&!x.isEmptyObject(t)}};var $=new K;var Z=new K;var Q=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ee=/[A-Z]/g;function te(e){if(e==="true"){return true}if(e==="false"){return false}if(e==="null"){return null}if(e===+e+""){return+e}if(Q.test(e)){return JSON.parse(e)}return e}function ie(e,t,i){var n;if(i===undefined&&e.nodeType===1){n="data-"+t.replace(ee,"-$&").toLowerCase();i=e.getAttribute(n);if(typeof i==="string"){try{i=te(i)}catch(e){}Z.set(e,t,i)}else{i=undefined}}return i}x.extend({hasData:function(e){return Z.hasData(e)||$.hasData(e)},data:function(e,t,i){return Z.access(e,t,i)},removeData:function(e,t){Z.remove(e,t)},_data:function(e,t,i){return $.access(e,t,i)},_removeData:function(e,t){$.remove(e,t)}});x.fn.extend({data:function(i,e){var t,n,r,o=this[0],a=o&&o.attributes;if(i===undefined){if(this.length){r=Z.get(o);if(o.nodeType===1&&!$.get(o,"hasDataAttrs")){t=a.length;while(t--){if(a[t]){n=a[t].name;if(n.indexOf("data-")===0){n=X(n.slice(5));ie(o,n,r[n])}}}$.set(o,"hasDataAttrs",true)}}return r}if(typeof i==="object"){return this.each(function(){Z.set(this,i)})}return W(this,function(e){var t;if(o&&e===undefined){t=Z.get(o,i);if(t!==undefined){return t}t=ie(o,i);if(t!==undefined){return t}return}this.each(function(){Z.set(this,i,e)})},null,e,arguments.length>1,null,true)},removeData:function(e){return this.each(function(){Z.remove(this,e)})}});x.extend({queue:function(e,t,i){var n;if(e){t=(t||"fx")+"queue";n=$.get(e,t);if(i){if(!n||Array.isArray(i)){n=$.access(e,t,x.makeArray(i))}else{n.push(i)}}return n||[]}},dequeue:function(e,t){t=t||"fx";var i=x.queue(e,t),n=i.length,r=i.shift(),o=x._queueHooks(e,t),a=function(){x.dequeue(e,t)};if(r==="inprogress"){r=i.shift();n--}if(r){if(t==="fx"){i.unshift("inprogress")}delete o.stop;r.call(e,a,o)}if(!n&&o){o.empty.fire()}},_queueHooks:function(e,t){var i=t+"queueHooks";return $.get(e,i)||$.access(e,i,{empty:x.Callbacks("once memory").add(function(){$.remove(e,[t+"queue",i])})})}});x.fn.extend({queue:function(t,i){var e=2;if(typeof t!=="string"){i=t;t="fx";e--}if(arguments.length<e){return x.queue(this[0],t)}return i===undefined?this:this.each(function(){var e=x.queue(this,t,i);x._queueHooks(this,t);if(t==="fx"&&e[0]!=="inprogress"){x.dequeue(this,t)}})},dequeue:function(e){return this.each(function(){x.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var i,n=1,r=x.Deferred(),o=this,a=this.length,s=function(){if(!--n){r.resolveWith(o,[o])}};if(typeof e!=="string"){t=e;e=undefined}e=e||"fx";while(a--){i=$.get(o[a],e+"queueHooks");if(i&&i.empty){n++;i.empty.add(s)}}s();return r.promise(t)}});var ne=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var re=new RegExp("^(?:([+-])=|)("+ne+")([a-z%]*)$","i");var oe=["Top","Right","Bottom","Left"];var ae=C.documentElement;var se=function(e){return x.contains(e.ownerDocument,e)},le={composed:true};if(ae.getRootNode){se=function(e){return x.contains(e.ownerDocument,e)||e.getRootNode(le)===e.ownerDocument}}var ue=function(e,t){e=t||e;return e.style.display==="none"||e.style.display===""&&se(e)&&x.css(e,"display")==="none"};function fe(e,t,i,n){var r,o,a=20,s=n?function(){return n.cur()}:function(){return x.css(e,t,"")},l=s(),u=i&&i[3]||(x.cssNumber[t]?"":"px"),f=e.nodeType&&(x.cssNumber[t]||u!=="px"&&+l)&&re.exec(x.css(e,t));if(f&&f[3]!==u){l=l/2;u=u||f[3];f=+l||1;while(a--){x.style(e,t,f+u);if((1-o)*(1-(o=s()/l||.5))<=0){a=0}f=f/o}f=f*2;x.style(e,t,f+u);i=i||[]}if(i){f=+f||+l||0;r=i[1]?f+(i[1]+1)*i[2]:+i[2];if(n){n.unit=u;n.start=f;n.end=r}}return r}var he={};function ce(e){var t,i=e.ownerDocument,n=e.nodeName,r=he[n];if(r){return r}t=i.body.appendChild(i.createElement(n));r=x.css(t,"display");t.parentNode.removeChild(t);if(r==="none"){r="block"}he[n]=r;return r}function de(e,t){var i,n,r=[],o=0,a=e.length;for(;o<a;o++){n=e[o];if(!n.style){continue}i=n.style.display;if(t){if(i==="none"){r[o]=$.get(n,"display")||null;if(!r[o]){n.style.display=""}}if(n.style.display===""&&ue(n)){r[o]=ce(n)}}else{if(i!=="none"){r[o]="none";$.set(n,"display",i)}}}for(o=0;o<a;o++){if(r[o]!=null){e[o].style.display=r[o]}}return e}x.fn.extend({show:function(){return de(this,true)},hide:function(){return de(this)},toggle:function(e){if(typeof e==="boolean"){return e?this.show():this.hide()}return this.each(function(){if(ue(this)){x(this).show()}else{x(this).hide()}})}});var pe=/^(?:checkbox|radio)$/i;var ve=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i;var ye=/^$|^module$|\/(?:java|ecma)script/i;(function(){var e=C.createDocumentFragment(),t=e.appendChild(C.createElement("div")),i=C.createElement("input");i.setAttribute("type","radio");i.setAttribute("checked","checked");i.setAttribute("name","t");t.appendChild(i);m.checkClone=t.cloneNode(true).cloneNode(true).lastChild.checked;t.innerHTML="<textarea>x</textarea>";m.noCloneChecked=!!t.cloneNode(true).lastChild.defaultValue;t.innerHTML="<option></option>";m.option=!!t.lastChild})();var me={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};me.tbody=me.tfoot=me.colgroup=me.caption=me.thead;me.th=me.td;if(!m.option){me.optgroup=me.option=[1,"<select multiple='multiple'>","</select>"]}function ge(e,t){var i;if(typeof e.getElementsByTagName!=="undefined"){i=e.getElementsByTagName(t||"*")}else if(typeof e.querySelectorAll!=="undefined"){i=e.querySelectorAll(t||"*")}else{i=[]}if(t===undefined||t&&k(e,t)){return x.merge([e],i)}return i}function Se(e,t){var i=0,n=e.length;for(;i<n;i++){$.set(e[i],"globalEval",!t||$.get(t[i],"globalEval"))}}var be=/<|&#?\w+;/;function we(e,t,i,n,r){var o,a,s,l,u,f,h=t.createDocumentFragment(),c=[],d=0,p=e.length;for(;d<p;d++){o=e[d];if(o||o===0){if(w(o)==="object"){x.merge(c,o.nodeType?[o]:o)}else if(!be.test(o)){c.push(t.createTextNode(o))}else{a=a||h.appendChild(t.createElement("div"));s=(ve.exec(o)||["",""])[1].toLowerCase();l=me[s]||me._default;a.innerHTML=l[1]+x.htmlPrefilter(o)+l[2];f=l[0];while(f--){a=a.lastChild}x.merge(c,a.childNodes);a=h.firstChild;a.textContent=""}}}h.textContent="";d=0;while(o=c[d++]){if(n&&x.inArray(o,n)>-1){if(r){r.push(o)}continue}u=se(o);a=ge(h.appendChild(o),"script");if(u){Se(a)}if(i){f=0;while(o=a[f++]){if(ye.test(o.type||"")){i.push(o)}}}}return h}var _e=/^key/,Pe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Ce=/^([^.]*)(?:\.(.+)|)/;function xe(){return true}function Te(){return false}function ke(e,t){return e===De()===(t==="focus")}function De(){try{return C.activeElement}catch(e){}}function Me(e,t,i,n,r,o){var a,s;if(typeof t==="object"){if(typeof i!=="string"){n=n||i;i=undefined}for(s in t){Me(e,s,i,n,t[s],o)}return e}if(n==null&&r==null){r=i;n=i=undefined}else if(r==null){if(typeof i==="string"){r=n;n=undefined}else{r=n;n=i;i=undefined}}if(r===false){r=Te}else if(!r){return e}if(o===1){a=r;r=function(e){x().off(e);return a.apply(this,arguments)};r.guid=a.guid||(a.guid=x.guid++)}return e.each(function(){x.event.add(this,t,r,n,i)})}x.event={global:{},add:function(t,e,i,n,r){var o,a,s,l,u,f,h,c,d,p,v,y=$.get(t);if(!J(t)){return}if(i.handler){o=i;i=o.handler;r=o.selector}if(r){x.find.matchesSelector(ae,r)}if(!i.guid){i.guid=x.guid++}if(!(l=y.events)){l=y.events=Object.create(null)}if(!(a=y.handle)){a=y.handle=function(e){return typeof x!=="undefined"&&x.event.triggered!==e.type?x.event.dispatch.apply(t,arguments):undefined}}e=(e||"").match(F)||[""];u=e.length;while(u--){s=Ce.exec(e[u])||[];d=v=s[1];p=(s[2]||"").split(".").sort();if(!d){continue}h=x.event.special[d]||{};d=(r?h.delegateType:h.bindType)||d;h=x.event.special[d]||{};f=x.extend({type:d,origType:v,data:n,handler:i,guid:i.guid,selector:r,needsContext:r&&x.expr.match.needsContext.test(r),namespace:p.join(".")},o);if(!(c=l[d])){c=l[d]=[];c.delegateCount=0;if(!h.setup||h.setup.call(t,n,p,a)===false){if(t.addEventListener){t.addEventListener(d,a)}}}if(h.add){h.add.call(t,f);if(!f.handler.guid){f.handler.guid=i.guid}}if(r){c.splice(c.delegateCount++,0,f)}else{c.push(f)}x.event.global[d]=true}},remove:function(e,t,i,n,r){var o,a,s,l,u,f,h,c,d,p,v,y=$.hasData(e)&&$.get(e);if(!y||!(l=y.events)){return}t=(t||"").match(F)||[""];u=t.length;while(u--){s=Ce.exec(t[u])||[];d=v=s[1];p=(s[2]||"").split(".").sort();if(!d){for(d in l){x.event.remove(e,d+t[u],i,n,true)}continue}h=x.event.special[d]||{};d=(n?h.delegateType:h.bindType)||d;c=l[d]||[];s=s[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)");a=o=c.length;while(o--){f=c[o];if((r||v===f.origType)&&(!i||i.guid===f.guid)&&(!s||s.test(f.namespace))&&(!n||n===f.selector||n==="**"&&f.selector)){c.splice(o,1);if(f.selector){c.delegateCount--}if(h.remove){h.remove.call(e,f)}}}if(a&&!c.length){if(!h.teardown||h.teardown.call(e,p,y.handle)===false){x.removeEvent(e,d,y.handle)}delete l[d]}}if(x.isEmptyObject(l)){$.remove(e,"handle events")}},dispatch:function(e){var t,i,n,r,o,a,s=new Array(arguments.length),l=x.event.fix(e),u=($.get(this,"events")||Object.create(null))[l.type]||[],f=x.event.special[l.type]||{};s[0]=l;for(t=1;t<arguments.length;t++){s[t]=arguments[t]}l.delegateTarget=this;if(f.preDispatch&&f.preDispatch.call(this,l)===false){return}a=x.event.handlers.call(this,l,u);t=0;while((r=a[t++])&&!l.isPropagationStopped()){l.currentTarget=r.elem;i=0;while((o=r.handlers[i++])&&!l.isImmediatePropagationStopped()){if(!l.rnamespace||o.namespace===false||l.rnamespace.test(o.namespace)){l.handleObj=o;l.data=o.data;n=((x.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,s);if(n!==undefined){if((l.result=n)===false){l.preventDefault();l.stopPropagation()}}}}}if(f.postDispatch){f.postDispatch.call(this,l)}return l.result},handlers:function(e,t){var i,n,r,o,a,s=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!(e.type==="click"&&e.button>=1)){for(;u!==this;u=u.parentNode||this){if(u.nodeType===1&&!(e.type==="click"&&u.disabled===true)){o=[];a={};for(i=0;i<l;i++){n=t[i];r=n.selector+" ";if(a[r]===undefined){a[r]=n.needsContext?x(r,this).index(u)>-1:x.find(r,this,null,[u]).length}if(a[r]){o.push(n)}}if(o.length){s.push({elem:u,handlers:o})}}}}u=this;if(l<t.length){s.push({elem:u,handlers:t.slice(l)})}return s},addProp:function(t,e){Object.defineProperty(x.Event.prototype,t,{enumerable:true,configurable:true,get:g(e)?function(){if(this.originalEvent){return e(this.originalEvent)}}:function(){if(this.originalEvent){return this.originalEvent[t]}},set:function(e){Object.defineProperty(this,t,{enumerable:true,configurable:true,writable:true,value:e})}})},fix:function(e){return e[x.expando]?e:new x.Event(e)},special:{load:{noBubble:true},click:{setup:function(e){var t=this||e;if(pe.test(t.type)&&t.click&&k(t,"input")){Re(t,"click",xe)}return false},trigger:function(e){var t=this||e;if(pe.test(t.type)&&t.click&&k(t,"input")){Re(t,"click")}return true},_default:function(e){var t=e.target;return pe.test(t.type)&&t.click&&k(t,"input")&&$.get(t,"click")||k(t,"a")}},beforeunload:{postDispatch:function(e){if(e.result!==undefined&&e.originalEvent){e.originalEvent.returnValue=e.result}}}}};function Re(e,r,o){if(!o){if($.get(e,r)===undefined){x.event.add(e,r,xe)}return}$.set(e,r,false);x.event.add(e,r,{namespace:false,handler:function(e){var t,i,n=$.get(this,r);if(e.isTrigger&1&&this[r]){if(!n.length){n=s.call(arguments);$.set(this,r,n);t=o(this,r);this[r]();i=$.get(this,r);if(n!==i||t){$.set(this,r,false)}else{i={}}if(n!==i){e.stopImmediatePropagation();e.preventDefault();return i.value}}else if((x.event.special[r]||{}).delegateType){e.stopPropagation()}}else if(n.length){$.set(this,r,{value:x.event.trigger(x.extend(n[0],x.Event.prototype),n.slice(1),this)});e.stopImmediatePropagation()}}})}x.removeEvent=function(e,t,i){if(e.removeEventListener){e.removeEventListener(t,i)}};x.Event=function(e,t){if(!(this instanceof x.Event)){return new x.Event(e,t)}if(e&&e.type){this.originalEvent=e;this.type=e.type;this.isDefaultPrevented=e.defaultPrevented||e.defaultPrevented===undefined&&e.returnValue===false?xe:Te;this.target=e.target&&e.target.nodeType===3?e.target.parentNode:e.target;this.currentTarget=e.currentTarget;this.relatedTarget=e.relatedTarget}else{this.type=e}if(t){x.extend(this,t)}this.timeStamp=e&&e.timeStamp||Date.now();this[x.expando]=true};x.Event.prototype={constructor:x.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:false,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=xe;if(e&&!this.isSimulated){e.preventDefault()}},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=xe;if(e&&!this.isSimulated){e.stopPropagation()}},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=xe;if(e&&!this.isSimulated){e.stopImmediatePropagation()}this.stopPropagation()}};x.each({altKey:true,bubbles:true,cancelable:true,changedTouches:true,ctrlKey:true,detail:true,eventPhase:true,metaKey:true,pageX:true,pageY:true,shiftKey:true,view:true,char:true,code:true,charCode:true,key:true,keyCode:true,button:true,buttons:true,clientX:true,clientY:true,offsetX:true,offsetY:true,pointerId:true,pointerType:true,screenX:true,screenY:true,targetTouches:true,toElement:true,touches:true,which:function(e){var t=e.button;if(e.which==null&&_e.test(e.type)){return e.charCode!=null?e.charCode:e.keyCode}if(!e.which&&t!==undefined&&Pe.test(e.type)){if(t&1){return 1}if(t&2){return 3}if(t&4){return 2}return 0}return e.which}},x.event.addProp);x.each({focus:"focusin",blur:"focusout"},function(e,t){x.event.special[e]={setup:function(){Re(this,e,ke);return false},trigger:function(){Re(this,e);return true},delegateType:t}});x.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,o){x.event.special[e]={delegateType:o,bindType:o,handle:function(e){var t,i=this,n=e.relatedTarget,r=e.handleObj;if(!n||n!==i&&!x.contains(i,n)){e.type=r.origType;t=r.handler.apply(this,arguments);e.type=o}return t}}});x.fn.extend({on:function(e,t,i,n){return Me(this,e,t,i,n)},one:function(e,t,i,n){return Me(this,e,t,i,n,1)},off:function(e,t,i){var n,r;if(e&&e.preventDefault&&e.handleObj){n=e.handleObj;x(e.delegateTarget).off(n.namespace?n.origType+"."+n.namespace:n.origType,n.selector,n.handler);return this}if(typeof e==="object"){for(r in e){this.off(r,t,e[r])}return this}if(t===false||typeof t==="function"){i=t;t=undefined}if(i===false){i=Te}return this.each(function(){x.event.remove(this,e,i,t)})}});var Ee=/<script|<style|<link/i,Ie=/checked\s*(?:[^=]|=\s*.checked.)/i,ze=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function Ae(e,t){if(k(e,"table")&&k(t.nodeType!==11?t:t.firstChild,"tr")){return x(e).children("tbody")[0]||e}return e}function Oe(e){e.type=(e.getAttribute("type")!==null)+"/"+e.type;return e}function Fe(e){if((e.type||"").slice(0,5)==="true/"){e.type=e.type.slice(5)}else{e.removeAttribute("type")}return e}function Be(e,t){var i,n,r,o,a,s,l;if(t.nodeType!==1){return}if($.hasData(e)){o=$.get(e);l=o.events;if(l){$.remove(t,"handle events");for(r in l){for(i=0,n=l[r].length;i<n;i++){x.event.add(t,r,l[r][i])}}}}if(Z.hasData(e)){a=Z.access(e);s=x.extend({},a);Z.set(t,s)}}function Le(e,t){var i=t.nodeName.toLowerCase();if(i==="input"&&pe.test(e.type)){t.checked=e.checked}else if(i==="input"||i==="textarea"){t.defaultValue=e.defaultValue}}function qe(i,n,r,o){n=v(n);var e,t,a,s,l,u,f=0,h=i.length,c=h-1,d=n[0],p=g(d);if(p||h>1&&typeof d==="string"&&!m.checkClone&&Ie.test(d)){return i.each(function(e){var t=i.eq(e);if(p){n[0]=d.call(this,e,t.html())}qe(t,n,r,o)})}if(h){e=we(n,i[0].ownerDocument,false,i,o);t=e.firstChild;if(e.childNodes.length===1){e=t}if(t||o){a=x.map(ge(e,"script"),Oe);s=a.length;for(;f<h;f++){l=e;if(f!==c){l=x.clone(l,true,true);if(s){x.merge(a,ge(l,"script"))}}r.call(i[f],l,f)}if(s){u=a[a.length-1].ownerDocument;x.map(a,Fe);for(f=0;f<s;f++){l=a[f];if(ye.test(l.type||"")&&!$.access(l,"globalEval")&&x.contains(u,l)){if(l.src&&(l.type||"").toLowerCase()!=="module"){if(x._evalUrl&&!l.noModule){x._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u)}}else{b(l.textContent.replace(ze,""),l,u)}}}}}}return i}function Ne(e,t,i){var n,r=t?x.filter(t,e):e,o=0;for(;(n=r[o])!=null;o++){if(!i&&n.nodeType===1){x.cleanData(ge(n))}if(n.parentNode){if(i&&se(n)){Se(ge(n,"script"))}n.parentNode.removeChild(n)}}return e}x.extend({htmlPrefilter:function(e){return e},clone:function(e,t,i){var n,r,o,a,s=e.cloneNode(true),l=se(e);if(!m.noCloneChecked&&(e.nodeType===1||e.nodeType===11)&&!x.isXMLDoc(e)){a=ge(s);o=ge(e);for(n=0,r=o.length;n<r;n++){Le(o[n],a[n])}}if(t){if(i){o=o||ge(e);a=a||ge(s);for(n=0,r=o.length;n<r;n++){Be(o[n],a[n])}}else{Be(e,s)}}a=ge(s,"script");if(a.length>0){Se(a,!l&&ge(e,"script"))}return s},cleanData:function(e){var t,i,n,r=x.event.special,o=0;for(;(i=e[o])!==undefined;o++){if(J(i)){if(t=i[$.expando]){if(t.events){for(n in t.events){if(r[n]){x.event.remove(i,n)}else{x.removeEvent(i,n,t.handle)}}}i[$.expando]=undefined}if(i[Z.expando]){i[Z.expando]=undefined}}}}});x.fn.extend({detach:function(e){return Ne(this,e,true)},remove:function(e){return Ne(this,e)},text:function(e){return W(this,function(e){return e===undefined?x.text(this):this.empty().each(function(){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){this.textContent=e}})},null,e,arguments.length)},append:function(){return qe(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Ae(this,e);t.appendChild(e)}})},prepend:function(){return qe(this,arguments,function(e){if(this.nodeType===1||this.nodeType===11||this.nodeType===9){var t=Ae(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return qe(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this)}})},after:function(){return qe(this,arguments,function(e){if(this.parentNode){this.parentNode.insertBefore(e,this.nextSibling)}})},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){if(e.nodeType===1){x.cleanData(ge(e,false));e.textContent=""}}return this},clone:function(e,t){e=e==null?false:e;t=t==null?e:t;return this.map(function(){return x.clone(this,e,t)})},html:function(e){return W(this,function(e){var t=this[0]||{},i=0,n=this.length;if(e===undefined&&t.nodeType===1){return t.innerHTML}if(typeof e==="string"&&!Ee.test(e)&&!me[(ve.exec(e)||["",""])[1].toLowerCase()]){e=x.htmlPrefilter(e);try{for(;i<n;i++){t=this[i]||{};if(t.nodeType===1){x.cleanData(ge(t,false));t.innerHTML=e}}t=0}catch(e){}}if(t){this.empty().append(e)}},null,e,arguments.length)},replaceWith:function(){var i=[];return qe(this,arguments,function(e){var t=this.parentNode;if(x.inArray(this,i)<0){x.cleanData(ge(this));if(t){t.replaceChild(e,this)}}},i)}});x.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,a){x.fn[e]=function(e){var t,i=[],n=x(e),r=n.length-1,o=0;for(;o<=r;o++){t=o===r?this:this.clone(true);x(n[o])[a](t);l.apply(i,t.get())}return this.pushStack(i)}});var He=new RegExp("^("+ne+")(?!px)[a-z%]+$","i");var je=function(e){var t=e.ownerDocument.defaultView;if(!t||!t.opener){t=P}return t.getComputedStyle(e)};var Ue=function(e,t,i){var n,r,o={};for(r in t){o[r]=e.style[r];e.style[r]=t[r]}n=i.call(e);for(r in t){e.style[r]=o[r]}return n};var We=new RegExp(oe.join("|"),"i");(function(){function e(){if(!u){return}l.style.cssText="position:absolute;left:-11111px;width:60px;"+"margin-top:1px;padding:0;border:0";u.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;"+"margin:auto;border:1px;padding:1px;"+"width:60%;top:1%";ae.appendChild(l).appendChild(u);var e=P.getComputedStyle(u);i=e.top!=="1%";s=t(e.marginLeft)===12;u.style.right="60%";o=t(e.right)===36;n=t(e.width)===36;u.style.position="absolute";r=t(u.offsetWidth/3)===12;ae.removeChild(l);u=null}function t(e){return Math.round(parseFloat(e))}var i,n,r,o,a,s,l=C.createElement("div"),u=C.createElement("div");if(!u.style){return}u.style.backgroundClip="content-box";u.cloneNode(true).style.backgroundClip="";m.clearCloneStyle=u.style.backgroundClip==="content-box";x.extend(m,{boxSizingReliable:function(){e();return n},pixelBoxStyles:function(){e();return o},pixelPosition:function(){e();return i},reliableMarginLeft:function(){e();return s},scrollboxSize:function(){e();return r},reliableTrDimensions:function(){var e,t,i,n;if(a==null){e=C.createElement("table");t=C.createElement("tr");i=C.createElement("div");e.style.cssText="position:absolute;left:-11111px";t.style.height="1px";i.style.height="9px";ae.appendChild(e).appendChild(t).appendChild(i);n=P.getComputedStyle(t);a=parseInt(n.height)>3;ae.removeChild(e)}return a}})})();function Ge(e,t,i){var n,r,o,a,s=e.style;i=i||je(e);if(i){a=i.getPropertyValue(t)||i[t];if(a===""&&!se(e)){a=x.style(e,t)}if(!m.pixelBoxStyles()&&He.test(a)&&We.test(t)){n=s.width;r=s.minWidth;o=s.maxWidth;s.minWidth=s.maxWidth=s.width=a;a=i.width;s.width=n;s.minWidth=r;s.maxWidth=o}}return a!==undefined?a+"":a}function Ve(e,t){return{get:function(){if(e()){delete this.get;return}return(this.get=t).apply(this,arguments)}}}var Ye=["Webkit","Moz","ms"],Xe=C.createElement("div").style,Je={};function Ke(e){var t=e[0].toUpperCase()+e.slice(1),i=Ye.length;while(i--){e=Ye[i]+t;if(e in Xe){return e}}}function $e(e){var t=x.cssProps[e]||Je[e];if(t){return t}if(e in Xe){return e}return Je[e]=Ke(e)||e}var Ze=/^(none|table(?!-c[ea]).+)/,Qe=/^--/,et={position:"absolute",visibility:"hidden",display:"block"},tt={letterSpacing:"0",fontWeight:"400"};function it(e,t,i){var n=re.exec(t);return n?Math.max(0,n[2]-(i||0))+(n[3]||"px"):t}function nt(e,t,i,n,r,o){var a=t==="width"?1:0,s=0,l=0;if(i===(n?"border":"content")){return 0}for(;a<4;a+=2){if(i==="margin"){l+=x.css(e,i+oe[a],true,r)}if(!n){l+=x.css(e,"padding"+oe[a],true,r);if(i!=="padding"){l+=x.css(e,"border"+oe[a]+"Width",true,r)}else{s+=x.css(e,"border"+oe[a]+"Width",true,r)}}else{if(i==="content"){l-=x.css(e,"padding"+oe[a],true,r)}if(i!=="margin"){l-=x.css(e,"border"+oe[a]+"Width",true,r)}}}if(!n&&o>=0){l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-s-.5))||0}return l}function rt(e,t,i){var n=je(e),r=!m.boxSizingReliable()||i,o=r&&x.css(e,"boxSizing",false,n)==="border-box",a=o,s=Ge(e,t,n),l="offset"+t[0].toUpperCase()+t.slice(1);if(He.test(s)){if(!i){return s}s="auto"}if((!m.boxSizingReliable()&&o||!m.reliableTrDimensions()&&k(e,"tr")||s==="auto"||!parseFloat(s)&&x.css(e,"display",false,n)==="inline")&&e.getClientRects().length){o=x.css(e,"boxSizing",false,n)==="border-box";a=l in e;if(a){s=e[l]}}s=parseFloat(s)||0;return s+nt(e,t,i||(o?"border":"content"),a,n,s)+"px"}x.extend({cssHooks:{opacity:{get:function(e,t){if(t){var i=Ge(e,"opacity");return i===""?"1":i}}}},cssNumber:{animationIterationCount:true,columnCount:true,fillOpacity:true,flexGrow:true,flexShrink:true,fontWeight:true,gridArea:true,gridColumn:true,gridColumnEnd:true,gridColumnStart:true,gridRow:true,gridRowEnd:true,gridRowStart:true,lineHeight:true,opacity:true,order:true,orphans:true,widows:true,zIndex:true,zoom:true},cssProps:{},style:function(e,t,i,n){if(!e||e.nodeType===3||e.nodeType===8||!e.style){return}var r,o,a,s=X(t),l=Qe.test(t),u=e.style;if(!l){t=$e(s)}a=x.cssHooks[t]||x.cssHooks[s];if(i!==undefined){o=typeof i;if(o==="string"&&(r=re.exec(i))&&r[1]){i=fe(e,t,r);o="number"}if(i==null||i!==i){return}if(o==="number"&&!l){i+=r&&r[3]||(x.cssNumber[s]?"":"px")}if(!m.clearCloneStyle&&i===""&&t.indexOf("background")===0){u[t]="inherit"}if(!a||!("set"in a)||(i=a.set(e,i,n))!==undefined){if(l){u.setProperty(t,i)}else{u[t]=i}}}else{if(a&&"get"in a&&(r=a.get(e,false,n))!==undefined){return r}return u[t]}},css:function(e,t,i,n){var r,o,a,s=X(t),l=Qe.test(t);if(!l){t=$e(s)}a=x.cssHooks[t]||x.cssHooks[s];if(a&&"get"in a){r=a.get(e,true,i)}if(r===undefined){r=Ge(e,t,n)}if(r==="normal"&&t in tt){r=tt[t]}if(i===""||i){o=parseFloat(r);return i===true||isFinite(o)?o||0:r}return r}});x.each(["height","width"],function(e,u){x.cssHooks[u]={get:function(e,t,i){if(t){return Ze.test(x.css(e,"display"))&&(!e.getClientRects().length||!e.getBoundingClientRect().width)?Ue(e,et,function(){return rt(e,u,i)}):rt(e,u,i)}},set:function(e,t,i){var n,r=je(e),o=!m.scrollboxSize()&&r.position==="absolute",a=o||i,s=a&&x.css(e,"boxSizing",false,r)==="border-box",l=i?nt(e,u,i,s,r):0;if(s&&o){l-=Math.ceil(e["offset"+u[0].toUpperCase()+u.slice(1)]-parseFloat(r[u])-nt(e,u,"border",false,r)-.5)}if(l&&(n=re.exec(t))&&(n[3]||"px")!=="px"){e.style[u]=t;t=x.css(e,u)}return it(e,t,l)}}});x.cssHooks.marginLeft=Ve(m.reliableMarginLeft,function(e,t){if(t){return(parseFloat(Ge(e,"marginLeft"))||e.getBoundingClientRect().left-Ue(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}});x.each({margin:"",padding:"",border:"Width"},function(r,o){x.cssHooks[r+o]={expand:function(e){var t=0,i={},n=typeof e==="string"?e.split(" "):[e];for(;t<4;t++){i[r+oe[t]+o]=n[t]||n[t-2]||n[0]}return i}};if(r!=="margin"){x.cssHooks[r+o].set=it}});x.fn.extend({css:function(e,t){return W(this,function(e,t,i){var n,r,o={},a=0;if(Array.isArray(t)){n=je(e);r=t.length;for(;a<r;a++){o[t[a]]=x.css(e,t[a],false,n)}return o}return i!==undefined?x.style(e,t,i):x.css(e,t)},e,t,arguments.length>1)}});function ot(e,t,i,n,r){return new ot.prototype.init(e,t,i,n,r)}x.Tween=ot;ot.prototype={constructor:ot,init:function(e,t,i,n,r,o){this.elem=e;this.prop=i;this.easing=r||x.easing._default;this.options=t;this.start=this.now=this.cur();this.end=n;this.unit=o||(x.cssNumber[i]?"":"px")},cur:function(){var e=ot.propHooks[this.prop];return e&&e.get?e.get(this):ot.propHooks._default.get(this)},run:function(e){var t,i=ot.propHooks[this.prop];if(this.options.duration){this.pos=t=x.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration)}else{this.pos=t=e}this.now=(this.end-this.start)*t+this.start;if(this.options.step){this.options.step.call(this.elem,this.now,this)}if(i&&i.set){i.set(this)}else{ot.propHooks._default.set(this)}return this}};ot.prototype.init.prototype=ot.prototype;ot.propHooks={_default:{get:function(e){var t;if(e.elem.nodeType!==1||e.elem[e.prop]!=null&&e.elem.style[e.prop]==null){return e.elem[e.prop]}t=x.css(e.elem,e.prop,"");return!t||t==="auto"?0:t},set:function(e){if(x.fx.step[e.prop]){x.fx.step[e.prop](e)}else if(e.elem.nodeType===1&&(x.cssHooks[e.prop]||e.elem.style[$e(e.prop)]!=null)){x.style(e.elem,e.prop,e.now+e.unit)}else{e.elem[e.prop]=e.now}}}};ot.propHooks.scrollTop=ot.propHooks.scrollLeft={set:function(e){if(e.elem.nodeType&&e.elem.parentNode){e.elem[e.prop]=e.now}}};x.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"};x.fx=ot.prototype.init;x.fx.step={};var at,st,lt=/^(?:toggle|show|hide)$/,ut=/queueHooks$/;function ft(){if(st){if(C.hidden===false&&P.requestAnimationFrame){P.requestAnimationFrame(ft)}else{P.setTimeout(ft,x.fx.interval)}x.fx.tick()}}function ht(){P.setTimeout(function(){at=undefined});return at=Date.now()}function ct(e,t){var i,n=0,r={height:e};t=t?1:0;for(;n<4;n+=2-t){i=oe[n];r["margin"+i]=r["padding"+i]=e}if(t){r.opacity=r.width=e}return r}function dt(e,t,i){var n,r=(yt.tweeners[t]||[]).concat(yt.tweeners["*"]),o=0,a=r.length;for(;o<a;o++){if(n=r[o].call(i,t,e)){return n}}}function pt(e,t,i){var n,r,o,a,s,l,u,f,h="width"in t||"height"in t,c=this,d={},p=e.style,v=e.nodeType&&ue(e),y=$.get(e,"fxshow");if(!i.queue){a=x._queueHooks(e,"fx");if(a.unqueued==null){a.unqueued=0;s=a.empty.fire;a.empty.fire=function(){if(!a.unqueued){s()}}}a.unqueued++;c.always(function(){c.always(function(){a.unqueued--;if(!x.queue(e,"fx").length){a.empty.fire()}})})}for(n in t){r=t[n];if(lt.test(r)){delete t[n];o=o||r==="toggle";if(r===(v?"hide":"show")){if(r==="show"&&y&&y[n]!==undefined){v=true}else{continue}}d[n]=y&&y[n]||x.style(e,n)}}l=!x.isEmptyObject(t);if(!l&&x.isEmptyObject(d)){return}if(h&&e.nodeType===1){i.overflow=[p.overflow,p.overflowX,p.overflowY];u=y&&y.display;if(u==null){u=$.get(e,"display")}f=x.css(e,"display");if(f==="none"){if(u){f=u}else{de([e],true);u=e.style.display||u;f=x.css(e,"display");de([e])}}if(f==="inline"||f==="inline-block"&&u!=null){if(x.css(e,"float")==="none"){if(!l){c.done(function(){p.display=u});if(u==null){f=p.display;u=f==="none"?"":f}}p.display="inline-block"}}}if(i.overflow){p.overflow="hidden";c.always(function(){p.overflow=i.overflow[0];p.overflowX=i.overflow[1];p.overflowY=i.overflow[2]})}l=false;for(n in d){if(!l){if(y){if("hidden"in y){v=y.hidden}}else{y=$.access(e,"fxshow",{display:u})}if(o){y.hidden=!v}if(v){de([e],true)}c.done(function(){if(!v){de([e])}$.remove(e,"fxshow");for(n in d){x.style(e,n,d[n])}})}l=dt(v?y[n]:0,n,c);if(!(n in y)){y[n]=l.start;if(v){l.end=l.start;l.start=0}}}}function vt(e,t){var i,n,r,o,a;for(i in e){n=X(i);r=t[n];o=e[i];if(Array.isArray(o)){r=o[1];o=e[i]=o[0]}if(i!==n){e[n]=o;delete e[i]}a=x.cssHooks[n];if(a&&"expand"in a){o=a.expand(o);delete e[n];for(i in o){if(!(i in e)){e[i]=o[i];t[i]=r}}}else{t[n]=r}}}function yt(a,e,t){var i,s,n=0,r=yt.prefilters.length,l=x.Deferred().always(function(){delete o.elem}),o=function(){if(s){return false}var e=at||ht(),t=Math.max(0,u.startTime+u.duration-e),i=t/u.duration||0,n=1-i,r=0,o=u.tweens.length;for(;r<o;r++){u.tweens[r].run(n)}l.notifyWith(a,[u,n,t]);if(n<1&&o){return t}if(!o){l.notifyWith(a,[u,1,0])}l.resolveWith(a,[u]);return false},u=l.promise({elem:a,props:x.extend({},e),opts:x.extend(true,{specialEasing:{},easing:x.easing._default},t),originalProperties:e,originalOptions:t,startTime:at||ht(),duration:t.duration,tweens:[],createTween:function(e,t){var i=x.Tween(a,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);u.tweens.push(i);return i},stop:function(e){var t=0,i=e?u.tweens.length:0;if(s){return this}s=true;for(;t<i;t++){u.tweens[t].run(1)}if(e){l.notifyWith(a,[u,1,0]);l.resolveWith(a,[u,e])}else{l.rejectWith(a,[u,e])}return this}}),f=u.props;vt(f,u.opts.specialEasing);for(;n<r;n++){i=yt.prefilters[n].call(u,a,f,u.opts);if(i){if(g(i.stop)){x._queueHooks(u.elem,u.opts.queue).stop=i.stop.bind(i)}return i}}x.map(f,dt,u);if(g(u.opts.start)){u.opts.start.call(a,u)}u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always);x.fx.timer(x.extend(o,{elem:a,anim:u,queue:u.opts.queue}));return u}x.Animation=x.extend(yt,{tweeners:{"*":[function(e,t){var i=this.createTween(e,t);fe(i.elem,e,re.exec(t),i);return i}]},tweener:function(e,t){if(g(e)){t=e;e=["*"]}else{e=e.match(F)}var i,n=0,r=e.length;for(;n<r;n++){i=e[n];yt.tweeners[i]=yt.tweeners[i]||[];yt.tweeners[i].unshift(t)}},prefilters:[pt],prefilter:function(e,t){if(t){yt.prefilters.unshift(e)}else{yt.prefilters.push(e)}}});x.speed=function(e,t,i){var n=e&&typeof e==="object"?x.extend({},e):{complete:i||!i&&t||g(e)&&e,duration:e,easing:i&&t||t&&!g(t)&&t};if(x.fx.off){n.duration=0}else{if(typeof n.duration!=="number"){if(n.duration in x.fx.speeds){n.duration=x.fx.speeds[n.duration]}else{n.duration=x.fx.speeds._default}}}if(n.queue==null||n.queue===true){n.queue="fx"}n.old=n.complete;n.complete=function(){if(g(n.old)){n.old.call(this)}if(n.queue){x.dequeue(this,n.queue)}};return n};x.fn.extend({fadeTo:function(e,t,i,n){return this.filter(ue).css("opacity",0).show().end().animate({opacity:t},e,i,n)},animate:function(t,e,i,n){var r=x.isEmptyObject(t),o=x.speed(e,i,n),a=function(){var e=yt(this,x.extend({},t),o);if(r||$.get(this,"finish")){e.stop(true)}};a.finish=a;return r||o.queue===false?this.each(a):this.queue(o.queue,a)},stop:function(r,e,o){var a=function(e){var t=e.stop;delete e.stop;t(o)};if(typeof r!=="string"){o=e;e=r;r=undefined}if(e){this.queue(r||"fx",[])}return this.each(function(){var e=true,t=r!=null&&r+"queueHooks",i=x.timers,n=$.get(this);if(t){if(n[t]&&n[t].stop){a(n[t])}}else{for(t in n){if(n[t]&&n[t].stop&&ut.test(t)){a(n[t])}}}for(t=i.length;t--;){if(i[t].elem===this&&(r==null||i[t].queue===r)){i[t].anim.stop(o);e=false;i.splice(t,1)}}if(e||!o){x.dequeue(this,r)}})},finish:function(a){if(a!==false){a=a||"fx"}return this.each(function(){var e,t=$.get(this),i=t[a+"queue"],n=t[a+"queueHooks"],r=x.timers,o=i?i.length:0;t.finish=true;x.queue(this,a,[]);if(n&&n.stop){n.stop.call(this,true)}for(e=r.length;e--;){if(r[e].elem===this&&r[e].queue===a){r[e].anim.stop(true);r.splice(e,1)}}for(e=0;e<o;e++){if(i[e]&&i[e].finish){i[e].finish.call(this)}}delete t.finish})}});x.each(["toggle","show","hide"],function(e,n){var r=x.fn[n];x.fn[n]=function(e,t,i){return e==null||typeof e==="boolean"?r.apply(this,arguments):this.animate(ct(n,true),e,t,i)}});x.each({slideDown:ct("show"),slideUp:ct("hide"),slideToggle:ct("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,n){x.fn[e]=function(e,t,i){return this.animate(n,e,t,i)}});x.timers=[];x.fx.tick=function(){var e,t=0,i=x.timers;at=Date.now();for(;t<i.length;t++){e=i[t];if(!e()&&i[t]===e){i.splice(t--,1)}}if(!i.length){x.fx.stop()}at=undefined};x.fx.timer=function(e){x.timers.push(e);x.fx.start()};x.fx.interval=13;x.fx.start=function(){if(st){return}st=true;ft()};x.fx.stop=function(){st=null};x.fx.speeds={slow:600,fast:200,_default:400};x.fn.delay=function(n,e){n=x.fx?x.fx.speeds[n]||n:n;e=e||"fx";return this.queue(e,function(e,t){var i=P.setTimeout(e,n);t.stop=function(){P.clearTimeout(i)}})};(function(){var e=C.createElement("input"),t=C.createElement("select"),i=t.appendChild(C.createElement("option"));e.type="checkbox";m.checkOn=e.value!=="";m.optSelected=i.selected;e=C.createElement("input");e.value="t";e.type="radio";m.radioValue=e.value==="t"})();var mt,gt=x.expr.attrHandle;x.fn.extend({attr:function(e,t){return W(this,x.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){x.removeAttr(this,e)})}});x.extend({attr:function(e,t,i){var n,r,o=e.nodeType;if(o===3||o===8||o===2){return}if(typeof e.getAttribute==="undefined"){return x.prop(e,t,i)}if(o!==1||!x.isXMLDoc(e)){r=x.attrHooks[t.toLowerCase()]||(x.expr.match.bool.test(t)?mt:undefined)}if(i!==undefined){if(i===null){x.removeAttr(e,t);return}if(r&&"set"in r&&(n=r.set(e,i,t))!==undefined){return n}e.setAttribute(t,i+"");return i}if(r&&"get"in r&&(n=r.get(e,t))!==null){return n}n=x.find.attr(e,t);return n==null?undefined:n},attrHooks:{type:{set:function(e,t){if(!m.radioValue&&t==="radio"&&k(e,"input")){var i=e.value;e.setAttribute("type",t);if(i){e.value=i}return t}}}},removeAttr:function(e,t){var i,n=0,r=t&&t.match(F);if(r&&e.nodeType===1){while(i=r[n++]){e.removeAttribute(i)}}}});mt={set:function(e,t,i){if(t===false){x.removeAttr(e,i)}else{e.setAttribute(i,i)}return i}};x.each(x.expr.match.bool.source.match(/\w+/g),function(e,t){var a=gt[t]||x.find.attr;gt[t]=function(e,t,i){var n,r,o=t.toLowerCase();if(!i){r=gt[o];gt[o]=n;n=a(e,t,i)!=null?o:null;gt[o]=r}return n}});var St=/^(?:input|select|textarea|button)$/i,bt=/^(?:a|area)$/i;x.fn.extend({prop:function(e,t){return W(this,x.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[x.propFix[e]||e]})}});x.extend({prop:function(e,t,i){var n,r,o=e.nodeType;if(o===3||o===8||o===2){return}if(o!==1||!x.isXMLDoc(e)){t=x.propFix[t]||t;r=x.propHooks[t]}if(i!==undefined){if(r&&"set"in r&&(n=r.set(e,i,t))!==undefined){return n}return e[t]=i}if(r&&"get"in r&&(n=r.get(e,t))!==null){return n}return e[t]},propHooks:{tabIndex:{get:function(e){var t=x.find.attr(e,"tabindex");if(t){return parseInt(t,10)}if(St.test(e.nodeName)||bt.test(e.nodeName)&&e.href){return 0}return-1}}},propFix:{for:"htmlFor",class:"className"}});if(!m.optSelected){x.propHooks.selected={get:function(e){var t=e.parentNode;if(t&&t.parentNode){t.parentNode.selectedIndex}return null},set:function(e){var t=e.parentNode;if(t){t.selectedIndex;if(t.parentNode){t.parentNode.selectedIndex}}}}}x.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){x.propFix[this.toLowerCase()]=this});function wt(e){var t=e.match(F)||[];return t.join(" ")}function _t(e){return e.getAttribute&&e.getAttribute("class")||""}function Pt(e){if(Array.isArray(e)){return e}if(typeof e==="string"){return e.match(F)||[]}return[]}x.fn.extend({addClass:function(t){var e,i,n,r,o,a,s,l=0;if(g(t)){return this.each(function(e){x(this).addClass(t.call(this,e,_t(this)))})}e=Pt(t);if(e.length){while(i=this[l++]){r=_t(i);n=i.nodeType===1&&" "+wt(r)+" ";if(n){a=0;while(o=e[a++]){if(n.indexOf(" "+o+" ")<0){n+=o+" "}}s=wt(n);if(r!==s){i.setAttribute("class",s)}}}}return this},removeClass:function(t){var e,i,n,r,o,a,s,l=0;if(g(t)){return this.each(function(e){x(this).removeClass(t.call(this,e,_t(this)))})}if(!arguments.length){return this.attr("class","")}e=Pt(t);if(e.length){while(i=this[l++]){r=_t(i);n=i.nodeType===1&&" "+wt(r)+" ";if(n){a=0;while(o=e[a++]){while(n.indexOf(" "+o+" ")>-1){n=n.replace(" "+o+" "," ")}}s=wt(n);if(r!==s){i.setAttribute("class",s)}}}}return this},toggleClass:function(r,t){var o=typeof r,a=o==="string"||Array.isArray(r);if(typeof t==="boolean"&&a){return t?this.addClass(r):this.removeClass(r)}if(g(r)){return this.each(function(e){x(this).toggleClass(r.call(this,e,_t(this),t),t)})}return this.each(function(){var e,t,i,n;if(a){t=0;i=x(this);n=Pt(r);while(e=n[t++]){if(i.hasClass(e)){i.removeClass(e)}else{i.addClass(e)}}}else if(r===undefined||o==="boolean"){e=_t(this);if(e){$.set(this,"__className__",e)}if(this.setAttribute){this.setAttribute("class",e||r===false?"":$.get(this,"__className__")||"")}}})},hasClass:function(e){var t,i,n=0;t=" "+e+" ";while(i=this[n++]){if(i.nodeType===1&&(" "+wt(_t(i))+" ").indexOf(t)>-1){return true}}return false}});var Ct=/\r/g;x.fn.extend({val:function(i){var n,e,r,t=this[0];if(!arguments.length){if(t){n=x.valHooks[t.type]||x.valHooks[t.nodeName.toLowerCase()];if(n&&"get"in n&&(e=n.get(t,"value"))!==undefined){return e}e=t.value;if(typeof e==="string"){return e.replace(Ct,"")}return e==null?"":e}return}r=g(i);return this.each(function(e){var t;if(this.nodeType!==1){return}if(r){t=i.call(this,e,x(this).val())}else{t=i}if(t==null){t=""}else if(typeof t==="number"){t+=""}else if(Array.isArray(t)){t=x.map(t,function(e){return e==null?"":e+""})}n=x.valHooks[this.type]||x.valHooks[this.nodeName.toLowerCase()];if(!n||!("set"in n)||n.set(this,t,"value")===undefined){this.value=t}})}});x.extend({valHooks:{option:{get:function(e){var t=x.find.attr(e,"value");return t!=null?t:wt(x.text(e))}},select:{get:function(e){var t,i,n,r=e.options,o=e.selectedIndex,a=e.type==="select-one",s=a?null:[],l=a?o+1:r.length;if(o<0){n=l}else{n=a?o:0}for(;n<l;n++){i=r[n];if((i.selected||n===o)&&!i.disabled&&(!i.parentNode.disabled||!k(i.parentNode,"optgroup"))){t=x(i).val();if(a){return t}s.push(t)}}return s},set:function(e,t){var i,n,r=e.options,o=x.makeArray(t),a=r.length;while(a--){n=r[a];if(n.selected=x.inArray(x.valHooks.option.get(n),o)>-1){i=true}}if(!i){e.selectedIndex=-1}return o}}}});x.each(["radio","checkbox"],function(){x.valHooks[this]={set:function(e,t){if(Array.isArray(t)){return e.checked=x.inArray(x(e).val(),t)>-1}}};if(!m.checkOn){x.valHooks[this].get=function(e){return e.getAttribute("value")===null?"on":e.value}}});m.focusin="onfocusin"in P;var xt=/^(?:focusinfocus|focusoutblur)$/,Tt=function(e){e.stopPropagation()};x.extend(x.event,{trigger:function(e,t,i,n){var r,o,a,s,l,u,f,h,c=[i||C],d=y.call(e,"type")?e.type:e,p=y.call(e,"namespace")?e.namespace.split("."):[];o=h=a=i=i||C;if(i.nodeType===3||i.nodeType===8){return}if(xt.test(d+x.event.triggered)){return}if(d.indexOf(".")>-1){p=d.split(".");d=p.shift();p.sort()}l=d.indexOf(":")<0&&"on"+d;e=e[x.expando]?e:new x.Event(d,typeof e==="object"&&e);e.isTrigger=n?2:3;e.namespace=p.join(".");e.rnamespace=e.namespace?new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"):null;e.result=undefined;if(!e.target){e.target=i}t=t==null?[e]:x.makeArray(t,[e]);f=x.event.special[d]||{};if(!n&&f.trigger&&f.trigger.apply(i,t)===false){return}if(!n&&!f.noBubble&&!S(i)){s=f.delegateType||d;if(!xt.test(s+d)){o=o.parentNode}for(;o;o=o.parentNode){c.push(o);a=o}if(a===(i.ownerDocument||C)){c.push(a.defaultView||a.parentWindow||P)}}r=0;while((o=c[r++])&&!e.isPropagationStopped()){h=o;e.type=r>1?s:f.bindType||d;u=($.get(o,"events")||Object.create(null))[e.type]&&$.get(o,"handle");if(u){u.apply(o,t)}u=l&&o[l];if(u&&u.apply&&J(o)){e.result=u.apply(o,t);if(e.result===false){e.preventDefault()}}}e.type=d;if(!n&&!e.isDefaultPrevented()){if((!f._default||f._default.apply(c.pop(),t)===false)&&J(i)){if(l&&g(i[d])&&!S(i)){a=i[l];if(a){i[l]=null}x.event.triggered=d;if(e.isPropagationStopped()){h.addEventListener(d,Tt)}i[d]();if(e.isPropagationStopped()){h.removeEventListener(d,Tt)}x.event.triggered=undefined;if(a){i[l]=a}}}}return e.result},simulate:function(e,t,i){var n=x.extend(new x.Event,i,{type:e,isSimulated:true});x.event.trigger(n,null,t)}});x.fn.extend({trigger:function(e,t){return this.each(function(){x.event.trigger(e,t,this)})},triggerHandler:function(e,t){var i=this[0];if(i){return x.event.trigger(e,t,i,true)}}});if(!m.focusin){x.each({focus:"focusin",blur:"focusout"},function(i,n){var r=function(e){x.event.simulate(n,e.target,x.event.fix(e))};x.event.special[n]={setup:function(){var e=this.ownerDocument||this.document||this,t=$.access(e,n);if(!t){e.addEventListener(i,r,true)}$.access(e,n,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=$.access(e,n)-1;if(!t){e.removeEventListener(i,r,true);$.remove(e,n)}else{$.access(e,n,t)}}}})}var kt=P.location;var Dt={guid:Date.now()};var Mt=/\?/;x.parseXML=function(e){var t;if(!e||typeof e!=="string"){return null}try{t=(new P.DOMParser).parseFromString(e,"text/xml")}catch(e){t=undefined}if(!t||t.getElementsByTagName("parsererror").length){x.error("Invalid XML: "+e)}return t};var Rt=/\[\]$/,Et=/\r?\n/g,It=/^(?:submit|button|image|reset|file)$/i,zt=/^(?:input|select|textarea|keygen)/i;function At(i,e,n,r){var t;if(Array.isArray(e)){x.each(e,function(e,t){if(n||Rt.test(i)){r(i,t)}else{At(i+"["+(typeof t==="object"&&t!=null?e:"")+"]",t,n,r)}})}else if(!n&&w(e)==="object"){for(t in e){At(i+"["+t+"]",e[t],n,r)}}else{r(i,e)}}x.param=function(e,t){var i,n=[],r=function(e,t){var i=g(t)?t():t;n[n.length]=encodeURIComponent(e)+"="+encodeURIComponent(i==null?"":i)};if(e==null){return""}if(Array.isArray(e)||e.jquery&&!x.isPlainObject(e)){x.each(e,function(){r(this.name,this.value)})}else{for(i in e){At(i,e[i],t,r)}}return n.join("&")};x.fn.extend({serialize:function(){return x.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=x.prop(this,"elements");return e?x.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!x(this).is(":disabled")&&zt.test(this.nodeName)&&!It.test(e)&&(this.checked||!pe.test(e))}).map(function(e,t){var i=x(this).val();if(i==null){return null}if(Array.isArray(i)){return x.map(i,function(e){return{name:t.name,value:e.replace(Et,"\r\n")}})}return{name:t.name,value:i.replace(Et,"\r\n")}}).get()}});var Ot=/%20/g,Ft=/#.*$/,Bt=/([?&])_=[^&]*/,Lt=/^(.*?):[ \t]*([^\r\n]*)$/gm,qt=/^(?:about|app|app-storage|.+-extension|file|res|widget):$/,Nt=/^(?:GET|HEAD)$/,Ht=/^\/\//,jt={},Ut={},Wt="*/".concat("*"),Gt=C.createElement("a");Gt.href=kt.href;function Vt(o){return function(e,t){if(typeof e!=="string"){t=e;e="*"}var i,n=0,r=e.toLowerCase().match(F)||[];if(g(t)){while(i=r[n++]){if(i[0]==="+"){i=i.slice(1)||"*";(o[i]=o[i]||[]).unshift(t)}else{(o[i]=o[i]||[]).push(t)}}}}}function Yt(t,r,o,a){var s={},l=t===Ut;function u(e){var n;s[e]=true;x.each(t[e]||[],function(e,t){var i=t(r,o,a);if(typeof i==="string"&&!l&&!s[i]){r.dataTypes.unshift(i);u(i);return false}else if(l){return!(n=i)}});return n}return u(r.dataTypes[0])||!s["*"]&&u("*")}function Xt(e,t){var i,n,r=x.ajaxSettings.flatOptions||{};for(i in t){if(t[i]!==undefined){(r[i]?e:n||(n={}))[i]=t[i]}}if(n){x.extend(true,e,n)}return e}function Jt(e,t,i){var n,r,o,a,s=e.contents,l=e.dataTypes;while(l[0]==="*"){l.shift();if(n===undefined){n=e.mimeType||t.getResponseHeader("Content-Type")}}if(n){for(r in s){if(s[r]&&s[r].test(n)){l.unshift(r);break}}}if(l[0]in i){o=l[0]}else{for(r in i){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}if(!a){a=r}}o=o||a}if(o){if(o!==l[0]){l.unshift(o)}return i[o]}}function Kt(e,t,i,n){var r,o,a,s,l,u={},f=e.dataTypes.slice();if(f[1]){for(a in e.converters){u[a.toLowerCase()]=e.converters[a]}}o=f.shift();while(o){if(e.responseFields[o]){i[e.responseFields[o]]=t}if(!l&&n&&e.dataFilter){t=e.dataFilter(t,e.dataType)}l=o;o=f.shift();if(o){if(o==="*"){o=l}else if(l!=="*"&&l!==o){a=u[l+" "+o]||u["* "+o];if(!a){for(r in u){s=r.split(" ");if(s[1]===o){a=u[l+" "+s[0]]||u["* "+s[0]];if(a){if(a===true){a=u[r]}else if(u[r]!==true){o=s[0];f.unshift(s[1])}break}}}}if(a!==true){if(a&&e.throws){t=a(t)}else{try{t=a(t)}catch(e){return{state:"parsererror",error:a?e:"No conversion from "+l+" to "+o}}}}}}}return{state:"success",data:t}}x.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:kt.href,type:"GET",isLocal:qt.test(kt.protocol),global:true,processData:true,async:true,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Wt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":true,"text json":JSON.parse,"text xml":x.parseXML},flatOptions:{url:true,context:true}},ajaxSetup:function(e,t){return t?Xt(Xt(e,x.ajaxSettings),t):Xt(x.ajaxSettings,e)},ajaxPrefilter:Vt(jt),ajaxTransport:Vt(Ut),ajax:function(e,t){if(typeof e==="object"){t=e;e=undefined}t=t||{};var f,h,c,i,d,n,p,v,r,o,y=x.ajaxSetup({},t),m=y.context||y,g=y.context&&(m.nodeType||m.jquery)?x(m):x.event,S=x.Deferred(),b=x.Callbacks("once memory"),w=y.statusCode||{},a={},s={},l="canceled",_={readyState:0,getResponseHeader:function(e){var t;if(p){if(!i){i={};while(t=Lt.exec(c)){i[t[1].toLowerCase()+" "]=(i[t[1].toLowerCase()+" "]||[]).concat(t[2])}}t=i[e.toLowerCase()+" "]}return t==null?null:t.join(", ")},getAllResponseHeaders:function(){return p?c:null},setRequestHeader:function(e,t){if(p==null){e=s[e.toLowerCase()]=s[e.toLowerCase()]||e;a[e]=t}return this},overrideMimeType:function(e){if(p==null){y.mimeType=e}return this},statusCode:function(e){var t;if(e){if(p){_.always(e[_.status])}else{for(t in e){w[t]=[w[t],e[t]]}}}return this},abort:function(e){var t=e||l;if(f){f.abort(t)}u(0,t);return this}};S.promise(_);y.url=((e||y.url||kt.href)+"").replace(Ht,kt.protocol+"//");y.type=t.method||t.type||y.method||y.type;y.dataTypes=(y.dataType||"*").toLowerCase().match(F)||[""];if(y.crossDomain==null){n=C.createElement("a");try{n.href=y.url;n.href=n.href;y.crossDomain=Gt.protocol+"//"+Gt.host!==n.protocol+"//"+n.host}catch(e){y.crossDomain=true}}if(y.data&&y.processData&&typeof y.data!=="string"){y.data=x.param(y.data,y.traditional)}Yt(jt,y,t,_);if(p){return _}v=x.event&&y.global;if(v&&x.active++===0){x.event.trigger("ajaxStart")}y.type=y.type.toUpperCase();y.hasContent=!Nt.test(y.type);h=y.url.replace(Ft,"");if(!y.hasContent){o=y.url.slice(h.length);if(y.data&&(y.processData||typeof y.data==="string")){h+=(Mt.test(h)?"&":"?")+y.data;delete y.data}if(y.cache===false){h=h.replace(Bt,"$1");o=(Mt.test(h)?"&":"?")+"_="+Dt.guid+++o}y.url=h+o}else if(y.data&&y.processData&&(y.contentType||"").indexOf("application/x-www-form-urlencoded")===0){y.data=y.data.replace(Ot,"+")}if(y.ifModified){if(x.lastModified[h]){_.setRequestHeader("If-Modified-Since",x.lastModified[h])}if(x.etag[h]){_.setRequestHeader("If-None-Match",x.etag[h])}}if(y.data&&y.hasContent&&y.contentType!==false||t.contentType){_.setRequestHeader("Content-Type",y.contentType)}_.setRequestHeader("Accept",y.dataTypes[0]&&y.accepts[y.dataTypes[0]]?y.accepts[y.dataTypes[0]]+(y.dataTypes[0]!=="*"?", "+Wt+"; q=0.01":""):y.accepts["*"]);for(r in y.headers){_.setRequestHeader(r,y.headers[r])}if(y.beforeSend&&(y.beforeSend.call(m,_,y)===false||p)){return _.abort()}l="abort";b.add(y.complete);_.done(y.success);_.fail(y.error);f=Yt(Ut,y,t,_);if(!f){u(-1,"No Transport")}else{_.readyState=1;if(v){g.trigger("ajaxSend",[_,y])}if(p){return _}if(y.async&&y.timeout>0){d=P.setTimeout(function(){_.abort("timeout")},y.timeout)}try{p=false;f.send(a,u)}catch(e){if(p){throw e}u(-1,e)}}function u(e,t,i,n){var r,o,a,s,l,u=t;if(p){return}p=true;if(d){P.clearTimeout(d)}f=undefined;c=n||"";_.readyState=e>0?4:0;r=e>=200&&e<300||e===304;if(i){s=Jt(y,_,i)}if(!r&&x.inArray("script",y.dataTypes)>-1){y.converters["text script"]=function(){}}s=Kt(y,s,_,r);if(r){if(y.ifModified){l=_.getResponseHeader("Last-Modified");if(l){x.lastModified[h]=l}l=_.getResponseHeader("etag");if(l){x.etag[h]=l}}if(e===204||y.type==="HEAD"){u="nocontent"}else if(e===304){u="notmodified"}else{u=s.state;o=s.data;a=s.error;r=!a}}else{a=u;if(e||!u){u="error";if(e<0){e=0}}}_.status=e;_.statusText=(t||u)+"";if(r){S.resolveWith(m,[o,u,_])}else{S.rejectWith(m,[_,u,a])}_.statusCode(w);w=undefined;if(v){g.trigger(r?"ajaxSuccess":"ajaxError",[_,y,r?o:a])}b.fireWith(m,[_,u]);if(v){g.trigger("ajaxComplete",[_,y]);if(!--x.active){x.event.trigger("ajaxStop")}}}return _},getJSON:function(e,t,i){return x.get(e,t,i,"json")},getScript:function(e,t){return x.get(e,undefined,t,"script")}});x.each(["get","post"],function(e,r){x[r]=function(e,t,i,n){if(g(t)){n=n||i;i=t;t=undefined}return x.ajax(x.extend({url:e,type:r,dataType:n,data:t,success:i},x.isPlainObject(e)&&e))}});x.ajaxPrefilter(function(e){var t;for(t in e.headers){if(t.toLowerCase()==="content-type"){e.contentType=e.headers[t]||""}}});x._evalUrl=function(e,t,i){return x.ajax({url:e,type:"GET",dataType:"script",cache:true,async:false,global:false,converters:{"text script":function(){}},dataFilter:function(e){x.globalEval(e,t,i)}})};x.fn.extend({wrapAll:function(e){var t;if(this[0]){if(g(e)){e=e.call(this[0])}t=x(e,this[0].ownerDocument).eq(0).clone(true);if(this[0].parentNode){t.insertBefore(this[0])}t.map(function(){var e=this;while(e.firstElementChild){e=e.firstElementChild}return e}).append(this)}return this},wrapInner:function(i){if(g(i)){return this.each(function(e){x(this).wrapInner(i.call(this,e))})}return this.each(function(){var e=x(this),t=e.contents();if(t.length){t.wrapAll(i)}else{e.append(i)}})},wrap:function(t){var i=g(t);return this.each(function(e){x(this).wrapAll(i?t.call(this,e):t)})},unwrap:function(e){this.parent(e).not("body").each(function(){x(this).replaceWith(this.childNodes)});return this}});x.expr.pseudos.hidden=function(e){return!x.expr.pseudos.visible(e)};x.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)};x.ajaxSettings.xhr=function(){try{return new P.XMLHttpRequest}catch(e){}};var $t={0:200,1223:204},Zt=x.ajaxSettings.xhr();m.cors=!!Zt&&"withCredentials"in Zt;m.ajax=Zt=!!Zt;x.ajaxTransport(function(r){var o,a;if(m.cors||Zt&&!r.crossDomain){return{send:function(e,t){var i,n=r.xhr();n.open(r.type,r.url,r.async,r.username,r.password);if(r.xhrFields){for(i in r.xhrFields){n[i]=r.xhrFields[i]}}if(r.mimeType&&n.overrideMimeType){n.overrideMimeType(r.mimeType)}if(!r.crossDomain&&!e["X-Requested-With"]){e["X-Requested-With"]="XMLHttpRequest"}for(i in e){n.setRequestHeader(i,e[i])}o=function(e){return function(){if(o){o=a=n.onload=n.onerror=n.onabort=n.ontimeout=n.onreadystatechange=null;if(e==="abort"){n.abort()}else if(e==="error"){if(typeof n.status!=="number"){t(0,"error")}else{t(n.status,n.statusText)}}else{t($t[n.status]||n.status,n.statusText,(n.responseType||"text")!=="text"||typeof n.responseText!=="string"?{binary:n.response}:{text:n.responseText},n.getAllResponseHeaders())}}}};n.onload=o();a=n.onerror=n.ontimeout=o("error");if(n.onabort!==undefined){n.onabort=a}else{n.onreadystatechange=function(){if(n.readyState===4){P.setTimeout(function(){if(o){a()}})}}}o=o("abort");try{n.send(r.hasContent&&r.data||null)}catch(e){if(o){throw e}}},abort:function(){if(o){o()}}}}});x.ajaxPrefilter(function(e){if(e.crossDomain){e.contents.script=false}});x.ajaxSetup({accepts:{script:"text/javascript, application/javascript, "+"application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){x.globalEval(e);return e}}});x.ajaxPrefilter("script",function(e){if(e.cache===undefined){e.cache=false}if(e.crossDomain){e.type="GET"}});x.ajaxTransport("script",function(i){if(i.crossDomain||i.scriptAttrs){var n,r;return{send:function(e,t){n=x("<script>").attr(i.scriptAttrs||{}).prop({charset:i.scriptCharset,src:i.url}).on("load error",r=function(e){n.remove();r=null;if(e){t(e.type==="error"?404:200,e.type)}});C.head.appendChild(n[0])},abort:function(){if(r){r()}}}}});var Qt=[],ei=/(=)\?(?=&|$)|\?\?/;x.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Qt.pop()||x.expando+"_"+Dt.guid++;this[e]=true;return e}});x.ajaxPrefilter("json jsonp",function(e,t,i){var n,r,o,a=e.jsonp!==false&&(ei.test(e.url)?"url":typeof e.data==="string"&&(e.contentType||"").indexOf("application/x-www-form-urlencoded")===0&&ei.test(e.data)&&"data");if(a||e.dataTypes[0]==="jsonp"){n=e.jsonpCallback=g(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback;if(a){e[a]=e[a].replace(ei,"$1"+n)}else if(e.jsonp!==false){e.url+=(Mt.test(e.url)?"&":"?")+e.jsonp+"="+n}e.converters["script json"]=function(){if(!o){x.error(n+" was not called")}return o[0]};e.dataTypes[0]="json";r=P[n];P[n]=function(){o=arguments};i.always(function(){if(r===undefined){x(P).removeProp(n)}else{P[n]=r}if(e[n]){e.jsonpCallback=t.jsonpCallback;Qt.push(n)}if(o&&g(r)){r(o[0])}o=r=undefined});return"script"}});m.createHTMLDocument=function(){var e=C.implementation.createHTMLDocument("").body;e.innerHTML="<form></form><form></form>";return e.childNodes.length===2}();x.parseHTML=function(e,t,i){if(typeof e!=="string"){return[]}if(typeof t==="boolean"){i=t;t=false}var n,r,o;if(!t){if(m.createHTMLDocument){t=C.implementation.createHTMLDocument("");n=t.createElement("base");n.href=C.location.href;t.head.appendChild(n)}else{t=C}}r=D.exec(e);o=!i&&[];if(r){return[t.createElement(r[1])]}r=we([e],t,o);if(o&&o.length){x(o).remove()}return x.merge([],r.childNodes)};x.fn.load=function(e,t,i){var n,r,o,a=this,s=e.indexOf(" ");if(s>-1){n=wt(e.slice(s));e=e.slice(0,s)}if(g(t)){i=t;t=undefined}else if(t&&typeof t==="object"){r="POST"}if(a.length>0){x.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments;a.html(n?x("<div>").append(x.parseHTML(e)).find(n):e)}).always(i&&function(e,t){a.each(function(){i.apply(this,o||[e.responseText,t,e])})})}return this};x.expr.pseudos.animated=function(t){return x.grep(x.timers,function(e){return t===e.elem}).length};x.offset={setOffset:function(e,t,i){var n,r,o,a,s,l,u,f=x.css(e,"position"),h=x(e),c={};if(f==="static"){e.style.position="relative"}s=h.offset();o=x.css(e,"top");l=x.css(e,"left");u=(f==="absolute"||f==="fixed")&&(o+l).indexOf("auto")>-1;if(u){n=h.position();a=n.top;r=n.left}else{a=parseFloat(o)||0;r=parseFloat(l)||0}if(g(t)){t=t.call(e,i,x.extend({},s))}if(t.top!=null){c.top=t.top-s.top+a}if(t.left!=null){c.left=t.left-s.left+r}if("using"in t){t.using.call(e,c)}else{if(typeof c.top==="number"){c.top+="px"}if(typeof c.left==="number"){c.left+="px"}h.css(c)}}};x.fn.extend({offset:function(t){if(arguments.length){return t===undefined?this:this.each(function(e){x.offset.setOffset(this,t,e)})}var e,i,n=this[0];if(!n){return}if(!n.getClientRects().length){return{top:0,left:0}}e=n.getBoundingClientRect();i=n.ownerDocument.defaultView;return{top:e.top+i.pageYOffset,left:e.left+i.pageXOffset}},position:function(){if(!this[0]){return}var e,t,i,n=this[0],r={top:0,left:0};if(x.css(n,"position")==="fixed"){t=n.getBoundingClientRect()}else{t=this.offset();i=n.ownerDocument;e=n.offsetParent||i.documentElement;while(e&&(e===i.body||e===i.documentElement)&&x.css(e,"position")==="static"){e=e.parentNode}if(e&&e!==n&&e.nodeType===1){r=x(e).offset();r.top+=x.css(e,"borderTopWidth",true);r.left+=x.css(e,"borderLeftWidth",true)}}return{top:t.top-r.top-x.css(n,"marginTop",true),left:t.left-r.left-x.css(n,"marginLeft",true)}},offsetParent:function(){return this.map(function(){var e=this.offsetParent;while(e&&x.css(e,"position")==="static"){e=e.offsetParent}return e||ae})}});x.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,r){var o="pageYOffset"===r;x.fn[t]=function(e){return W(this,function(e,t,i){var n;if(S(e)){n=e}else if(e.nodeType===9){n=e.defaultView}if(i===undefined){return n?n[r]:e[t]}if(n){n.scrollTo(!o?i:n.pageXOffset,o?i:n.pageYOffset)}else{e[t]=i}},t,e,arguments.length)}});x.each(["top","left"],function(e,i){x.cssHooks[i]=Ve(m.pixelPosition,function(e,t){if(t){t=Ge(e,i);return He.test(t)?x(e).position()[i]+"px":t}})});x.each({Height:"height",Width:"width"},function(a,s){x.each({padding:"inner"+a,content:s,"":"outer"+a},function(n,o){x.fn[o]=function(e,t){var i=arguments.length&&(n||typeof e!=="boolean"),r=n||(e===true||t===true?"margin":"border");return W(this,function(e,t,i){var n;if(S(e)){return o.indexOf("outer")===0?e["inner"+a]:e.document.documentElement["client"+a]}if(e.nodeType===9){n=e.documentElement;return Math.max(e.body["scroll"+a],n["scroll"+a],e.body["offset"+a],n["offset"+a],n["client"+a])}return i===undefined?x.css(e,t,r):x.style(e,t,i,r)},s,i?e:undefined,i)}})});x.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){x.fn[t]=function(e){return this.on(t,e)}});x.fn.extend({bind:function(e,t,i){return this.on(e,null,t,i)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,i,n){return this.on(t,e,i,n)},undelegate:function(e,t,i){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",i)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}});x.each(("blur focus focusin focusout resize scroll click dblclick "+"mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave "+"change select submit keydown keypress keyup contextmenu").split(" "),function(e,i){x.fn[i]=function(e,t){return arguments.length>0?this.on(i,null,e,t):this.trigger(i)}});var ti=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;x.proxy=function(e,t){var i,n,r;if(typeof t==="string"){i=e[t];t=e;e=i}if(!g(e)){return undefined}n=s.call(arguments,2);r=function(){return e.apply(t||this,n.concat(s.call(arguments)))};r.guid=e.guid=e.guid||x.guid++;return r};x.holdReady=function(e){if(e){x.readyWait++}else{x.ready(true)}};x.isArray=Array.isArray;x.parseJSON=JSON.parse;x.nodeName=k;x.isFunction=g;x.isWindow=S;x.camelCase=X;x.type=w;x.now=Date.now;x.isNumeric=function(e){var t=x.type(e);return(t==="number"||t==="string")&&!isNaN(e-parseFloat(e))};x.trim=function(e){return e==null?"":(e+"").replace(ti,"")};if(true){!(ai=[],si=function(){return x}.apply(oi,ai),si!==undefined&&(ri.exports=si))}var ii=P.jQuery,ni=P.$;x.noConflict=function(e){if(P.$===x){P.$=ni}if(e&&P.jQuery===x){P.jQuery=ii}return x};if(typeof e==="undefined"){P.jQuery=P.$=x}return x})},function(e,t,i){e.exports=i(6)},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.JSPlugin=undefined;var j=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();var U=i(0);var r=i(7);var W=i(16);var G=i(17);var V=i(18);var n=i(4);var Y=o(n);function o(e){return e&&e.__esModule?e:{default:e}}function X(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var w=null;var a=-1;var n=0;var l=0;var u=1;var f=40;var h=1024*1024*4;var c=1001;var d=1002;var p=1003;var v=2001;var s=2002;var y=new r.StreamClient;var m=null;var g=null;var S=Symbol("OPTIONS");var o=Symbol("CURRENTPLAYRATE");var b=Symbol("CURRENTSOUNDWND");var _=Symbol("MAXWNDNUM");var P=Symbol("MAXWNDNUM");var C=Symbol("DRAWCANVAS");var x=Symbol("SHAPEID");var T=Symbol("WINDOWFULL");var k=Symbol("SINGLEWINDOW");var D=Symbol("FILETMP");var M=Symbol("STATUSTMP");var R=Symbol("UPGRADESTATUSURL");var E=Symbol("CURWNDINDEX");var I=Symbol("CALLBACKFUNCTION");var z=Symbol("PLUGINVERSION");var A=Symbol("CANFULLSCREEN");function O(){document.addEventListener("visibilitychange",function(){if(document.hidden){for(var e=0;e<16;e++){if(w[P][e]&&w[P][e].bLoad){w[P][e].oPlayCtrl.PlayM4_IsVisible(false)}}}else{for(var t=0;t<16;t++){if(w[P][t]&&w[P][t].bLoad){w[P][t].oPlayCtrl.PlayM4_IsVisible(true)}}}},false)}function F(e,t){if(e&&t){w[S].iWidth=e;w[S].iHeight=t}var i=w[S].iWidth%w[S].iCurrentSplit;var n=w[S].iHeight%w[S].iCurrentSplit;var r=(w[S].iWidth-i-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var o=(w[S].iHeight-n-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var a=(w[S].iWidth-i)/w[S].iCurrentSplit;var s=(w[S].iHeight-n)/w[S].iCurrentSplit;var l=w[S].iCurrentSplit;if(typeof w[S].szId==="string"){g=(0,Y.default)("#"+w[S].szId)}else{g=w[S].szId}var u='<div class="parent-wnd" style="overflow:hidden;width:100%; height:100%; position: relative;">';for(var f=0;f<w[_];f++){e=r+(f%l===l-1?i:0);t=o+(f+l>=Math.pow(l,2)?n:0);var h=a+(f%l===l-1?i:0);var c=s+(f+l>=Math.pow(l,2)?n:0);u+='<div style="float:left; background-color: '+w[S].oStyle.background+"; position: relative; width: "+h+"px; height: "+c+'px;">'+'<canvas id="canvas'+f+'" class="play-window" wid="'+f+'" width="'+h+'" height="'+c+'"></canvas>'+(f===0?'<img style="display:none;" id="playImg'+f+'" src="">':"")+'<canvas id="canvas_draw'+f+'"  class="draw-window" style="border:1px solid '+w[S].oStyle.border+';position:absolute; top:0; left:0;" wid="'+f+'" width='+e+" height="+t+"></canvas>"+"</div>"}u+="</div>";g.html(u);g.find(".parent-wnd").eq(0).children().eq(0).find(".draw-window").eq(0).css("border","1px solid "+w[S].oStyle.borderSelect)}function B(e,t){if(e&&t){w[S].iWidth=e;w[S].iHeight=t}var i=w[S].iWidth%w[S].iCurrentSplit;var n=w[S].iHeight%w[S].iCurrentSplit;var r=(w[S].iWidth-i-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var o=(w[S].iHeight-n-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var a=(w[S].iWidth-i)/w[S].iCurrentSplit;var s=(w[S].iHeight-n)/w[S].iCurrentSplit;var l=w[S].iCurrentSplit;if(typeof w[S].szId==="string"){g=(0,Y.default)("#"+w[S].szId)}else{g=w[S].szId}g.html("");var u='<div class="parent-wnd" style="overflow:hidden;width:100%; height:100%; position: relative;">';for(var f=0;f<w[_];f++){e=r+(f%l===l-1?i:0);t=o+(f+l>=Math.pow(l,2)?n:0);var h=a+(f%l===l-1?i:0);var c=s+(f+l>=Math.pow(l,2)?n:0);u+='<div style="float:left; background-color: '+w[S].oStyle.background+"; position: relative; width: "+h+"px; height: "+c+'px;">'+'<img id="canvas'+f+'" class="play-window" wid="'+f+'" width="'+h+'" height="'+c+'"></img>'+'<canvas id="canvas_draw'+f+'"  class="draw-window" style="border:1px solid '+w[S].oStyle.border+';position:absolute; top:0; left:0;" wid="'+f+'" width='+e+" height="+t+"></canvas>"+"</div>"}u+="</div>";g.html(u);g.find(".parent-wnd").eq(0).children().eq(0).find(".draw-window").eq(0).css("border","1px solid "+w[S].oStyle.borderSelect)}function e(){w.EventCallback=function(){return{loadEventHandler:function e(){window.loadEventHandler&&window.loadEventHandler()},zoomEventResponse:function e(){},windowEventSelect:function e(t){if(w[E]===t){return}w[E]=t;if(w[P][t].bEZoom||w[P][t].b3DZoom){(0,Y.default)(".draw-window").unbind();w[C].setDrawStatus(false);w[C]=null;w[C]=new V.ESCanvas("canvas_draw"+t);w[C].setShapeType("Rect");w[C].setDrawStyle("#ff0000","",0);if(w[P][t].bEZoom){w[C].setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){w[P][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{w[P][t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}})}else if(w[P][t].b3DZoom){w[C].setDrawStatus(true,function(e){w[I](e)})}}window.GetSelectWndInfo&&window.GetSelectWndInfo(t)},pluginErrorHandler:function e(t,i,n){window.PluginEventHandler&&window.PluginEventHandler(t,i,n)},windowEventOver:function e(t){window.windowEventOver&&window.windowEventOver(t)},windowEventOut:function e(t){window.windowEventOut&&window.windowEventOut(t)},windowEventUp:function e(t){window.windowEventUp&&window.windowEventUp(t)},windowFullCcreenChange:function e(t){window.windowFullCcreenChange&&window.windowFullCcreenChange(t)},firstFrameDisplay:function e(t,i,n){window.firstFrameDisplay&&window.firstFrameDisplay(t,i,n)},performanceLack:function e(){window.performanceLack&&window.performanceLack()},mouseEvent:function e(t,i,n){var r='<?xml version="1.0"?><MouseEvent><WndIndex>0</WndIndex><EventType>'+t+"</EventType>"+"<Position><x>"+i+"</x><y>"+n+"</y><delta>0</delta></Position></MouseEvent>";window.onMouseEvent&&window.onMouseEvent(r)}}}()}function L(){e();g.find(".parent-wnd").eq(0).children().each(function(n){var o=this;var r=false;(0,Y.default)(o).unbind().bind("mousedown",function(){g.find(".parent-wnd").eq(0).find(".draw-window").css("border","1px solid "+w[S].oStyle.border);g.find(".parent-wnd").eq(0).children().eq(n).find(".draw-window").eq(0).css("border","1px solid "+w[S].oStyle.borderSelect);w.EventCallback.windowEventSelect(parseInt(g.find(".parent-wnd").eq(0).children().eq(n).find(".play-window").eq(0).attr("wid"),10))});(0,Y.default)(o).bind("mouseover",function(e){w.EventCallback.windowEventOver(n);e.stopPropagation()});(0,Y.default)(o).bind("mouseout",function(e){w.EventCallback.windowEventOut(n);e.stopPropagation()});(0,Y.default)(o).bind("mousedown",function(e){r=true;var t=e.offsetX/w[C].m_iCanvasWidth;var i=e.offsetY/w[C].m_iCanvasHeight;if(e.button===2){w.EventCallback.mouseEvent(4,t,i)}else if(e.button===0){w.EventCallback.mouseEvent(1,t,i)}e.stopPropagation()});(0,Y.default)(o).bind("mousemove",function(e){var t=e.offsetX/w[C].m_iCanvasWidth;var i=e.offsetY/w[C].m_iCanvasHeight;if(r){w.EventCallback.mouseEvent(7,t,i)}else{w.EventCallback.mouseEvent(6,t,i)}e.stopPropagation()});(0,Y.default)(o).bind("mousewheel",function(e){var t=e.offsetX/w[C].m_iCanvasWidth;var i=e.offsetY/w[C].m_iCanvasHeight;w.EventCallback.mouseEvent(8,t,i);e.stopPropagation()});(0,Y.default)(o).bind("mouseup",function(e){r=false;w.EventCallback.windowEventUp(n);var t=e.offsetX/w[C].m_iCanvasWidth;var i=e.offsetY/w[C].m_iCanvasHeight;if(e.button===2){w.EventCallback.mouseEvent(5,t,i)}else if(e.button===0){w.EventCallback.mouseEvent(3,t,i)}});(0,Y.default)(o).bind("dblclick",function(e){if(!w[P][w[E]].bPlay||w[S].iType!==2){return}if(!w[A]){return}var t=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;var i=(0,Y.default)(o).get(0);if(!t){if(i.requestFullScreen){i.requestFullScreen()}else if(i.webkitRequestFullScreen){i.webkitRequestFullScreen()}else if(i.mozRequestFullScreen){i.mozRequestFullScreen()}w[k]=(0,Y.default)(o)}else{if(g.find(".parent-wnd").eq(0).width()===(0,Y.default)(window).width()){return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}w[k]=null;w[T]=false}var n=e.offsetX/w[C].m_iCanvasWidth;var r=e.offsetX/w[C].m_iCanvasHeight;w.EventCallback.mouseEvent(2,n,r);e.stopPropagation()})});if(typeof document.fullScreen!=="undefined"){document.addEventListener("fullscreenchange",function(){var e=document.fullscreen||false;w.EventCallback.windowFullCcreenChange(e)})}else if(typeof document.webkitIsFullScreen!=="undefined"){document.addEventListener("webkitfullscreenchange",function(){var e=document.webkitIsFullScreen||false;w.EventCallback.windowFullCcreenChange(e)})}else if(typeof document.mozFullScreen!=="undefined"){document.addEventListener("mozfullscreenchange",function(){var e=document.mozFullScreen||false;w.EventCallback.windowFullCcreenChange(e)})}}function q(){var e=g.find(".parent-wnd").eq(0).children().length;var t=w[S].iWidth%w[S].iCurrentSplit;var i=w[S].iHeight%w[S].iCurrentSplit;var n=(w[S].iWidth-t-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var r=(w[S].iHeight-i-w[S].iCurrentSplit*2)/w[S].iCurrentSplit;var o=(w[S].iWidth-t)/w[S].iCurrentSplit;var a=(w[S].iHeight-i)/w[S].iCurrentSplit;var s=w[S].iCurrentSplit;for(var l=0;l<e;l++){var u=n+(l%s===s-1?t:0);var f=r+(l+s>=Math.pow(s,2)?i:0);var h=o+(l%s===s-1?t:0);var c=a+(l+s>=Math.pow(s,2)?i:0);g.find(".parent-wnd").eq(0).children().eq(l).width(h);g.find(".parent-wnd").eq(0).children().eq(l).height(c);g.find(".parent-wnd").eq(0).children().eq(l).find(".draw-window").attr("width",u);g.find(".parent-wnd").eq(0).children().eq(l).find(".draw-window").attr("height",f);g.find(".parent-wnd").eq(0).children().eq(l).find(".play-window").attr("width",h);g.find(".parent-wnd").eq(0).children().eq(l).find(".play-window").attr("height",c)}g.find(".parent-wnd").eq(w[E]).find(".draw-window").css("border","1px solid "+w[S].oStyle.border);g.find(".parent-wnd").eq(w[E]).children().eq(0).find(".draw-window").eq(0).css("border","1px solid "+w[S].oStyle.borderSelect)}function N(e,t,o,i,n,r,a){if(!(0,Y.default)("#"+w[P][o].windowID).length){return}var s=false;if(i&&n){s=true}w[P][o].bLoad=true;y.openStream(e,t,function(e){if(e.bHead&&!w[P][o].bPlay){w[P][o].bPlay=true;w[P][o].aHead=new Uint8Array(e.buf);w[P][o].oPlayCtrl.PlayM4_OpenStream(e.buf,f,1024*1024*2);if(w[P][o].szSecretKey!==""){setTimeout(function(){w[P][o].oPlayCtrl.PlayM4_SetSecretKey(1,w[P][o].szSecretKey,128);w[P][o].szSecretKey=""},100)}if(w[P][o].aHead[8]===4){w[P][o].oPlayCtrl.PlayM4_SetStreamOpenMode(0)}else{w[P][o].oPlayCtrl.PlayM4_SetStreamOpenMode(1)}w[P][o].oPlayCtrl.PlayM4_SetInputBufSize(h);w[P][o].oPlayCtrl.PlayM4_Play(w[P][o].windowID)}else{var t=new Uint8Array(e.buf);var i=w[P][o].oPlayCtrl.PlayM4_GetInputBufSize();var n=w[P][o].oPlayCtrl.PlayM4_GetYUVBufSize();if(n===2&&!w[P][o].bFirstFrame){w[P][o].bFirstFrame=true;w[P][o].oPlayCtrl.PlayM4_GetFrameResolution(function(e,t){w.EventCallback.firstFrameDisplay(o,e,t)})}var r=w[P][o].oPlayCtrl.PlayM4_GetDecodeFrameType();if(i>h*.5&&i<h*.8&&w[P][o].iRate===1){if(r!==u&&!w[P][o].bFrameForward){w[P][o].oPlayCtrl.PlayM4_SetDecodeFrameType(u);w.EventCallback.performanceLack()}}else if(i>=h*.8){}if(n>10&&n<15&&!w[P][o].bFrameForward){if(r!==u){w[P][o].oPlayCtrl.PlayM4_SetDecodeFrameType(u);w.EventCallback.performanceLack()}}else if(n>15){}if(n<10&&i<h*.5){if(r!==l&&w[P][o].iRate===1){w[P][o].oPlayCtrl.PlayM4_SetDecodeFrameType(l)}}if(e.statusString){w.EventCallback.pluginErrorHandler(o,c,e)}else if(e.type&&e.type==="exception"){w.EventCallback.pluginErrorHandler(o,d,e)}else{w[P][o].oPlayCtrl.PlayM4_InputData(t,t.length)}}if(w[P][o].szStorageUUID){m.inputData(w[P][o].szStorageUUID,e.buf)}e=null},function(){if(w[P][o].bPlay){w.EventCallback.pluginErrorHandler(o,p);w[P][o].bPlay=false;w[P][o].bFrameForward=false;w[P][o].iRate=1;if(w[P][o].oPlayCtrl){w[P][o].oPlayCtrl.PlayM4_Stop();w[P][o].oPlayCtrl.PlayM4_CloseStream()}}}).then(function(e){w[P][o].szStreamUUID=e;y.startPlay(e,i,n).then(function(){if(s){w[P][o].szPlayType="playback";w[P][o].iRate=1;w[P][o].oPlayCtrl.PlayM4_PlayRate(w[P][o].iRate)}else{w[P][o].szPlayType="realplay"}r()},function(e){a(e)})},function(e){a(e)})}function H(e,t,n,i,r,o,a){if(!(0,Y.default)("#"+w[P][n].windowID).length){return}w[P][n].bPlay=true;y.openStream(e,t,function(e){if(!e.bHead){var t=new Blob([e.buf],{type:"image/jpeg"});var i=URL.createObjectURL(t);if((0,Y.default)("#canvas"+n).length){document.getElementById("canvas"+n).src=i}}},function(){if(w[P][n].bPlay){w.EventCallback.pluginErrorHandler(n,p);w[P][n].bPlay=false}}).then(function(e){w[P][n].szStreamUUID=e;y.startPlay(e,i,r).then(function(){w[P][n].szPlayType="realplay";o()},function(e){a(e)})},function(e){a(e)})}var t=function(){function r(e){X(this,r);w=this;var t={szId:"playWnd",iMode:0,iType:1,iWidth:400,iHeight:300,iMaxSplit:4,iCurrentSplit:2,szBasePath:"./"};this[S]=Object.assign(t,e);var i={border:"#343434",borderSelect:"#FFCC00",background:"#4C4B4B"};i=Object.assign(i,e.oStyle);this[S].oStyle=i;if(this[S].iCurrentSplit>this[S].iMaxSplit){this[S].iCurrentSplit=this[S].iMaxSplit}this[o]=1;this[b]=-1;this[_]=this[S].iMaxSplit*this[S].iMaxSplit;this[x]="";this[T]=false;this[k]=null;this[D]=null;this[M]="";this[R]="";this[E]=-1;this[I]=null;this[A]=true;this[z]="V1.2.0 build20190123";m=new G.StorageManager(this[S].szBasePath+"/transform");if(typeof w[S].szId==="string"){g=(0,Y.default)("#"+w[S].szId)}else{g=w[S].szId}this[P]=[];for(var n=0;n<this[_];n++){this[P][n]={};this[P][n].bSelect=false;this[P][n].bPlay=false;this[P][n].bPause=false;this[P][n].bRecord=false;this[P][n].oPlayCtrl=null;this[P][n].szPlayType="";this[P][n].szStorageUUID="";this[P][n].szStreamUUID="";this[P][n].aHead=[];this[P][n].bLoad=false;this[P][n].windowID="canvas"+n;this[P][n].drawID="canvas_draw"+n;this[P][n].iRate=1;this[P][n].bEZoom=false;this[P][n].b3DZoom=false;this[P][n].szSecretKey="";this[P][n].bFrameForward=false;this[P][n].iDecodeType=l;this[P][n].bFirstFrame=false}O();if(this[S].iMode===0){F()}else if(this[S].iMode===1){}else if(this[S].iMode===2){B()}this[C]=new V.ESCanvas("canvas_draw0");if(this[S].iType===0){g.hide()}L();w.EventCallback.windowEventSelect(0)}j(r,[{key:"JS_UpdateWindowStyle",value:function e(t){this[S].oStyle=t;q()}},{key:"JS_GetPluginVersion",value:function e(){return w[z]}},{key:"JS_ArrangeWindow",value:function e(t){if(t<w[S].iMaxSplit){w[S].iCurrentSplit=t}else{w[S].iCurrentSplit=w[S].iMaxSplit}if(U.oTool.isFirefox()){for(var i=0;i<w[S].iMaxSplit*w[S].iMaxSplit;i++){if(w[P][i].oPlayCtrl){w[P][i].oPlayCtrl.PlayM4_ClearCanvas()}}}q();w.EventCallback.windowEventSelect(0)}},{key:"JS_SetSecretKey",value:function e(t,i){if(t<0){return-1}if(i===""||typeof i==="undefined"){return-1}this[P][t].szSecretKey=i;return 0}},{key:"JS_Play",value:function e(n,r,o,a,s){var t=new Promise(function(t,i){if(o<0||o>w[_]-1){i();return}if(w[P][o].bFrameForward){i();return}if(w[P][o].bPlay){w.JS_Stop(o)}if(w[S].iMode===0){setTimeout(function(){w[P][o].bFirstFrame=false;w[P][o].iDecodeType=l;if(w[P][o].oPlayCtrl){N(n,r,o,a,s,t,i)}else{w[P][o].oPlayCtrl=new W.JSPlayCtrl(w[S].szBasePath+"/playctrl/",function(e){if(e.cmd==="loaded"&&!w[P][o].bLoad){N(n,r,o,a,s,t,i)}else if(e.cmd==="OnebyOne"){if(!e.status){if(!w[P][o].bPause){y.pause(w[P][o].szStreamUUID);w[P][o].bPause=true}}else{if(w[P][o].bPause){y.resume(w[P][o].szStreamUUID);w[P][o].bPause=false}}}else if(e.cmd==="GetFrameData"){w.EventCallback.pluginErrorHandler(o,v)}},o)}},200)}else if(w[S].iMode===1){}else if(w[S].iMode===2){var e=(0,Y.default)(".play-window").eq(o);e.show();H(n,r,o,a,s,t,i)}});return t}},{key:"JS_Seek",value:function e(i,n,r){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}y.seek(w[P][i].szStreamUUID,n,r).then(function(){e()},function(e){t(e)})});return t}},{key:"JS_GetSdkVersion",value:function e(){return w[P][0].oPlayCtrl.PlayM4_GetSdkVersion()}},{key:"JS_DestroyWorker",value:function e(){w[P].forEach(function(e){if(e.bPlay){e.oPlayCtrl.PlayM4_CloseStream()}if(e.oPlayCtrl){e.oPlayCtrl.PlayM4_Destroy();e.oPlayCtrl=null;e.bLoad=false}});w[C].setShapeType("")}},{key:"JS_Stop",value:function e(i){var t=new Promise(function(t,e){if(i<0||i>w[_]-1){e();return}if(w[P][i].szStorageUUID){w.JS_StopSave(i)}if(w[P][i].bEZoom){w.JS_DisableZoom(i)}if(w[b]===i){w[b]=-1}y.stop(w[P][i].szStreamUUID).then(function(){w[P][i].bPlay=false;w[P][i].bFrameForward=false;w[P][i].iRate=1;if(w[S].iMode===0){if(w[P][i].oPlayCtrl){w[P][i].oPlayCtrl.PlayM4_Stop&&w[P][i].oPlayCtrl.PlayM4_Stop();w[P][i].oPlayCtrl.PlayM4_CloseStream&&w[P][i].oPlayCtrl.PlayM4_CloseStream()}}else if(w[S].iMode===2){var e=(0,Y.default)(".play-window").eq(i);e.hide()}setTimeout(function(){t()},500)},function(){setTimeout(function(){e()},500)})});return t}},{key:"JS_Pause",value:function e(i){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}if(w[P][i].bFrameForward){t();return}y.pause(w[P][i].szStreamUUID).then(function(){w[P][i].oPlayCtrl.PlayM4_Pause(true);w[P][i].bPause=true;e()},function(e){t(e)})});return t}},{key:"JS_Resume",value:function e(i){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}y.resume(w[P][i].szStreamUUID).then(function(){if(w[o]!==1){w[P][i].iRate=w[o];y.setPlayRate(w[P][i].szStreamUUID,w[P][i].iRate);w[P][i].oPlayCtrl.PlayM4_PlayRate(w[P][i].iRate);if(w[o]>1){w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(u)}else{w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(l)}}if(w[P][i].bFrameForward){w[P][i].oPlayCtrl.PlayM4_Play(w[P][i].windowID);w[P][i].bFrameForward=false}else{w[P][i].oPlayCtrl.PlayM4_Pause(false)}w[P][i].bPause=false;e()},function(e){t(e)})});return t}},{key:"JS_Slow",value:function e(i){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}if(w[P][i].szPlayType!=="playback"){t();return}if(w[P][i].iRate===-8){t();return}if(w[P][i].bFrameForward){t();return}if(w[P][i].iRate<0&&w[P][i].iRate>-8){w[P][i].iRate*=2}if(w[P][i].iRate===1){w[P][i].iRate*=-2}if(w[P][i].iRate>1){w[P][i].iRate/=2}y.setPlayRate(w[P][i].szStreamUUID,w[P][i].iRate).then(function(){if(w[P][i].iRate<2){w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(l)}else{w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(u);w[P][i].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}w[P][i].oPlayCtrl.PlayM4_PlayRate(w[P][i].iRate);e()},function(e){t(e)})});return t}},{key:"JS_Fast",value:function e(i){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}if(w[P][i].szPlayType!=="playback"){t();return}if(w[P][i].bFrameForward){t();return}if(w[P][i].iRate===8){t();return}if(w[P][i].iRate===-2){w[P][i].iRate=1}else if(w[P][i].iRate<-2){w[P][i].iRate/=2}else if(w[P][i].iRate>0&&w[P][i].iRate<8){w[P][i].iRate*=2}y.setPlayRate(w[P][i].szStreamUUID,w[P][i].iRate).then(function(){if(w[P][i].iRate<2){w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(l)}else{w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(u);if(w[P][i].iRate===8){w[P][i].oPlayCtrl.PlayM4_SetIFrameDecInterval(2)}else{w[P][i].oPlayCtrl.PlayM4_SetIFrameDecInterval(0)}}w[P][i].oPlayCtrl.PlayM4_PlayRate(w[P][i].iRate);e()},function(e){t(e)})});return t}},{key:"JS_Transmission",value:function e(n,r){var t=new Promise(function(t,i){if(n<0||n>w[_]-1){i();return}if(!w[P][n].szStreamUUID){i();return}y.transmission(w[P][n].szStreamUUID,r).then(function(e){t(e)},function(e){i(e)})});return t}},{key:"JS_FrameForward",value:function e(i){var t=new Promise(function(e,t){if(i<0||i>w[_]-1){t();return}if(!w[P][i].bPlay){t();return}if(w[P][i].iRate!==1){w[P][i].iRate=1;w[o]=w[P][i].iRate;y.setPlayRate(w[P][i].szStreamUUID,w[P][i].iRate).then(function(){w[P][i].oPlayCtrl.PlayM4_PlayRate(w[P][i].iRate);w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(l);w[P][i].oPlayCtrl.PlayM4_OneByOne();w[P][i].bFrameForward=true},function(e){t(e)})}else{w[P][i].oPlayCtrl.PlayM4_PlayRate(w[P][i].iRate);w[P][i].oPlayCtrl.PlayM4_SetDecodeFrameType(l);w[P][i].oPlayCtrl.PlayM4_OneByOne();w[P][i].bFrameForward=true}e()});return t}},{key:"JS_GetOSDTime",value:function e(i){var t=new Promise(function(n,e){if(i<0||i>w[_]-1){e(a);return}if(!w[P][i].bPlay){e(a);return}var t=w[P][i].oPlayCtrl.PlayM4_GetOSDTime(function(e){var t=U.oTool.isIOS()||U.oTool.isSafari()||U.oTool.isEdge()?"/":" ";var i=Date.parse(e.replace(/-/g,t))/1e3;n(i)});if(t!==0){e(a);return}});return t}},{key:"JS_OpenSound",value:function e(t){if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}if(w[b]===t){return a}if(w[b]!==-1){w[P][w[b]].oPlayCtrl.PlayM4_StopSound()}if(w[P][t].oPlayCtrl.PlayM4_PlaySound(t)!==0){return a}w[b]=t;return n}},{key:"JS_GetVolume",value:function e(t,i){w[P][t].oPlayCtrl.PlayM4_GetVolume(function(e){i(e)})}},{key:"JS_SetVolume",value:function e(t,i){if(w[P][t].oPlayCtrl.PlayM4_SetVolume(i)!==0){return a}return n}},{key:"JS_CloseSound",value:function e(){var t=w[b];if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}if(w[P][t].oPlayCtrl.PlayM4_StopSound()!==0){return a}w[b]=-1;return n}},{key:"JS_EnableZoom",value:function e(t){if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}(0,Y.default)(".draw-window").unbind();this[C]=new V.ESCanvas("canvas_draw"+t);this[C].setShapeType("Rect");this[C].setDrawStyle("#ff0000","",0);this[C].setDrawStatus(true,function(e){if(e.startPos&&e.endPos){if(e.startPos[0]>e.endPos[0]){w[P][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)}else{w[P][t].oPlayCtrl.PlayM4_SetDisplayRegion({left:e.startPos[0],top:e.startPos[1],right:e.endPos[0],bottom:e.endPos[1]},true)}}});w[P][t].bEZoom=true;return n}},{key:"JS_DisableZoom",value:function e(t){if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}this[C].setDrawStatus(false);if(this[P][t].oPlayCtrl.PlayM4_SetDisplayRegion(null,false)!==0){return a}this[P][t].bEZoom=false;return n}},{key:"JS_Enable3DZoom",value:function e(t,i){if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}(0,Y.default)(".draw-window").unbind();this[I]=i;this[C]=new V.ESCanvas("canvas_draw"+t);this[C].setShapeType("Rect");this[C].setDrawStyle("#ff0000","",0);this[C].setDrawStatus(true,function(e){i(e)});w[P][t].b3DZoom=true;return n}},{key:"JS_Disable3DZoom",value:function e(t){if(t<0||t>w[_]-1){return a}if(!w[P][t].bPlay){return a}this[C].setDrawStatus(false);this[P][t].b3DZoom=false;return n}},{key:"JS_CapturePicture",value:function e(n,r,o,a){var t=new Promise(function(t,i){if(n<0||n>w[_]-1){i();return}if(!w[P][n].bPlay){i();return}if(!o){o="JPEG"}if(o==="BMP"){w[P][n].oPlayCtrl.PlayM4_GetBMP(function(e){if(e===6){i(s)}else{if(a){a(e)}else{U.oTool.downloadFile(e,r+".BMP")}t()}})}else if(o==="JPEG"){w[P][n].oPlayCtrl.PlayM4_GetJPEG(function(e){if(e===6){i(s)}else{if(a){a(e)}else{U.oTool.downloadFile(e,r+".jpeg")}t()}})}});return t}},{key:"JS_StopRealPlayAll",value:function e(){y.stopAll();w[P].forEach(function(e,t){if(e.bPlay){if(e.szStorageUUID){w.JS_StopSave(t)}if(e.bEZoom){w.JS_DisableZoom(t)}if(w[S].iMode===0){e.oPlayCtrl.PlayM4_Stop();e.oPlayCtrl.PlayM4_CloseStream()}else if(w[S].iMode===2){(0,Y.default)(".play-window").hide()}}e.bPlay=false});w[b]=-1}},{key:"JS_StartSave",value:function e(o,a,s){var t=new Promise(function(t,e){var i=0;if(s){i=s.iPackage}if(o<0||o>w[_]-1){e();return}if(!w[P][o].bPlay){e();return}if(a.indexOf(".mp4")<0){a=a+".mp4"}var n=w[P][o].aHead;var r=0;if(w[P][o].szPlayType==="playback"){r=1}m.startRecord(a,n,2,r,{cbEventHandler:function e(t){w.EventCallback.pluginErrorHandler(o,t)},iPackage:i}).then(function(e){w[P][o].szStorageUUID=e;t()},function(){e()})});return t}},{key:"JS_StopSave",value:function e(i){var t=new Promise(function(e,t){if(!w[P][i].szStorageUUID){t();return}m.stopRecord(w[P][i].szStorageUUID).then(function(){w[P][i].szStorageUUID="";e()},function(e){t(e)})});return t}},{key:"JS_GetLocalConfig",value:function e(){return""}},{key:"JS_SetLocalConfig",value:function e(){return 0}},{key:"JS_SetGridInfo",value:function e(t){if(t===null||typeof t==="undefined"){return-1}var i="#ff0000";if(t.drawColor){i=t.drawColor}this[C].setDrawStyle(i);this[C].setShapesInfoByType("Grid",[{szGridMap:t.gridMap,iGridColNum:t.gridColNum,iGridRowNum:t.gridRowNum}]);return 0}},{key:"JS_GetGridInfo",value:function e(){if(!this[C]){return{}}var t=this[C].getShapesInfoByType("Grid")[0];if(!t){return{iGridRowNum:18,iGridColNum:22,szGridMap:""}}return{gridColNum:t.iGridColNum,gridRowNum:t.iGridRowNum,gridMap:t.szGridMap}}},{key:"JS_SetDrawShapeInfo",value:function e(t,i){if(typeof t==="undefined"||t===""){return-1}this[C].setShapeType(t);this[C].setDrawStyle(i.szDrawColor||"",i.szFillColor||"",i.iTranslucent||0);if(i.iMaxShapeSupport&&i.iMaxShapeSupport>0){this[C].setMaxShapeSupport(i.iMaxShapeSupport)}if(i.iMaxShapeSupport>0){this[C].setCurrentShapeInfo({szId:"",szTips:i.szTips||"",iMinClosed:3,iMaxPointNum:i.iMaxPointSupport,iPolygonType:1,szDrawColor:i.szDrawColor||"",szFillColor:i.szFillColor||"",iTranslucent:i.iTranslucent||0})}}},{key:"JS_SetPolygonInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var i=[];if(t.length>0){for(var n=0,r=t.length;n<r;n++){var o=t[n].aPoint;if(o.length>0){i.push(t[n])}}}if(i.length>0){this[C].setShapesInfoByType("Polygon",i);return 0}return-1}},{key:"JS_GetPolygonInfo",value:function e(){var t=[];var i=this[C].getShapesInfoByType("Polygon");for(var n=0,r=i.length;n<r;n++){var o=i[n];var a={aPoint:o.aPoint,bClosed:o.bClosed,szTips:o.szTips};t.push(a)}return t}},{key:"JS_SetLineInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var i=[];if(t.length>0){for(var n=0,r=t.length;n<r;n++){var o=t[n].aPoint;if(o.length>0){i.push(t[n])}}}if(i.length>0){this[C].setShapesInfoByType("Line",i);return 0}return-1}},{key:"JS_GetLineInfo",value:function e(){var t=[];var i=this[C].getShapesInfoByType("Line");for(var n=0,r=i.length;n<r;n++){var o=i[n];var a={iLineType:o.iLineType,aPoint:o.aPoint,szTips:o.szTips};t.push(a)}return t}},{key:"JS_SetRectInfo",value:function e(t){if(typeof t==="undefined"||!t.length){return-1}var i=[];if(t.length>0){for(var n=0,r=t.length;n<r;n++){var o=t[n].aPoint;if(o.length>0){i.push(t[n])}}}if(i.length>0){this[C].setShapesInfoByType("Rect",i);return 0}return-1}},{key:"JS_GetRectInfo",value:function e(){var t=[];var i=this[C].getShapesInfoByType("Rect");for(var n=0,r=i.length;n<r;n++){var o=i[n];var a={aPoint:o.aPoint,szTips:o.szTips};t.push(a)}return t}},{key:"JS_SetRegionInfo",value:function e(t){this[C].clearAllShape();var i=U.oTool.parseXmlFromStr(t);this[C].setDrawStyle("#ff0000","#343434",.3);if((0,Y.default)(i).find("DetectionRegionInfo").length>0){this[C].setShapeType("Rect");var n=parseInt((0,Y.default)(i).find("MaxRegionNum").eq(0).text(),10);this[C].setMaxShapeSupport(n);this[C].m_szDisplayMode=(0,Y.default)(i).find("DisplayMode").eq(0).text();this[C].m_szVideoFormat=(0,Y.default)(i).find("videoFormat").eq(0).text();this[C].m_iHorizontalResolution=parseInt((0,Y.default)(i).find("HorizontalResolution").eq(0).text(),10);this[C].m_iVerticalResolution=parseInt((0,Y.default)(i).find("VerticalResolution").eq(0).text(),10);var o=[];(0,Y.default)(i).find("DetectionRegion").each(function(){var e=[];for(var t=0,i=(0,Y.default)(this).find("positionX").length;t<i;t++){var n=Math.round((0,Y.default)(this).find("positionX").eq(t).text())*w[C].m_iCanvasWidth/w[C].m_iHorizontalResolution;var r=(w[C].m_iVerticalResolution-Math.round((0,Y.default)(this).find("positionY").eq(t).text()))*w[C].m_iCanvasHeight/w[C].m_iVerticalResolution;e.push([n,r])}if(e.length>0&&!(e[0][0]===0&&e[1][0]===0&&e[2][0]===0&&e[3][0]===0)){o.push({aPoint:e,iEditType:w[C].m_szDisplayMode==="transparent"?1:0,szDrawColor:"#ff0000",szFillColor:"#343434",iTranslucent:.3})}});this[C].setShapesInfoByType("Rect",o)}else if((0,Y.default)(i).find("MoveDetection").length>0){this[C].setShapeType("Grid");var r=parseInt((0,Y.default)(i).find("columnGranularity").eq(0).text(),10);var a=parseInt((0,Y.default)(i).find("rowGranularity").eq(0).text(),10);var s=(0,Y.default)(i).find("gridMap").eq(0).text();this[C].setShapesInfoByType("Grid",[{szGridMap:s,iGridColNum:r,iGridRowNum:a}])}return 0}},{key:"JS_GetRegionInfo",value:function e(){if(!this[C]){return""}var t=this[C].getShapeType();var i='<?xml version="1.0" encoding="utf-8"?>';if(t==="Rect"){i+="<DetectionRegionInfo>";i+="<videoFormat>"+this[C].m_szVideoFormat+"</videoFormat><RegionType>roi</RegionType>";i+="<ROI><HorizontalResolution>"+this[C].m_iHorizontalResolution+"</HorizontalResolution><VerticalResolution>"+this[C].m_iVerticalResolution+"</VerticalResolution></ROI>";i+="<DisplayMode>"+this[C].m_szDisplayMode+"</DisplayMode><MaxRegionNum>"+this[C].getMaxShapeSupport()+"</MaxRegionNum>";i+="<DetectionRegionList>";var n=this[C].getShapesInfoByType("Rect");for(var r=0,o=n.length;r<o;r++){var a=n[r].aPoint;i+="<DetectionRegion><RegionCoordinatesList>";i+="<RegionCoordinates><positionX>"+Math.round(a[3][0]*this[C].m_iHorizontalResolution/this[C].m_iCanvasWidth)+"</positionX><positionY>"+(this[C].m_iVerticalResolution-Math.round(a[3][1]*this[C].m_iVerticalResolution/this[C].m_iCanvasHeight))+"</positionY></RegionCoordinates>";i+="<RegionCoordinates><positionX>"+Math.round(a[2][0]*this[C].m_iHorizontalResolution/this[C].m_iCanvasWidth)+"</positionX><positionY>"+(this[C].m_iVerticalResolution-Math.round(a[2][1]*this[C].m_iVerticalResolution/this[C].m_iCanvasHeight))+"</positionY></RegionCoordinates>";i+="<RegionCoordinates><positionX>"+Math.round(a[1][0]*this[C].m_iHorizontalResolution/this[C].m_iCanvasWidth)+"</positionX><positionY>"+(this[C].m_iVerticalResolution-Math.round(a[1][1]*this[C].m_iVerticalResolution/this[C].m_iCanvasHeight))+"</positionY></RegionCoordinates>";i+="<RegionCoordinates><positionX>"+Math.round(a[0][0]*this[C].m_iHorizontalResolution/this[C].m_iCanvasWidth)+"</positionX><positionY>"+(this[C].m_iVerticalResolution-Math.round(a[0][1]*this[C].m_iVerticalResolution/this[C].m_iCanvasHeight))+"</positionY></RegionCoordinates>";i+="</RegionCoordinatesList></DetectionRegion>"}i+="</DetectionRegionList>";i+="</DetectionRegionInfo>"}else if(t==="Grid"){var s=this[C].getShapesInfoByType("Grid")[0];if(!s){s={iGridRowNum:18,iGridColNum:22,szGridMap:""}}i+="<MoveDetection><videoFormat>PAL</videoFormat><RegionType>grid</RegionType>";i+="<Grid><rowGranularity>"+s.iGridRowNum+"</rowGranularity><columnGranularity>"+s.iGridColNum+"</columnGranularity></Grid>";i+="<DisplayMode>transparent</DisplayMode>";i+="<gridMap>"+s.szGridMap+"</gridMap></MoveDetection>"}return i}},{key:"JS_SetDrawStatus",value:function e(t){if(!this[C]){return-1}this[C].setDrawStatus(t);return 0}},{key:"JS_ClearRegion",value:function e(){if(!this[C]){return-1}this[C].clearAllShape();return 0}},{key:"JS_GetTextOverlay",value:function e(){if(!this[C]){return""}var t='<?xml version="1.0" encoding="utf-8"?>';t+="<OSD>";t+="<videoResolutionWidth>"+this[C].m_iHorizontalResolution+"</videoResolutionWidth>";t+="<videoResolutionHeight>"+this[C].m_iVerticalResolution+"</videoResolutionHeight>";var i="";var n="";var r="";var o=this[C].getShapesInfoByType("RectOSD");for(var a=0,s=o.length;a<s;a++){var l=o[a];var u=Math.round(l.iPositionX*this[C].m_iHorizontalResolution/this[C].m_iCanvasWidth);var f=Math.round(l.iPositionY*this[C].m_iVerticalResolution/this[C].m_iCanvasHeight);if(l.szOSDType==="overlay-date"){i+="<DateTimeOverlay><Type>"+l.szDateStyle+"</Type>";i+="<clockType>"+l.szClockType+"</clockType>";i+="<displayWeek>"+l.szDisplayWeek+"</displayWeek>";i+="<enabled>"+l.szEnabled+"</enabled>";i+="<alignment>"+l.szAlignment+"</alignment>";i+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></DateTimeOverlay>"}else if(l.szOSDType==="overlay-ch"){n+="<channelNameOverlay><enabled>"+l.szEnabled+"</enabled>";n+="<ChannelName>"+l.szText+"</ChannelName>";n+="<alignment>"+l.szAlignment+"</alignment>";n+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></channelNameOverlay>"}else if(l.szOSDType==="overlay-text"){r+="<TextOverlay><id>"+l.szId+"</id><enabled>"+l.szEnabled+"</enabled>";r+="<alignment>"+l.szAlignment+"</alignment>";r+="<displayText>"+l.szText+"</displayText>";r+="<positionX>"+u+"</positionX><positionY>"+f+"</positionY></TextOverlay>"}}t+=i;t+=n;t+="<TextOverlayList>";t+=r;t+="</TextOverlayList>";t+="</OSD>";return t}},{key:"JS_SetTextOverlay",value:function e(t){this[C].setMaxShapeSupport(20);var i=U.oTool.parseXmlFromStr(t);this[C].clearShapeByType("RectOSD");if((0,Y.default)(i).find("OSD").length>0){this[C].setDrawStyle("#ff0000","#343434",.7);this[C].m_iHorizontalResolution=parseInt((0,Y.default)(i).find("videoResolutionWidth").eq(0).text(),10);this[C].m_iVerticalResolution=parseInt((0,Y.default)(i).find("videoResolutionHeight").eq(0).text(),10);if((0,Y.default)(i).find("channelNameOverlay").length>0){var n=(0,Y.default)(i).find("channelNameOverlay").eq(0);var r=(0,Y.default)(n).find("ChannelName").eq(0).text();var o=(0,Y.default)(n).find("enabled").eq(0).text();var a=Math.round((0,Y.default)(n).find("positionX").eq(0).text())*this[C].m_iCanvasWidth/this[C].m_iHorizontalResolution;var s=Math.round((0,Y.default)(n).find("positionY").eq(0).text())*this[C].m_iCanvasHeight/this[C].m_iVerticalResolution;var l=(0,Y.default)(n).find("alignment").eq(0).text()||"0";this[C].addOSDShape(r,o,a,s,{szOSDType:"overlay-ch",szAlignment:l})}if((0,Y.default)(i).find("DateTimeOverlay").length>0){var u=(0,Y.default)(i).find("DateTimeOverlay").eq(0);var f=(0,Y.default)(u).find("enabled").eq(0).text();var h=(0,Y.default)(u).find("Type").eq(0).text()||(0,Y.default)(u).find("type").eq(0).text();var c=(0,Y.default)(u).find("displayWeek").eq(0).text();var d=(0,Y.default)(u).find("clockType").eq(0).text();var p="";var v="";var y=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var m=new Date;if(c==="true"){v=y[m.getDay()]}if(d==="24hour"){d=""}else{d="AM/PM"}switch(h){case"0":p="YYYY-MM-DD "+v+" hh:mm:ss "+d;break;case"1":p="MM-DD-YYYY "+v+" hh:mm:ss "+d;break;case"2":p="CHR-YYYY-MM-DD "+v+" hh:mm:ss "+d;break;case"3":p="CHR-MM-DD-YYYY "+v+" hh:mm:ss "+d;break;case"4":p="DD-MM-YYYY "+v+" hh:mm:ss "+d;break;case"5":p="CHR-DD-MM-YYYY "+v+" hh:mm:ss "+d;break;default:break}var g=Math.round((0,Y.default)(u).find("positionX").eq(0).text())*this[C].m_iCanvasWidth/this[C].m_iHorizontalResolution;var S=Math.round((0,Y.default)(u).find("positionY").eq(0).text())*this[C].m_iCanvasHeight/this[C].m_iVerticalResolution;var b=(0,Y.default)(u).find("alignment").eq(0).text()||"0";this[C].addOSDShape(p,f,g,S,{szOSDType:"overlay-date",szDateStyle:h,szDisplayWeek:c,szClockType:d,szAlignment:b})}if((0,Y.default)(i).find("TextOverlayList").length>0){(0,Y.default)(i).find("TextOverlayList").eq(0).find("TextOverlay").each(function(){var e=(0,Y.default)(this).find("displayText").eq(0).text();var t=(0,Y.default)(this).find("enabled").eq(0).text();var i=(0,Y.default)(this).find("id").eq(0).text();var n=Math.round((0,Y.default)(this).find("positionX").eq(0).text())*w[C].m_iCanvasWidth/w[C].m_iHorizontalResolution;var r=Math.round((0,Y.default)(this).find("positionY").eq(0).text())*w[C].m_iCanvasHeight/w[C].m_iVerticalResolution;var o=(0,Y.default)(this).find("alignment").eq(0).text()||"0";w[C].addOSDShape(e,t,n,r,{szOSDType:"overlay-text",szId:i,szAlignment:o})})}}return 0}},{key:"JS_ClearSnapInfo",value:function e(t){if(!this[C]){return-1}if(t===0){this[C].clearShapeByType("Rect")}else if(t===1){this[C].clearShapeByType("Polygon")}else if(t===2){this[C].clearShapeByType("Line")}else if(t===3){this[C].clearShapeByType("Rect");this[C].clearShapeByType("Polygon")}else if(t===5){this[C].clearShapeByType("Point")}else{this[C].clearAllShape()}return 0}},{key:"JS_ClearTargetPolygon",value:function e(t){var i=U.oTool.parseXmlFromStr(t);var n=this[C].getAllShapesInfo();var r=n.length;if(r>0){for(var o=0;o<r;o++){var a=(0,Y.default)(i).find("id").eq(0).text();if(n[o].szType==="Polygon"){if(n[o].szId===a){this[C].deleteShape(o);break}}}}}},{key:"JS_SetSnapPolygonInfo",value:function e(t){this[C].setShapeType("Polygon");this[C].setMaxShapeSupport(20);this[C].setDrawStyle("#FFFF00","#FFFF00",.1);var i=U.oTool.parseXmlFromStr(t);var n=this[C].getAllShapesInfo();var r=n.length;if(r>0){for(var o=0;o<r;o++){var a=(0,Y.default)(i).find("id").eq(0).text();if(n[o].szType==="Polygon"){if(n[o].szId===a){this[C].deleteShape(o);break}}}}var c=[];if((0,Y.default)(i).find("SnapPolygonList").length>0){(0,Y.default)(i).find("SnapPolygonList").eq(0).find("SnapPolygon").each(function(){var e=(0,Y.default)(this).find("id").eq(0).text();var t=parseInt((0,Y.default)(this).find("polygonType").eq(0).text()||"1",10);var i=(0,Y.default)(this).find("Tips").eq(0).text()||(0,Y.default)(this).find("tips").eq(0).text();var n=parseInt((0,Y.default)(this).find("MinClosed").eq(0).text(),10);var r=parseInt((0,Y.default)(this).find("PointNumMax").eq(0).text(),10);var o=parseInt((0,Y.default)(this).find("EditType").eq(0).text(),10)||parseInt((0,Y.default)(this).find("editType").eq(0).text(),10)||0;var a=(0,Y.default)(this).find("isClosed").eq(0).text()==="true";var s="rgb("+(0,Y.default)(this).find("r").eq(0).text()+","+(0,Y.default)(this).find("g").eq(0).text()+","+(0,Y.default)(this).find("b").eq(0).text()+")";var l=s;var u=.1;var f=parseInt((0,Y.default)(this).find("RedrawMode").eq(0).text(),10)||0;var h=[];(0,Y.default)(this).find("pointList").eq(0).find("point").each(function(e){h[e]=[];h[e][0]=Math.round((0,Y.default)(this).find("x").eq(0).text()*w[C].m_iCanvasWidth);h[e][1]=Math.round((0,Y.default)(this).find("y").eq(0).text()*w[C].m_iCanvasHeight)});if(h.length>0){c.push({szId:e,iPolygonType:t,iMinClosed:n,iMaxPointNum:r,iEditType:o,aPoint:h,bClosed:a,szTips:i,szDrawColor:s,szFillColor:l,iTranslucent:u,iRedrawMode:f});w[C].setDrawStatus(false)}else{w[C].setCurrentShapeInfo({szId:e,szTips:i,iMinClosed:n,iMaxPointNum:r,iPolygonType:t,szDrawColor:s,szFillColor:l,iTranslucent:u,iRedrawMode:f});w[C].setDrawStatus(true)}})}if(c.length>0){this[C].setShapesInfoByType("Polygon",c)}return 0}},{key:"JS_GetSnapPolygonInfo",value:function e(){var t="<?xml version='1.0' encoding='utf-8'?><SnapPolygonList>";var i=this[C].getShapesInfoByType("Polygon");for(var n=0,r=i.length;n<r;n++){var o=i[n];t+="<SnapPolygon>";t+="<id>"+o.szId+"</id>";t+="<polygonType>"+o.iPolygonType+"</polygonType>";t+="<color>";var a=o.szDrawColor.substring(4,o.szDrawColor.length-1).split(",");t+="<r>"+a[0]+"</r>";t+="<g>"+a[1]+"</g>";t+="<b>"+a[2]+"</b>";t+="</color>";t+="<tips>"+o.szTips+"</tips>";t+="<isClosed>"+o.bClosed+"</isClosed>";var s=o.aPoint;t+="<pointList>";for(var l=0,u=s.length;l<u;l++){t+="<point><x>"+(s[l][0]/this[C].m_iCanvasWidth).toFixed(6)+"</x><y>"+(s[l][1]/this[C].m_iCanvasHeight).toFixed(6)+"</y></point>"}t+="</pointList>";t+="</SnapPolygon>"}t+="</SnapPolygonList>";return t}},{key:"JS_SetSnapDrawMode",value:function e(t,i){if(!this[C]){return-1}this[C].setDrawMutiShapeOneTime(false);if(t===0&&i===3){w[C].setDrawStatus(false)}return 0}},{key:"JS_SetSnapLineInfo",value:function e(t){this[C].setShapeType("Line");this[C].setMaxShapeSupport(20);this[C].setDrawStyle("#FFFF00","#FFFF00",.1);var i=U.oTool.parseXmlFromStr(t);var n=this[C].getAllShapesInfo();var r=n.length;if(r>0){for(var o=0;o<r;o++){var a=(0,Y.default)(i).find("id").eq(0).text();if(n[o].szType==="Line"){if(n[o].szId===a){this[C].deleteShape(o);break}}}}var s=[];if((0,Y.default)(i).find("SnapLineList").length>0){(0,Y.default)(i).find("SnapLineList").eq(0).find("SnapLine").each(function(){var e=(0,Y.default)(this).find("id").eq(0).text();var t=parseInt((0,Y.default)(this).find("LineTypeEx").eq(0).text(),10);var i=parseInt((0,Y.default)(this).find("CustomType").text()||(0,Y.default)(this).find("LineType").text(),10);var n=parseInt((0,Y.default)(this).find("ArrowType").text()||0,10);var r=(0,Y.default)(this).find("Tips").eq(0).text()||(0,Y.default)(this).find("tips").eq(0).text();var o="rgb("+(0,Y.default)(this).find("r").eq(0).text()+","+(0,Y.default)(this).find("g").eq(0).text()+","+(0,Y.default)(this).find("b").eq(0).text()+")";var a=[];a[0]=[];a[1]=[];a[0][0]=Math.round((0,Y.default)(this).find("StartPos").eq(0).find("x").eq(0).text()*w[C].m_iCanvasWidth);a[0][1]=Math.round((0,Y.default)(this).find("StartPos").eq(0).find("y").eq(0).text()*w[C].m_iCanvasHeight);a[1][0]=Math.round((0,Y.default)(this).find("EndPos").eq(0).find("x").eq(0).text()*w[C].m_iCanvasWidth);a[1][1]=Math.round((0,Y.default)(this).find("EndPos").eq(0).find("y").eq(0).text()*w[C].m_iCanvasHeight);if(a.length>0){s.push({szId:e,iLineType:t,aPoint:a,szTips:r,iDirection:i,iArrowType:n,szDrawColor:o});w[C].setDrawStatus(false)}})}if(s.length>0){this[C].setShapesInfoByType("Line",s)}return 0}},{key:"JS_GetSnapLineInfo",value:function e(){var t="<?xml version='1.0' encoding='utf-8'?><SnapLineList>";var i=this[C].getShapesInfoByType("Line");for(var n=0,r=i.length;n<r;n++){t+="<SnapLine>";t+="<id>"+i[n].szId+"</id>";t+="<LineTypeEx>"+i[n].iLineType+"</LineTypeEx>";t+="<CustomType>0</CustomType><MoveChange>0</MoveChange><ArrowType>"+i[n].iArrowType+"</ArrowType>";t+="<tips>"+i[n].szTips+"</tips>";var o=i[n].aPoint;t+="<StartPos><x>"+(o[0][0]/w[C].m_iCanvasWidth).toFixed(6)+"</x><y>"+(o[0][1]/w[C].m_iCanvasHeight).toFixed(6)+"</y></StartPos>";t+="<EndPos><x>"+(o[1][0]/w[C].m_iCanvasWidth).toFixed(6)+"</x><y>"+(o[1][1]/w[C].m_iCanvasHeight).toFixed(6)+"</y></EndPos>";t+="<LineSelected>false</LineSelected>";if(i[n].aCrossArrowPoint.length>0){t+="<PDCArrow><Sp_x>"+(i[n].aCrossArrowPoint[0][0]/w[C].m_iCanvasWidth).toFixed(6)+"</Sp_x>";t+="<Sp_y>"+(i[n].aCrossArrowPoint[0][1]/w[C].m_iCanvasWidth).toFixed(6)+"</Sp_y>";t+="<Ep_x>"+(i[n].aCrossArrowPoint[1][0]/w[C].m_iCanvasWidth).toFixed(6)+"</Ep_x>";t+="<Ep_y>"+(i[n].aCrossArrowPoint[1][1]/w[C].m_iCanvasWidth).toFixed(6)+"</Ep_y></PDCArrow>"}t+="<PDCShowMark>false</PDCShowMark>";var a=i[n].szDrawColor.split(",")[0].split("(")[1];var s=i[n].szDrawColor.split(",")[1];var l=i[n].szDrawColor.split(",")[2].split(")")[0];t+="<color><r>"+(a||"255")+"</r><g>"+(s||"255")+"</g><b>"+(l||"0")+"</b></color>";t+="</SnapLine>"}t+="</SnapLineList>";return t}},{key:"JS_GetSnapPointInfo",value:function e(){return""}},{key:"JS_SelectShapeById",value:function e(t,i){if(!t){return-1}this[C].selectShapeById(t,i);return 0}},{key:"JS_DeleteChoosedShape",value:function e(){var t=this[C].getAllShapesInfo();var i=t.length;if(i>0){for(var n=0;n<i;n++){if(t[n].bChoosed){this[C].deleteShape(n);return t[n].szId}}return""}return""}},{key:"JS_SetSnapPointInfo",value:function e(t){var i=U.oTool.parseXmlFromStr(t);if((0,Y.default)(i).find("mode").eq(0).text()==="1"){var n=(0,Y.default)(i).find("id").eq(0).text();var r="rgb("+(0,Y.default)(i).find("r").eq(0).text()+","+(0,Y.default)(i).find("g").eq(0).text()+","+(0,Y.default)(i).find("b").eq(0).text()+")";this[C].setShapeType("Point");this[C].setDrawStatus(true);this[C].setMaxShapeSupport(12);w[C].setCurrentShapeInfo({szId:n,szDrawColor:r,iTranslucent:1})}else{var o=[];this[C].clearShapeByType("Point");this[C].setMaxShapeSupport(12);this[C].setDrawStatus(false);if((0,Y.default)(i).find("SnapPointList").length>0){(0,Y.default)(i).find("SnapPointList").eq(0).find("SnapPoint").each(function(){var e=(0,Y.default)(this).find("id").eq(0).text();var t="rgb("+(0,Y.default)(this).find("r").eq(0).text()+","+(0,Y.default)(this).find("g").eq(0).text()+","+(0,Y.default)(this).find("b").eq(0).text()+")";var i=[];i[0]=[];i[0][0]=Math.round((0,Y.default)(this).find("positionX").eq(0).text()*w[C].m_iCanvasWidth);i[0][1]=Math.round((0,Y.default)(this).find("positionY").eq(0).text()*w[C].m_iCanvasHeight);if(i.length>0){o.push({szId:e,aPoint:i,szDrawColor:t,iTranslucent:1})}})}if(o.length>0){this[C].setShapesInfoByType("Point",o)}}return 0}},{key:"JS_FullScreenDisplay",value:function e(t){if(t){var i=g.get(0);if(i.requestFullScreen){i.requestFullScreen()}else if(i.webkitRequestFullScreen){i.webkitRequestFullScreen()}else if(i.mozRequestFullScreen){i.mozRequestFullScreen()}}this[T]=t}},{key:"JS_FullScreenSingle",value:function e(t){if(!w[P][t].bPlay){return}var i=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;var n=g.find(".parent-wnd").eq(0).children().eq(t).get(0);if(!i){if(n.requestFullScreen){n.requestFullScreen()}else if(n.webkitRequestFullScreen){n.webkitRequestFullScreen()}else if(n.mozRequestFullScreen){n.mozRequestFullScreen()}w[k]=g.find(".parent-wnd").eq(0).children().eq(t)}else{if(g.find(".parent-wnd").eq(0).width()===(0,Y.default)(window).width()){return}if(document.exitFullscreen){document.exitFullscreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}w[k]=null;w[T]=false}}},{key:"JS_StartDownload",value:function e(t,i,n,r){var o=(0,Y.default)(U.oTool.parseXmlFromStr(r)).find("playbackURI").eq(0).text();var a=t+"?playbackURI="+o;var s=".mp4";if(t.indexOf("picture/Streaming/tracks")>0){a=t;s=".jpg"}var l=a.indexOf("&name=")+6;var u=a.indexOf("&size=");n=a.substring(l,u);(0,Y.default)("body").append('<a id="jsplugin_download_a" href="'+a+'" download='+n+s+'><li id="jsplugin_download_li"></li></a>');(0,Y.default)("#jsplugin_download_li").trigger("click");(0,Y.default)("#jsplugin_download_a").remove();return 0}},{key:"JS_Resize",value:function e(i,n){var r=this;setTimeout(function(){var e=document.fullscreen||document.webkitIsFullScreen||document.mozFullScreen||false;if(r[T]&&e){i=(0,Y.default)(window).width();n=(0,Y.default)(window).height();g.find("div").eq(0).css({width:i,height:n})}else{g.find("div").eq(0).css({width:i,height:n})}r[S].iWidth=i;r[S].iHeight=n;if(U.oTool.isFirefox()){for(var t=0;t<w[S].iMaxSplit*w[S].iMaxSplit;t++){if(w[P][t].oPlayCtrl){w[P][t].oPlayCtrl.PlayM4_ClearCanvas()}}}q();if(w[k]&&e){i=(0,Y.default)(window).width();n=(0,Y.default)(window).height();w[k].css({width:i,height:n});w[k].find(".play-window").attr("width",i);w[k].find(".play-window").attr("height",n);w[k].find(".draw-window").attr("width",i-2);w[k].find(".draw-window").attr("height",n-2);w[k].find("canvas").attr("width",i-2);w[k].find("canvas").attr("height",n-2)}if(!e){w[k]=null;w[T]=false}r[C].resizeCanvas();r[C].canvasRedraw()},80)}},{key:"JS_WndCreate",value:function e(t,i,n){F(i,n);this[C].updateCanvas("canvas_draw0");this[C].clearAllShape();if(t===0){g.hide()}else{g.show()}w.EventCallback.windowEventSelect(0);L();w[D]=null}},{key:"JS_ExportDeviceConfig",value:function e(t){(0,Y.default)("body").append('<a id="jsplugin_download_a" href="'+t+'"><li id="jsplugin_download_li"></li></a>');(0,Y.default)("#jsplugin_download_li").trigger("click");(0,Y.default)("#jsplugin_download_a").remove();return 0}},{key:"JS_OpenFileBrowser",value:function e(t,i,n,r,o){w[D]=null;var a=window.document.createElement("input");a.type="file";if(i.toLowerCase()==="bmp"){a.accept="image/bmp";a.style.display="none";(0,Y.default)("body").append(a)}if(t===0){a.setAttribute("webkitdirectory","")}a.addEventListener("change",function(){if(t===1){w[D]=a.files[0];n[r]=a.files[0].name;o&&o.$digest(function(){n[r]=a.files[0].name})}else if(t===0){w[D]=a.files}});var s=document.createEvent("MouseEvents");s.initEvent("click",true,true);a.dispatchEvent(s)}},{key:"JS_UploadFile",value:function e(t,i,n,r){var o=0;var a=new XMLHttpRequest;a.onreadystatechange=function(){if(a.readyState===4){if(a.status!==200){o=-1}}};a.open("put",t,false);a.setRequestHeader("Content-Type",r);a.send(w[D]);return o}},{key:"JS_StartAsynUpload",value:function e(t){var i=new XMLHttpRequest;i.onreadystatechange=function(){if(i.readyState===4){w[M]=i.responseText}};i.open("put",t,true);i.send(w[D]);return 0}},{key:"JS_StopAsynUpload",value:function e(){w[M]=""}},{key:"JS_GetUploadErrorInfo",value:function e(){if(typeof w[M]==="string"&&w[M].length>0){return w[M]}return""}},{key:"JS_StartUpgradeEx",value:function e(r,o){var t=new Promise(function(t,i){if(!r){i();return a}if(!o){i();return a}w[M]=0;var n=new XMLHttpRequest;n.onreadystatechange=function(){if(n.readyState===4){if(n.status===200){w[M]=100;t()}else{w[M]=1;var e=U.oTool.parseXmlFromStr(n.responseText);if((0,Y.default)(e).find("subStatusCode").text()==="lowPrivilege"){i(403)}else{i()}}}};n.open("put",r,true);n.send(w[D]);w[R]=o;setTimeout(function(){t()},3e3)});return t}},{key:"JS_UpgradeStatus",value:function e(){if(w[M]===100){return 0}return w[M]}},{key:"JS_UpgradeProgress",value:function e(){var t=0;var i=new XMLHttpRequest;i.onreadystatechange=function(){if(i.readyState===4){if(i.status===200){t=parseInt((0,Y.default)(U.oTool.parseXmlFromStr(i.responseText)).find("percent").text(),10)}}};i.open("get",w[R],false);i.send(null);if(w[M]===100){return 100}return t}},{key:"JS_StopUpgrade",value:function e(){w[D]=null;return 0}},{key:"JS_ExportDeviceLog",value:function e(t,i){i="Log.txt";var n=[];var r=[];n=n.concat((0,Y.default)(t).find("searchMatchItem").toArray());for(var o=0;o<n.length;o++){r[o]=[];r[o][0]=(0,Y.default)(n[o]).find("logtime").text().replace("T"," ").replace("Z","");r[o][1]=(0,Y.default)(n[o]).find("majortype").text();r[o][2]=(0,Y.default)(n[o]).find("minortype").text();r[o][3]=(0,Y.default)(n[o]).find("channelid").text();r[o][4]=(0,Y.default)(n[o]).find("userName").text();r[o][5]=(0,Y.default)(n[o]).find("remoteaddress").text()}var a=[];function s(e){a.push(e);var t=e.slice("");if(/^[\u4e00-\u9fa5]/.test(e)){for(var i=0;i<30-t.length*2;i++){a.push(" ")}}else{for(var n=0;n<30-t.length;n++){a.push(" ")}}}s(" ");s((0,Y.default)(t).find("laLogTime").text());s((0,Y.default)(t).find("laLogMajorType").text());s((0,Y.default)(t).find("laLogMinorType").text());s((0,Y.default)(t).find("laLogChannel").text());s((0,Y.default)(t).find("laLogRemoteUser").text());s((0,Y.default)(t).find("laLogRemoteIP").text());a.push("\r\n");for(var l=0;l<r.length;l++){var u=(l+1).toString();s(u);for(var f=0;f<6;f++){s(r[l][f])}a.push("\r\n")}a=a.join("");var h=new Blob([a],{type:"text/plain"});var c=(window.URL||window.webkitURL).createObjectURL(h);var d=window.document.createElement("a");d.href=c;d.download=i;var p=document.createEvent("MouseEvents");p.initEvent("click",true,true);d.dispatchEvent(p)}},{key:"outCsv",value:function e(t,i,n,r){var o="";var a="";for(var s=0;s<n.length;s++){a+=(0,Y.default)(t).find(n[s]).eq(0).text()+","}a=a.slice(0,-1);o+=a+"\r\n";for(var l=0;l<r.length;l++){a="";for(var u=0;u<n.length;u++){a+='"'+r[l][u]+'",'}a.slice(0,a.length-1);o+=a+"\r\n"}if(o===""){return}var f="";f+=i;var h="\ufeff";if(window.navigator.msSaveOrOpenBlob){var c=h+o;var d=new Blob([decodeURIComponent(encodeURI(c))],{type:"data:text/csv;charset=utf-8,"});navigator.msSaveBlob(d,f+".csv")}else{h="data:text/csv;charset=utf-8,\ufeff";var p=h+o;var v=encodeURI(p);var y=document.createElement("a");y.setAttribute("href",v);y.setAttribute("download",f+".csv");document.body.appendChild(y);y.click()}}},{key:"JS_ExportReport",value:function e(y,m,g){var S=this;var t=new Promise(function(e){var t=[];var i=[];var n=(0,Y.default)(y).find("NameList").text().split(",");t=t.concat((0,Y.default)(y).find("tDataItem").toArray());for(var r=0;r<t.length;r++){i[r]=[];for(var o=0;o<n.length;o++){i[r][o]=(0,Y.default)(t[r]).find(n[o]).text();if(n[o]==="logtime"){i[r][o]=i[r][o].replace("T"," ").replace("Z","")}}}var a=[];if(g===1){S.outCsv(y,m,n,i)}else{var s=function e(t){a.push(t);var i=t.slice("");if(/^[\u4e00-\u9fa5]/.test(t)){for(var n=0;n<30-i.length*2;n++){a.push(" ")}}else{for(var r=0;r<30-i.length;r++){a.push(" ")}}};s(" ");for(var l=0;l<n.length;l++){s((0,Y.default)(y).find(n[l]).eq(0).text())}a.push("\r\n");for(var u=0;u<i.length;u++){var f=(u+1).toString();s(f);for(var h=0;h<n.length;h++){s(i[u][h])}a.push("\r\n")}a=a.join("");var c=void 0;c=new Blob([a],{type:"text/plain"});var d=(window.URL||window.webkitURL).createObjectURL(c);var p=window.document.createElement("a");p.href=d;p.download=m;var v=document.createEvent("MouseEvents");v.initEvent("click",true,true);p.dispatchEvent(v)}e()});return t}},{key:"JS_GetWndContainer",value:function e(t){if(t<0||typeof t==="undefined"||t===null){return-1}return g.find(".parent-wnd").eq(0).children().eq(t)[0]}},{key:"JS_GetWndStatus",value:function e(t){if(t<0||typeof t==="undefined"||t===null){return-1}var i={bPlay:this[P][t].bPlay,bSound:this[b]===t,bSelect:this[P][t].bSelect,iRate:this[P][t].iRate};return i}},{key:"JS_SelectWnd",value:function e(t){g.find(".parent-wnd").eq(0).children().eq(t).mousedown()}},{key:"JS_PlayWithImg",value:function e(t){if(this[S].iType!==1){return}var i=(0,Y.default)("#canvas0");var n=(0,Y.default)("#canvas0").width();var r=(0,Y.default)("#canvas0").height();i.hide();var o=(0,Y.default)("#playImg0");o.show();o.css({width:n+"px",height:r+"px",border:"1px solid "+this[S].oStyle.border});o.attr("src",t)}},{key:"JS_SetCanFullScreen",value:function e(t){this[A]=t}},{key:"JS_OpenPlayerSDKPrintLog",value:function e(t){w[P][t].oPlayCtrl.OpenPlayerSDKPrintLog()}},{key:"JS_DownloadYUVdata",value:function e(t){w[P][t].oPlayCtrl.DownloadYUVdata()}},{key:"JS_DownloadPCMdata",value:function e(t){w[P][t].oPlayCtrl.DownloadPCMdata()}}]);return r}();return t}();t.JSPlugin=a},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StreamClient=undefined;var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();var r=i(1);var c=u(r);var o=i(10);var a=i(11);var s=i(12);var l=i(15);function u(e){return e&&e.__esModule?e:{default:e}}function f(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var h=function(){var C=Symbol("WEBSOCKET");var x=Symbol("GETINDEX");var T=Symbol("PROTOCOLVERSION");var k=Symbol("CIPHERSUITES");var h=new o.DirectDeviceCustom;var D=new a.DirectDevice;var M=new s.LiveMedia;var R=new l.LocalService;var e=function(){function e(){f(this,e);this[T]="0.1";this[k]=0;this[C]=[];this.ERRORS={};this[x]=function(e){var t=-1;for(var i=0,n=this[C].length;i<n;i++){if(this[C][i].id===e){t=i;break}}return t}}n(e,[{key:"openStream",value:function e(t,p,v,i){var n=false;if(t.indexOf("[")>-1){n=true}var y=this;var r=t.split("://");var o=r[0];var m="";var g=7681;var S=1;var b=0;var a=false;if(n){m=r[1].split("]:")[0]+"]";g=Math.floor(r[1].split("]:")[1].split("/")[0]);S=Math.floor(r[1].split("]:")[1].split("/")[1]/100);b=Math.floor(r[1].split("]:")[1].split("/")[1]%100)-1}else{m=r[1].split(":")[0];g=Math.floor(r[1].split(":")[1].split("/")[0]);S=Math.floor(r[1].split(":")[1].split("/")[1]/100);b=Math.floor(r[1].split(":")[1].split("/")[1]%100)-1;var s=r[1].split(":")[1].split("/")[2];if(s!==""&&s==="webSocketVideoCtrlProxy"){a=true}}if(S===0){b=0}p=p||{};var l="&sessionID=";if(p.token&&!p.playURL){l="&token="}var u=p.sessionID||p.session||(p.playURL?"":p.token)||"";var w=new window.WebSocket(o+"://"+m+":"+g+(p.mode?"/"+p.mode:"")+(a?"/webSocketVideoCtrlProxy":"")+"?version="+y[T]+"&cipherSuites="+y[k]+l+u+(p.proxy?"&proxy="+p.proxy:""));w.binaryType="arraybuffer";var _=c.default.v4();var P=-1;var f=new Promise(function(c,d){w.onopen=function(){if(!p.playURL&&!p.sessionID&&!p.deviceSerial&&!p.token){y[C].push(h.createClientObject(w,_,S,b));c(_)}};w.onmessage=function(e){if(typeof e.data==="string"){var t=JSON.parse(e.data);var i=y[x](_);if(t&&t.version&&t.cipherSuite){y[T]=t.version;y[k]=parseInt(t.cipherSuite,10);if(t&&t.PKD&&t.rand){y[C].push(M.createClientObject(w,_,t.PKD,t.rand,p))}else{var n="live://"+m+":"+g+"/"+S+"/"+b;if(y[k]===-1){y[C].push(R.createClientObject(w,_,n,p))}else{y[C].push(D.createClientObject(w,_,n))}}c(_);return}if(t&&t.sdp){var r=D.getMediaFromSdp(t.sdp);v({bHead:true,buf:r})}if(t&&t.cmd){if(t.cmd==="end"){v({type:"exception",cmd:t.cmd})}}if(t&&t.statusString){if(t.statusString.toLowerCase()==="ok"){if(y[C][i].resolve){y[C][i].resolve(t)}}if(t.statusString.toLowerCase()!=="ok"){var o=D.getError(t);if(i>-1){if(y[C][i].reject){y[C][i].reject(o)}}else{d(o)}}}}else{var a={};var s=new Uint8Array(e.data);P++;if(s.byteLength===64||s.byteLength===40){var l=-1;var u=s.byteLength;for(var f=0;f<u;f++){if(s[f]===73&&s[f+1]===77&&s[f+2]===75&&s[f+3]===72){l=f;break}}if(l!==-1){var h=s.slice(l,l+40);a={bHead:true,buf:h}}else{a={bHead:false,buf:s}}v(a)}else{if(P>0){a={bHead:false,buf:s};v(a)}}s=null;a=null;e=null}};w.onclose=function(){for(var e=0,t=y[C].length;e<t;e++){if(y[C][e].id===_){y[C][e].resolve();y[C].splice(e,1);setTimeout(function(){i()},200);break}}d()}});return f}},{key:"startPlay",value:function e(t,n,r){var o=this;var a=this[x](t);if(n&&r&&o[T]==="0.1"){n=n.replace(/-/g,"").replace(/:/g,"");r=r.replace(/-/g,"").replace(/:/g,"")}var i=new Promise(function(e,t){if(a>-1){o[C][a].resolve=e;o[C][a].reject=t;var i=null;if(!n||!r){if(o[C][a].iCurChannel===0&&o[T]==="0.1"){i=h.zeroPlayCmd(o[C][a].iCurChannel,o[C][a].iCurStream)}else{if(o[T]!=="0.1"){if(o[k]===0){i=M.playCmd(o[C][a])}else if(o[k]===1){i=D.playCmd(o[C][a].playURL)}else if(o[k]===-1){i=R.playCmd(o[C][a])}}else{i=h.playCmd(o[C][a].iCurChannel,o[C][a].iCurStream)}}}else{if(o[T]!=="0.1"){if(o[k]===0){i=M.playbackCmd(o[C][a],n,r)}else if(o[k]===1){i=D.playbackCmd(n,r,o[C][a].playURL)}else if(o[k]===-1){i=R.playbackCmd(o[C][a],n,r)}}else{i=h.playbackCmd(n,r,o[C][a].iCurChannel,o[C][a].iCurStream)}}o[C][a].socket.send(i);if(o[T]==="0.1"){e()}}else{if(o[T]==="0.1"){t()}}});return i}},{key:"singleFrame",value:function e(){}},{key:"setPlayRate",value:function e(a,s){var l=this;var t=new Promise(function(e,t){for(var i=0,n=l[C].length;i<n;i++){if(l[C][i].id===a){if(l[T]==="0.1"){var r=h.playRateCmd(s);l[C][i].socket.send(r);e();break}else{l[C][i].resolve=e;l[C][i].reject=t;var o=D.playRateCmd(s);l[C][i].socket.send(o)}}}});return t}},{key:"seek",value:function e(o,a,s){var l=this;var t=new Promise(function(e,t){for(var i=0,n=l[C].length;i<n;i++){if(l[C][i].id===o){l[C][i].resolve=e;l[C][i].reject=t;var r=M.seekCmd(a,s);l[C][i].socket.send(r)}}});return t}},{key:"pause",value:function e(a){var s=this;var t=new Promise(function(e,t){for(var i=0,n=s[C].length;i<n;i++){if(s[C][i].id===a){if(s[T]==="0.1"){var r=h.pauseCmd();s[C][i].socket.send(r);e();break}else{s[C][i].resolve=e;s[C][i].reject=t;var o=D.pauseCmd();s[C][i].socket.send(o)}}}});return t}},{key:"transmission",value:function e(r,o){var a=this;var t=new Promise(function(e,t){for(var i=0,n=a[C].length;i<n;i++){if(a[C][i].id===r){a[C][i].resolve=e;a[C][i].reject=t;a[C][i].socket.send(o)}}});return t}},{key:"resume",value:function e(a){var s=this;var t=new Promise(function(e,t){for(var i=0,n=s[C].length;i<n;i++){if(s[C][i].id===a){if(s[T]==="0.1"){var r=h.resumeCmd();s[C][i].socket.send(r);e();break}else{s[C][i].resolve=e;s[C][i].reject=t;var o=D.resumeCmd();s[C][i].socket.send(o)}}}});return t}},{key:"stop",value:function e(o){var a=this;var t=new Promise(function(e,t){if(!o){t()}else{var i=-1;for(var n=0,r=a[C].length;n<r;n++){if(a[C][n].id===o){i=n;a[C][n].resolve=e;a[C][n].socket.close(1e3,"CLOSE");break}}if(i===-1){t()}}});return t}},{key:"stopAll",value:function e(){var t=this;for(var i=0,n=t[C].length;i<n;i++){t[C][i].socket.close(1e3,"CLOSE")}}}]);return e}();return e}();t.StreamClient=h},function(e,t,i){var p=i(2);var v=i(3);var y;var m;var g=0;var S=0;function n(e,t,i){var n=t&&i||0;var r=t||[];e=e||{};var o=e.node||y;var a=e.clockseq!==undefined?e.clockseq:m;if(o==null||a==null){var s=p();if(o==null){o=y=[s[0]|1,s[1],s[2],s[3],s[4],s[5]]}if(a==null){a=m=(s[6]<<8|s[7])&16383}}var l=e.msecs!==undefined?e.msecs:(new Date).getTime();var u=e.nsecs!==undefined?e.nsecs:S+1;var f=l-g+(u-S)/1e4;if(f<0&&e.clockseq===undefined){a=a+1&16383}if((f<0||l>g)&&e.nsecs===undefined){u=0}if(u>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}g=l;S=u;m=a;l+=122192928e5;var h=((l&268435455)*1e4+u)%4294967296;r[n++]=h>>>24&255;r[n++]=h>>>16&255;r[n++]=h>>>8&255;r[n++]=h&255;var c=l/4294967296*1e4&268435455;r[n++]=c>>>8&255;r[n++]=c&255;r[n++]=c>>>24&15|16;r[n++]=c>>>16&255;r[n++]=a>>>8|128;r[n++]=a&255;for(var d=0;d<6;++d){r[n+d]=o[d]}return t?t:v(r)}e.exports=n},function(e,t,i){var a=i(2);var s=i(3);function n(e,t,i){var n=t&&i||0;if(typeof e=="string"){t=e==="binary"?new Array(16):null;e=null}e=e||{};var r=e.random||(e.rng||a)();r[6]=r[6]&15|64;r[8]=r[8]&63|128;if(t){for(var o=0;o<16;++o){t[n+o]=r[o]}}return t||s(r)}e.exports=n},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r){return{socket:t,id:i,iCurChannel:n,iCurStream:r,resolve:null,reject:null}}},{key:"zeroPlayCmd",value:function e(t,i){var n=[0,0,0,44,0,0,0,0,0,0,0,0,0,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t+1,0,0,0,i,0,0,4,0];return new Uint8Array(n)}},{key:"playCmd",value:function e(t,i){var n=[0,0,0,44,0,0,0,0,0,0,0,0,0,3,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,t,0,0,0,i,0,0,4,0];return new Uint8Array(n)}},{key:"playbackCmd",value:function e(t,i,n,r){var o=t.split("T")[0];var a=t.split("T")[1];var s="0"+parseInt(o.substring(0,4),10).toString(16);var l=parseInt(o.substring(4,6),10);var u=parseInt(o.substring(6),10);var f=parseInt(a.substring(0,2),10);var h=parseInt(a.substring(2,4),10);var c=parseInt(a.substring(4,6),10);var d=i.split("T")[0];var p=i.split("T")[1];var v="0"+parseInt(d.substring(0,4),10).toString(16);var y=parseInt(d.substring(4,6),10);var m=parseInt(p.substring(0,2),10);var g=parseInt(p.substring(2,4),10);var S=parseInt(p.substring(4,6),10);var b=[0,0,0,96,0,0,0,0,0,0,0,0,0,3,1,2,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,n,0,0,parseInt(s.substring(0,2),16),parseInt(s.substring(2,4),16),0,0,0,l,0,0,0,u,0,0,0,f,0,0,0,h,0,0,0,c,0,0,parseInt(v.substring(0,2),16),parseInt(v.substring(2,4),16),0,0,0,y,0,0,0,u,0,0,0,m,0,0,0,g,0,0,0,S,0,0,0,0,0,0,0,0,r,0,0,0];return new Uint8Array(b)}},{key:"playRateCmd",value:function e(t){var i=(parseInt(t,10)>>>0).toString(16).toLocaleUpperCase().toString(16);for(var n=i.length;n<8;n++){i="0"+i}var r=[0,0,0,0];for(var o=0,a=i.length;o<a;o=o+2){r[Math.floor(o/2)]=parseInt(i.substring(o,o+2),16)}var s=[0,0,0,36,0,0,0,0,0,0,0,0,0,3,1,47,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,r[0],r[1],r[2],r[3]];return new Uint8Array(s)}},{key:"pauseCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,7,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}},{key:"resumeCmd",value:function e(){var t=[0,0,0,32,0,0,0,0,0,0,0,0,0,3,1,8,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];return new Uint8Array(t)}}]);return e}();return e}();t.DirectDeviceCustom=o},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=3001;var a=3002;var s=3003;var l=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n){return{socket:t,id:i,playURL:n,resolve:null,reject:null}}},{key:"getMediaFromSdp",value:function e(t){var i=t.indexOf("MEDIAINFO=")+10;var n=t.slice(i,i+80);var r=[];for(var o=0,a=n.length/2;o<a;o++){r[o]=parseInt(n.slice(o*2,o*2+2),16)}return new Uint8Array(r)}},{key:"playCmd",value:function e(t){var i={sequence:0,cmd:"realplay",url:t};return JSON.stringify(i)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",url:n,startTime:t,endTime:i};return JSON.stringify(r)}},{key:"playRateCmd",value:function e(t){var i={sequence:0,cmd:"speed",rate:t};return JSON.stringify(i)}},{key:"pauseCmd",value:function e(){var t={sequence:0,cmd:"pause"};return JSON.stringify(t)}},{key:"resumeCmd",value:function e(){var t={sequence:0,cmd:"resume"};return JSON.stringify(t)}},{key:"getError",value:function e(t){var i=o;if(t){if(parseInt(t.statusCode,10)===6&&t.subStatusCode==="streamLimit"){i=a}else if(parseInt(t.statusCode,10)===4&&t.subStatusCode==="badAuthorization"){i=s}}return{iErrorNum:i,oError:t}}}]);return e}();return e}();t.DirectDevice=l},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.LiveMedia=undefined;var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();var r=i(13);var o=s(r);var a=i(14);var l=s(a);function s(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=function(){var e=function(){function e(){u(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r,o){var a=l.default.AES.encrypt((new Date).getTime().toString(),l.default.enc.Hex.parse("1234567891234567123456789123456712345678912345671234567891234567"),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse("12345678912345671234567891234567"),padding:l.default.pad.Pkcs7}).ciphertext.toString();if(a.length<64){a=a+a}var s=l.default.AES.encrypt((new Date).getTime().toString(),l.default.enc.Hex.parse("12345678912345671234567891234567"),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse("12345678912345671234567891234567"),padding:l.default.pad.Pkcs7}).ciphertext.toString();return{socket:t,id:i,PKD:n,rand:r,playURL:o.playURL||"",auth:o.auth||"",token:o.token||"",key:a,iv:s,resolve:null,reject:null}}},{key:"playCmd",value:function e(t){var i={sequence:0,cmd:"realplay",url:t.playURL,key:o.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:l.default.AES.encrypt(t.rand+":"+t.auth,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),token:l.default.AES.encrypt(t.token,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString()};return JSON.stringify(i)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",url:t.playURL,key:o.default.encrypt(t.iv+":"+t.key,t.PKD).cipher.split("?")[0],authorization:l.default.AES.encrypt(t.rand+":"+t.auth,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),token:l.default.AES.encrypt(t.token,l.default.enc.Hex.parse(t.key),{mode:l.default.mode.CBC,iv:l.default.enc.Hex.parse(t.iv),padding:l.default.pad.Pkcs7}).ciphertext.toString(),startTime:i,endTime:n};return JSON.stringify(r)}},{key:"seekCmd",value:function e(t,i){var n={sequence:0,cmd:"seek",startTime:t,endTime:i};return JSON.stringify(n)}}]);return e}();return e}();t.LiveMedia=f},function(e,t,n){"use strict";var p=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};var r={appName:"Netscape",appVersion:40};var o,a=0xdeadbeefcafe,s=(a&16777215)==15715070;function y(e,t,i){e!=null&&("number"==typeof e?this.fromNumber(e,t,i):t==null&&"string"!=typeof e?this.fromString(e,256):this.fromString(e,t))}function m(){return new y(null)}function l(e,t,i,n,r,o){for(;--o>=0;){var a=t*this[e++]+i[n]+r,r=Math.floor(a/67108864);i[n++]=a&67108863}return r}function u(e,t,i,n,r,o){var a=t&32767;for(t>>=15;--o>=0;){var s=this[e]&32767,l=this[e++]>>15,u=t*s+l*a,s=a*s+((u&32767)<<15)+i[n]+(r&1073741823),r=(s>>>30)+(u>>>15)+t*l+(r>>>30);i[n++]=s&1073741823}return r}function f(e,t,i,n,r,o){var a=t&16383;for(t>>=14;--o>=0;){var s=this[e]&16383,l=this[e++]>>14,u=t*s+l*a,s=a*s+((u&16383)<<14)+i[n]+r,r=(s>>28)+(u>>14)+t*l;i[n++]=s&268435455}return r}s&&r.appName=="Microsoft Internet Explorer"?(y.prototype.am=u,o=30):s&&r.appName!="Netscape"?(y.prototype.am=l,o=26):(y.prototype.am=f,o=28);y.prototype.DB=o;y.prototype.DM=(1<<o)-1;y.prototype.DV=1<<o;var h=52;y.prototype.FV=Math.pow(2,h);y.prototype.F1=h-o;y.prototype.F2=2*o-h;var c="0123456789abcdefghijklmnopqrstuvwxyz",d=[],g,S;g="0".charCodeAt(0);for(S=0;S<=9;++S){d[g++]=S}g="a".charCodeAt(0);for(S=10;S<36;++S){d[g++]=S}g="A".charCodeAt(0);for(S=10;S<36;++S){d[g++]=S}function b(e){return c.charAt(e)}function w(e,t){var i=d[e.charCodeAt(t)];return i==null?-1:i}function _(e){for(var t=this.t-1;t>=0;--t){e[t]=this[t]}e.t=this.t;e.s=this.s}function P(e){this.t=1;this.s=e<0?-1:0;e>0?this[0]=e:e<-1?this[0]=e+DV:this.t=0}function C(e){var t=m();t.fromInt(e);return t}function x(e,t){var i;if(t==16)i=4;else if(t==8)i=3;else if(t==256)i=8;else if(t==2)i=1;else if(t==32)i=5;else if(t==4)i=2;else{this.fromRadix(e,t);return}this.s=this.t=0;for(var n=e.length,r=!1,o=0;--n>=0;){var a=i==8?e[n]&255:w(e,n);a<0?e.charAt(n)=="-"&&(r=!0):(r=!1,o==0?this[this.t++]=a:o+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,o+=i,o>=this.DB&&(o-=this.DB))}if(i==8&&(e[0]&128)!=0)this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o);this.clamp();r&&y.ZERO.subTo(this,this)}function T(){for(var e=this.s&this.DM;this.t>0&&this[this.t-1]==e;){--this.t}}function k(e){if(this.s<0)return"-"+this.negate().toString(e);if(e==16)e=4;else if(e==8)e=3;else if(e==2)e=1;else if(e==32)e=5;else if(e==64)e=6;else if(e==4)e=2;else return this.toRadix(e);var t=(1<<e)-1,i,n=!1,r="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0){if(a<this.DB&&(i=this[o]>>a)>0)n=!0,r=b(i);for(;o>=0;){a<e?(i=(this[o]&(1<<a)-1)<<e-a,i|=this[--o]>>(a+=this.DB-e)):(i=this[o]>>(a-=e)&t,a<=0&&(a+=this.DB,--o)),i>0&&(n=!0),n&&(r+=b(i))}}return n?r:"0"}function D(){var e=m();y.ZERO.subTo(this,e);return e}function M(){return this.s<0?this.negate():this}function R(e){var t=this.s-e.s;if(t!=0)return t;var i=this.t,t=i-e.t;if(t!=0)return t;for(;--i>=0;){if((t=this[i]-e[i])!=0)return t}return 0}function E(e){var t=1,i;if((i=e>>>16)!=0)e=i,t+=16;if((i=e>>8)!=0)e=i,t+=8;if((i=e>>4)!=0)e=i,t+=4;if((i=e>>2)!=0)e=i,t+=2;e>>1!=0&&(t+=1);return t}function I(){return this.t<=0?0:this.DB*(this.t-1)+E(this[this.t-1]^this.s&this.DM)}function z(e,t){var i;for(i=this.t-1;i>=0;--i){t[i+e]=this[i]}for(i=e-1;i>=0;--i){t[i]=0}t.t=this.t+e;t.s=this.s}function A(e,t){for(var i=e;i<this.t;++i){t[i-e]=this[i]}t.t=Math.max(this.t-e,0);t.s=this.s}function O(e,t){var i=e%this.DB,n=this.DB-i,r=(1<<n)-1,o=Math.floor(e/this.DB),a=this.s<<i&this.DM,s;for(s=this.t-1;s>=0;--s){t[s+o+1]=this[s]>>n|a,a=(this[s]&r)<<i}for(s=o-1;s>=0;--s){t[s]=0}t[o]=a;t.t=this.t+o+1;t.s=this.s;t.clamp()}function F(e,t){t.s=this.s;var i=Math.floor(e/this.DB);if(i>=this.t)t.t=0;else{var n=e%this.DB,r=this.DB-n,o=(1<<n)-1;t[0]=this[i]>>n;for(var a=i+1;a<this.t;++a){t[a-i-1]|=(this[a]&o)<<r,t[a-i]=this[a]>>n}n>0&&(t[this.t-i-1]|=(this.s&o)<<r);t.t=this.t-i;t.clamp()}}function B(e,t){for(var i=0,n=0,r=Math.min(e.t,this.t);i<r;){n+=this[i]-e[i],t[i++]=n&this.DM,n>>=this.DB}if(e.t<this.t){for(n-=e.s;i<this.t;){n+=this[i],t[i++]=n&this.DM,n>>=this.DB}n+=this.s}else{for(n+=this.s;i<e.t;){n-=e[i],t[i++]=n&this.DM,n>>=this.DB}n-=e.s}t.s=n<0?-1:0;n<-1?t[i++]=this.DV+n:n>0&&(t[i++]=n);t.t=i;t.clamp()}function L(e,t){var i=this.abs(),n=e.abs(),r=i.t;for(t.t=r+n.t;--r>=0;){t[r]=0}for(r=0;r<n.t;++r){t[r+i.t]=i.am(0,n[r],t,r,0,i.t)}t.s=0;t.clamp();this.s!=e.s&&y.ZERO.subTo(t,t)}function q(e){for(var t=this.abs(),i=e.t=2*t.t;--i>=0;){e[i]=0}for(i=0;i<t.t-1;++i){var n=t.am(i,t[i],e,2*i,0,1);if((e[i+t.t]+=t.am(i+1,2*t[i],e,2*i+1,n,t.t-i-1))>=t.DV)e[i+t.t]-=t.DV,e[i+t.t+1]=1}e.t>0&&(e[e.t-1]+=t.am(i,t[i],e,2*i,0,1));e.s=0;e.clamp()}function N(e,t,i){var n=e.abs();if(!(n.t<=0)){var r=this.abs();if(r.t<n.t)t!=null&&t.fromInt(0),i!=null&&this.copyTo(i);else{i==null&&(i=m());var o=m(),a=this.s,e=e.s,s=this.DB-E(n[n.t-1]);s>0?(n.lShiftTo(s,o),r.lShiftTo(s,i)):(n.copyTo(o),r.copyTo(i));n=o.t;r=o[n-1];if(r!=0){var l=r*(1<<this.F1)+(n>1?o[n-2]>>this.F2:0),u=this.FV/l,l=(1<<this.F1)/l,f=1<<this.F2,h=i.t,c=h-n,d=t==null?m():t;o.dlShiftTo(c,d);i.compareTo(d)>=0&&(i[i.t++]=1,i.subTo(d,i));y.ONE.dlShiftTo(n,d);for(d.subTo(o,o);o.t<n;){o[o.t++]=0}for(;--c>=0;){var p=i[--h]==r?this.DM:Math.floor(i[h]*u+(i[h-1]+f)*l);if((i[h]+=o.am(0,p,i,c,0,n))<p){o.dlShiftTo(c,d);for(i.subTo(d,i);i[h]<--p;){i.subTo(d,i)}}}t!=null&&(i.drShiftTo(n,t),a!=e&&y.ZERO.subTo(t,t));i.t=n;i.clamp();s>0&&i.rShiftTo(s,i);a<0&&y.ZERO.subTo(i,i)}}}}function H(e){var t=m();this.abs().divRemTo(e,null,t);this.s<0&&t.compareTo(y.ZERO)>0&&e.subTo(t,t);return t}function j(e){this.m=e}function U(e){return e.s<0||e.compareTo(this.m)>=0?e.mod(this.m):e}function W(e){return e}function G(e){e.divRemTo(this.m,null,e)}function V(e,t,i){e.multiplyTo(t,i);this.reduce(i)}function Y(e,t){e.squareTo(t);this.reduce(t)}j.prototype.convert=U;j.prototype.revert=W;j.prototype.reduce=G;j.prototype.mulTo=V;j.prototype.sqrTo=Y;function X(){if(this.t<1)return 0;var e=this[0];if((e&1)==0)return 0;var t=e&3,t=t*(2-(e&15)*t)&15,t=t*(2-(e&255)*t)&255,t=t*(2-((e&65535)*t&65535))&65535,t=t*(2-e*t%this.DV)%this.DV;return t>0?this.DV-t:-t}function J(e){this.m=e;this.mp=e.invDigit();this.mpl=this.mp&32767;this.mph=this.mp>>15;this.um=(1<<e.DB-15)-1;this.mt2=2*e.t}function K(e){var t=m();e.abs().dlShiftTo(this.m.t,t);t.divRemTo(this.m,null,t);e.s<0&&t.compareTo(y.ZERO)>0&&this.m.subTo(t,t);return t}function $(e){var t=m();e.copyTo(t);this.reduce(t);return t}function Z(e){for(;e.t<=this.mt2;){e[e.t++]=0}for(var t=0;t<this.m.t;++t){var i=e[t]&32767,n=i*this.mpl+((i*this.mph+(e[t]>>15)*this.mpl&this.um)<<15)&e.DM,i=t+this.m.t;for(e[i]+=this.m.am(0,n,e,t,0,this.m.t);e[i]>=e.DV;){e[i]-=e.DV,e[++i]++}}e.clamp();e.drShiftTo(this.m.t,e);e.compareTo(this.m)>=0&&e.subTo(this.m,e)}function Q(e,t){e.squareTo(t);this.reduce(t)}function ee(e,t,i){e.multiplyTo(t,i);this.reduce(i)}J.prototype.convert=K;J.prototype.revert=$;J.prototype.reduce=Z;J.prototype.mulTo=ee;J.prototype.sqrTo=Q;function te(){return(this.t>0?this[0]&1:this.s)==0}function ie(e,t){if(e>4294967295||e<1)return y.ONE;var i=m(),n=m(),r=t.convert(this),o=E(e)-1;for(r.copyTo(i);--o>=0;){if(t.sqrTo(i,n),(e&1<<o)>0)t.mulTo(n,r,i);else var a=i,i=n,n=a}return t.revert(i)}function ne(e,t){var i;i=e<256||t.isEven()?new j(t):new J(t);return this.exp(e,i)}y.prototype.copyTo=_;y.prototype.fromInt=P;y.prototype.fromString=x;y.prototype.clamp=T;y.prototype.dlShiftTo=z;y.prototype.drShiftTo=A;y.prototype.lShiftTo=O;y.prototype.rShiftTo=F;y.prototype.subTo=B;y.prototype.multiplyTo=L;y.prototype.squareTo=q;y.prototype.divRemTo=N;y.prototype.invDigit=X;y.prototype.isEven=te;y.prototype.exp=ie;y.prototype.toString=k;y.prototype.negate=D;y.prototype.abs=M;y.prototype.compareTo=R;y.prototype.bitLength=I;y.prototype.mod=H;y.prototype.modPowInt=ne;y.ZERO=C(0);y.ONE=C(1);function re(){var e=m();this.copyTo(e);return e}function oe(){if(this.s<0){if(this.t==1)return this[0]-this.DV;else{if(this.t==0)return-1}}else if(this.t==1)return this[0];else if(this.t==0)return 0;return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]}function ae(){return this.t==0?this.s:this[0]<<24>>24}function se(){return this.t==0?this.s:this[0]<<16>>16}function le(e){return Math.floor(Math.LN2*this.DB/Math.log(e))}function ue(){return this.s<0?-1:this.t<=0||this.t==1&&this[0]<=0?0:1}function fe(e){e==null&&(e=10);if(this.signum()==0||e<2||e>36)return"0";var t=this.chunkSize(e),t=Math.pow(e,t),i=C(t),n=m(),r=m(),o="";for(this.divRemTo(i,n,r);n.signum()>0;){o=(t+r.intValue()).toString(e).substr(1)+o,n.divRemTo(i,n,r)}return r.intValue().toString(e)+o}function he(e,t){this.fromInt(0);t==null&&(t=10);for(var i=this.chunkSize(t),n=Math.pow(t,i),r=!1,o=0,a=0,s=0;s<e.length;++s){var l=w(e,s);l<0?e.charAt(s)=="-"&&this.signum()==0&&(r=!0):(a=t*a+l,++o>=i&&(this.dMultiply(n),this.dAddOffset(a,0),a=o=0))}o>0&&(this.dMultiply(Math.pow(t,o)),this.dAddOffset(a,0));r&&y.ZERO.subTo(this,this)}function ce(e,t,i){if("number"==typeof t){if(e<2)this.fromInt(1);else{this.fromNumber(e,i);this.testBit(e-1)||this.bitwiseTo(y.ONE.shiftLeft(e-1),be,this);for(this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(t);){this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(y.ONE.shiftLeft(e-1),this)}}}else{var i=[],n=e&7;i.length=(e>>3)+1;t.nextBytes(i);n>0?i[0]&=(1<<n)-1:i[0]=0;this.fromString(i,256)}}function de(){var e=this.t,t=[];t[0]=this.s;var i=this.DB-e*this.DB%8,n,r=0;if(e-- >0){if(i<this.DB&&(n=this[e]>>i)!=(this.s&this.DM)>>i)t[r++]=n|this.s<<this.DB-i;for(;e>=0;){if(i<8?(n=(this[e]&(1<<i)-1)<<8-i,n|=this[--e]>>(i+=this.DB-8)):(n=this[e]>>(i-=8)&255,i<=0&&(i+=this.DB,--e)),(n&128)!=0&&(n|=-256),r==0&&(this.s&128)!=(n&128)&&++r,r>0||n!=this.s)t[r++]=n}}return t}function pe(e){return this.compareTo(e)==0}function ve(e){return this.compareTo(e)<0?this:e}function ye(e){return this.compareTo(e)>0?this:e}function me(e,t,i){var n,r,o=Math.min(e.t,this.t);for(n=0;n<o;++n){i[n]=t(this[n],e[n])}if(e.t<this.t){r=e.s&this.DM;for(n=o;n<this.t;++n){i[n]=t(this[n],r)}i.t=this.t}else{r=this.s&this.DM;for(n=o;n<e.t;++n){i[n]=t(r,e[n])}i.t=e.t}i.s=t(this.s,e.s);i.clamp()}function ge(e,t){return e&t}function Se(e){var t=m();this.bitwiseTo(e,ge,t);return t}function be(e,t){return e|t}function we(e){var t=m();this.bitwiseTo(e,be,t);return t}function _e(e,t){return e^t}function Pe(e){var t=m();this.bitwiseTo(e,_e,t);return t}function Ce(e,t){return e&~t}function xe(e){var t=m();this.bitwiseTo(e,Ce,t);return t}function Te(){for(var e=m(),t=0;t<this.t;++t){e[t]=this.DM&~this[t]}e.t=this.t;e.s=~this.s;return e}function ke(e){var t=m();e<0?this.rShiftTo(-e,t):this.lShiftTo(e,t);return t}function De(e){var t=m();e<0?this.lShiftTo(-e,t):this.rShiftTo(e,t);return t}function Me(e){if(e==0)return-1;var t=0;(e&65535)==0&&(e>>=16,t+=16);(e&255)==0&&(e>>=8,t+=8);(e&15)==0&&(e>>=4,t+=4);(e&3)==0&&(e>>=2,t+=2);(e&1)==0&&++t;return t}function Re(){for(var e=0;e<this.t;++e){if(this[e]!=0)return e*this.DB+Me(this[e])}return this.s<0?this.t*this.DB:-1}function Ee(e){for(var t=0;e!=0;){e&=e-1,++t}return t}function Ie(){for(var e=0,t=this.s&this.DM,i=0;i<this.t;++i){e+=Ee(this[i]^t)}return e}function ze(e){var t=Math.floor(e/this.DB);return t>=this.t?this.s!=0:(this[t]&1<<e%this.DB)!=0}function Ae(e,t){var i=y.ONE.shiftLeft(e);this.bitwiseTo(i,t,i);return i}function Oe(e){return this.changeBit(e,be)}function Fe(e){return this.changeBit(e,Ce)}function Be(e){return this.changeBit(e,_e)}function Le(e,t){for(var i=0,n=0,r=Math.min(e.t,this.t);i<r;){n+=this[i]+e[i],t[i++]=n&this.DM,n>>=this.DB}if(e.t<this.t){for(n+=e.s;i<this.t;){n+=this[i],t[i++]=n&this.DM,n>>=this.DB}n+=this.s}else{for(n+=this.s;i<e.t;){n+=e[i],t[i++]=n&this.DM,n>>=this.DB}n+=e.s}t.s=n<0?-1:0;n>0?t[i++]=n:n<-1&&(t[i++]=this.DV+n);t.t=i;t.clamp()}function qe(e){var t=m();this.addTo(e,t);return t}function Ne(e){var t=m();this.subTo(e,t);return t}function He(e){var t=m();this.multiplyTo(e,t);return t}function je(){var e=m();this.squareTo(e);return e}function Ue(e){var t=m();this.divRemTo(e,t,null);return t}function We(e){var t=m();this.divRemTo(e,null,t);return t}function Ge(e){var t=m(),i=m();this.divRemTo(e,t,i);return[t,i]}function Ve(e){this[this.t]=this.am(0,e-1,this,0,0,this.t);++this.t;this.clamp()}function Ye(e,t){if(e!=0){for(;this.t<=t;){this[this.t++]=0}for(this[t]+=e;this[t]>=this.DV;){this[t]-=this.DV,++t>=this.t&&(this[this.t++]=0),++this[t]}}}function Xe(){}function Je(e){return e}function Ke(e,t,i){e.multiplyTo(t,i)}function $e(e,t){e.squareTo(t)}Xe.prototype.convert=Je;Xe.prototype.revert=Je;Xe.prototype.mulTo=Ke;Xe.prototype.sqrTo=$e;function Ze(e){return this.exp(e,new Xe)}function Qe(e,t,i){var n=Math.min(this.t+e.t,t);i.s=0;for(i.t=n;n>0;){i[--n]=0}var r;for(r=i.t-this.t;n<r;++n){i[n+this.t]=this.am(0,e[n],i,n,0,this.t)}for(r=Math.min(e.t,t);n<r;++n){this.am(0,e[n],i,n,0,t-n)}i.clamp()}function et(e,t,i){--t;var n=i.t=this.t+e.t-t;for(i.s=0;--n>=0;){i[n]=0}for(n=Math.max(t-this.t,0);n<e.t;++n){i[this.t+n-t]=this.am(t-n,e[n],i,0,0,this.t+n-t)}i.clamp();i.drShiftTo(1,i)}function tt(e){this.r2=m();this.q3=m();y.ONE.dlShiftTo(2*e.t,this.r2);this.mu=this.r2.divide(e);this.m=e}function it(e){if(e.s<0||e.t>2*this.m.t)return e.mod(this.m);else if(e.compareTo(this.m)<0)return e;else{var t=m();e.copyTo(t);this.reduce(t);return t}}function nt(e){return e}function rt(e){e.drShiftTo(this.m.t-1,this.r2);if(e.t>this.m.t+1)e.t=this.m.t+1,e.clamp();this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3);for(this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);e.compareTo(this.r2)<0;){e.dAddOffset(1,this.m.t+1)}for(e.subTo(this.r2,e);e.compareTo(this.m)>=0;){e.subTo(this.m,e)}}function ot(e,t){e.squareTo(t);this.reduce(t)}function at(e,t,i){e.multiplyTo(t,i);this.reduce(i)}tt.prototype.convert=it;tt.prototype.revert=nt;tt.prototype.reduce=rt;tt.prototype.mulTo=at;tt.prototype.sqrTo=ot;function st(e,t){var i=e.bitLength(),n,r=C(1),o;if(i<=0)return r;else n=i<18?1:i<48?3:i<144?4:i<768?5:6;o=i<8?new j(t):t.isEven()?new tt(t):new J(t);var a=[],s=3,l=n-1,u=(1<<n)-1;a[1]=o.convert(this);if(n>1){i=m();for(o.sqrTo(a[1],i);s<=u;){a[s]=m(),o.mulTo(i,a[s-2],a[s]),s+=2}}for(var f=e.t-1,h,c=!0,d=m(),i=E(e[f])-1;f>=0;){i>=l?h=e[f]>>i-l&u:(h=(e[f]&(1<<i+1)-1)<<l-i,f>0&&(h|=e[f-1]>>this.DB+i-l));for(s=n;(h&1)==0;){h>>=1,--s}if((i-=s)<0)i+=this.DB,--f;if(c)a[h].copyTo(r),c=!1;else{for(;s>1;){o.sqrTo(r,d),o.sqrTo(d,r),s-=2}s>0?o.sqrTo(r,d):(s=r,r=d,d=s);o.mulTo(d,a[h],r)}for(;f>=0&&(e[f]&1<<i)==0;){o.sqrTo(r,d),s=r,r=d,d=s,--i<0&&(i=this.DB-1,--f)}}return o.revert(r)}function lt(e){var t=this.s<0?this.negate():this.clone(),e=e.s<0?e.negate():e.clone();if(t.compareTo(e)<0)var i=t,t=e,e=i;var i=t.getLowestSetBit(),n=e.getLowestSetBit();if(n<0)return t;i<n&&(n=i);n>0&&(t.rShiftTo(n,t),e.rShiftTo(n,e));for(;t.signum()>0;){(i=t.getLowestSetBit())>0&&t.rShiftTo(i,t),(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),t.compareTo(e)>=0?(t.subTo(e,t),t.rShiftTo(1,t)):(e.subTo(t,e),e.rShiftTo(1,e))}n>0&&e.lShiftTo(n,e);return e}function ut(e){if(e<=0)return 0;var t=this.DV%e,i=this.s<0?e-1:0;if(this.t>0)if(t==0)i=this[0]%e;else for(var n=this.t-1;n>=0;--n){i=(t*i+this[n])%e}return i}function ft(e){var t=e.isEven();if(this.isEven()&&t||e.signum()==0)return y.ZERO;for(var i=e.clone(),n=this.clone(),r=C(1),o=C(0),a=C(0),s=C(1);i.signum()!=0;){for(;i.isEven();){i.rShiftTo(1,i);if(t){if(!r.isEven()||!o.isEven())r.addTo(this,r),o.subTo(e,o);r.rShiftTo(1,r)}else o.isEven()||o.subTo(e,o);o.rShiftTo(1,o)}for(;n.isEven();){n.rShiftTo(1,n);if(t){if(!a.isEven()||!s.isEven())a.addTo(this,a),s.subTo(e,s);a.rShiftTo(1,a)}else s.isEven()||s.subTo(e,s);s.rShiftTo(1,s)}i.compareTo(n)>=0?(i.subTo(n,i),t&&r.subTo(a,r),o.subTo(s,o)):(n.subTo(i,n),t&&a.subTo(r,a),s.subTo(o,s))}if(n.compareTo(y.ONE)!=0)return y.ZERO;if(s.compareTo(e)>=0)return s.subtract(e);if(s.signum()<0)s.addTo(e,s);else return s;return s.signum()<0?s.add(e):s}var ht=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],ct=67108864/ht[ht.length-1];function dt(e){var t,i=this.abs();if(i.t==1&&i[0]<=ht[ht.length-1]){for(t=0;t<ht.length;++t){if(i[0]==ht[t])return!0}return!1}if(i.isEven())return!1;for(t=1;t<ht.length;){for(var n=ht[t],r=t+1;r<ht.length&&n<ct;){n*=ht[r++]}for(n=i.modInt(n);t<r;){if(n%ht[t++]==0)return!1}}return i.millerRabin(e)}function pt(e){var t=this.subtract(y.ONE),i=t.getLowestSetBit();if(i<=0)return!1;var n=t.shiftRight(i),e=e+1>>1;if(e>ht.length)e=ht.length;for(var r=m(),o=0;o<e;++o){r.fromInt(ht[Math.floor(Math.random()*ht.length)]);var a=r.modPow(n,this);if(a.compareTo(y.ONE)!=0&&a.compareTo(t)!=0){for(var s=1;s++<i&&a.compareTo(t)!=0;){if(a=a.modPowInt(2,this),a.compareTo(y.ONE)==0)return!1}if(a.compareTo(t)!=0)return!1}}return!0}y.prototype.chunkSize=le;y.prototype.toRadix=fe;y.prototype.fromRadix=he;y.prototype.fromNumber=ce;y.prototype.bitwiseTo=me;y.prototype.changeBit=Ae;y.prototype.addTo=Le;y.prototype.dMultiply=Ve;y.prototype.dAddOffset=Ye;y.prototype.multiplyLowerTo=Qe;y.prototype.multiplyUpperTo=et;y.prototype.modInt=ut;y.prototype.millerRabin=pt;y.prototype.clone=re;y.prototype.intValue=oe;y.prototype.byteValue=ae;y.prototype.shortValue=se;y.prototype.signum=ue;y.prototype.toByteArray=de;y.prototype.equals=pe;y.prototype.min=ve;y.prototype.max=ye;y.prototype.and=Se;y.prototype.or=we;y.prototype.xor=Pe;y.prototype.andNot=xe;y.prototype.not=Te;y.prototype.shiftLeft=ke;y.prototype.shiftRight=De;y.prototype.getLowestSetBit=Re;y.prototype.bitCount=Ie;y.prototype.testBit=ze;y.prototype.setBit=Oe;y.prototype.clearBit=Fe;y.prototype.flipBit=Be;y.prototype.add=qe;y.prototype.subtract=Ne;y.prototype.multiply=He;y.prototype.divide=Ue;y.prototype.remainder=We;y.prototype.divideAndRemainder=Ge;y.prototype.modPow=st;y.prototype.modInverse=ft;y.prototype.pow=Ze;y.prototype.gcd=lt;y.prototype.isProbablePrime=dt;y.prototype.square=je;(function(r,o,l,a,s,u,f){function h(e){var t,i,s=this,n=e.length,r=0,o=s.i=s.j=s.m=0;s.S=[];s.c=[];for(n||(e=[n++]);r<l;){s.S[r]=r++}for(r=0;r<l;r++){t=s.S[r],o=o+t+e[r%n]&l-1,i=s.S[o],s.S[r]=i,s.S[o]=t}s.g=function(e){var t=s.S,i=s.i+1&l-1,n=t[i],r=s.j+n&l-1,o=t[r];t[i]=o;t[r]=n;for(var a=t[n+o&l-1];--e;){i=i+1&l-1,n=t[i],r=r+n&l-1,o=t[r],t[i]=o,t[r]=n,a=a*l+t[n+o&l-1]}s.i=i;s.j=r;return a};s.g(l)}function c(e,t,i,n,r){i=[];r=typeof e==="undefined"?"undefined":p(e);if(t&&r=="object")for(n in e){if(n.indexOf("S")<5)try{i.push(c(e[n],t-1))}catch(e){}}return i.length?i:e+(r!="string"?"\0":"")}function d(e,t,i,n){e+="";for(n=i=0;n<e.length;n++){var r=t,o=n&l-1,a=(i^=t[n&l-1]*19)+e.charCodeAt(n);r[o]=a&l-1}e="";for(n in t){e+=String.fromCharCode(t[n])}return e}o.seedrandom=function(e,t){var i=[],n,e=d(c(t?[e,r]:arguments.length?e:[(new Date).getTime(),r,window],3),i);n=new h(i);d(n.S,r);o.random=function(){for(var e=n.g(a),t=f,i=0;e<s;){e=(e+i)*l,t*=l,i=n.g(1)}for(;e>=u;){e/=2,t/=2,i>>>=1}return(e+i)/t};return e};f=o.pow(l,a);s=o.pow(2,s);u=s*2;d(o.random(),r)})([],Math,256,6,52);function vt(){}function yt(e){var t;for(t=0;t<e.length;t++){e[t]=Math.floor(Math.random()*256)}}vt.prototype.nextBytes=yt;function mt(){this.j=this.i=0;this.S=[]}function gt(e){var t,i,n;for(t=0;t<256;++t){this.S[t]=t}for(t=i=0;t<256;++t){i=i+this.S[t]+e[t%e.length]&255,n=this.S[t],this.S[t]=this.S[i],this.S[i]=n}this.j=this.i=0}function St(){var e;this.i=this.i+1&255;this.j=this.j+this.S[this.i]&255;e=this.S[this.i];this.S[this.i]=this.S[this.j];this.S[this.j]=e;return this.S[e+this.S[this.i]&255]}mt.prototype.init=gt;mt.prototype.next=St;function bt(){return new mt}var wt=256,_t,Pt,Ct;function xt(e){Pt[Ct++]^=e&255;Pt[Ct++]^=e>>8&255;Pt[Ct++]^=e>>16&255;Pt[Ct++]^=e>>24&255;Ct>=wt&&(Ct-=wt)}function Tt(){xt((new Date).getTime())}if(Pt==null){Pt=[];Ct=0;var kt;if(r.appName=="Netscape"&&r.appVersion<"5"&&window.crypto){var Dt=window.crypto.random(32);for(kt=0;kt<Dt.length;++kt){Pt[Ct++]=Dt.charCodeAt(kt)&255}}for(;Ct<wt;){kt=Math.floor(65536*Math.random()),Pt[Ct++]=kt>>>8,Pt[Ct++]=kt&255}Ct=0;Tt()}function Mt(){if(_t==null){Tt();_t=bt();_t.init(Pt);for(Ct=0;Ct<Pt.length;++Ct){Pt[Ct]=0}Ct=0}return _t.next()}function Rt(e){var t;for(t=0;t<e.length;++t){e[t]=Mt()}}function Et(){}Et.prototype.nextBytes=Rt;function It(e){function m(e,t){var i=(e&65535)+(t&65535);return(e>>16)+(t>>16)+(i>>16)<<16|i&65535}function g(e,t){return e>>>t|e<<32-t}e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(e);return function(e){for(var t="",i=0;i<e.length*4;i++){t+="0123456789abcdef".charAt(e[i>>2]>>(3-i%4)*8+4&15)+"0123456789abcdef".charAt(e[i>>2]>>(3-i%4)*8&15)}return t}(function(e,t){var i=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],n=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],r=Array(64),o,a,s,l,u,f,h,c,d,p,v,y;e[t>>5]|=128<<24-t%32;e[(t+64>>9<<4)+15]=t;for(d=0;d<e.length;d+=16){o=n[0];a=n[1];s=n[2];l=n[3];u=n[4];f=n[5];h=n[6];c=n[7];for(p=0;p<64;p++){r[p]=p<16?e[p+d]:m(m(m(g(r[p-2],17)^g(r[p-2],19)^r[p-2]>>>10,r[p-7]),g(r[p-15],7)^g(r[p-15],18)^r[p-15]>>>3),r[p-16]),v=m(m(m(m(c,g(u,6)^g(u,11)^g(u,25)),u&f^~u&h),i[p]),r[p]),y=m(g(o,2)^g(o,13)^g(o,22),o&a^o&s^a&s),c=h,h=f,f=u,u=m(l,v),l=s,s=a,a=o,o=m(v,y)}n[0]=m(o,n[0]);n[1]=m(a,n[1]);n[2]=m(s,n[2]);n[3]=m(l,n[3]);n[4]=m(u,n[4]);n[5]=m(f,n[5]);n[6]=m(h,n[6]);n[7]=m(c,n[7])}return n}(function(e){for(var t=[],i=0;i<e.length*8;i+=8){t[i>>5]|=(e.charCodeAt(i/8)&255)<<24-i%32}return t}(e),e.length*8))}var zt={hex:function e(t){return It(t)}};function At(e){function t(e,t){return e<<t|e>>>32-t}function i(e){var t="",i,n;for(i=7;i>=0;i--){n=e>>>i*4&15,t+=n.toString(16)}return t}var n,r,o=Array(80),a=1732584193,s=4023233417,l=2562383102,u=271733878,f=3285377520,h,c,d,p,v,e=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(e);h=e.length;var y=[];for(n=0;n<h-3;n+=4){r=e.charCodeAt(n)<<24|e.charCodeAt(n+1)<<16|e.charCodeAt(n+2)<<8|e.charCodeAt(n+3),y.push(r)}switch(h%4){case 0:n=2147483648;break;case 1:n=e.charCodeAt(h-1)<<24|8388608;break;case 2:n=e.charCodeAt(h-2)<<24|e.charCodeAt(h-1)<<16|32768;break;case 3:n=e.charCodeAt(h-3)<<24|e.charCodeAt(h-2)<<16|e.charCodeAt(h-1)<<8|128}for(y.push(n);y.length%16!=14;){y.push(0)}y.push(h>>>29);y.push(h<<3&4294967295);for(e=0;e<y.length;e+=16){for(n=0;n<16;n++){o[n]=y[e+n]}for(n=16;n<=79;n++){o[n]=t(o[n-3]^o[n-8]^o[n-14]^o[n-16],1)}r=a;h=s;c=l;d=u;p=f;for(n=0;n<=19;n++){v=t(r,5)+(h&c|~h&d)+p+o[n]+1518500249&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(n=20;n<=39;n++){v=t(r,5)+(h^c^d)+p+o[n]+1859775393&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(n=40;n<=59;n++){v=t(r,5)+(h&c|h&d|c&d)+p+o[n]+2400959708&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}for(n=60;n<=79;n++){v=t(r,5)+(h^c^d)+p+o[n]+3395469782&4294967295,p=d,d=c,c=t(h,30),h=r,r=v}a=a+r&4294967295;s=s+h&4294967295;l=l+c&4294967295;u=u+d&4294967295;f=f+p&4294967295}v=i(a)+i(s)+i(l)+i(u)+i(f);return v.toLowerCase()}var Ot={hex:function e(t){return At(t)}},Ft=function e(t){function s(e,t){var i,n,r,o,a;r=e&2147483648;o=t&2147483648;i=e&1073741824;n=t&1073741824;a=(e&1073741823)+(t&1073741823);return i&n?a^2147483648^r^o:i|n?a&1073741824?a^3221225472^r^o:a^1073741824^r^o:a^r^o}function i(e,t,i,n,r,o,a){e=s(e,s(s(t&i|~t&n,r),a));return s(e<<o|e>>>32-o,t)}function n(e,t,i,n,r,o,a){e=s(e,s(s(t&n|i&~n,r),a));return s(e<<o|e>>>32-o,t)}function r(e,t,i,n,r,o,a){e=s(e,s(s(t^i^n,r),a));return s(e<<o|e>>>32-o,t)}function o(e,t,i,n,r,o,a){e=s(e,s(s(i^(t|~n),r),a));return s(e<<o|e>>>32-o,t)}function a(e){var t="",i="",n;for(n=0;n<=3;n++){i=e>>>n*8&255,i="0"+i.toString(16),t+=i.substr(i.length-2,2)}return t}var l=[],u,f,h,c,d,p,v,y,t=function(e){for(var e=e.replace(/\r\n/g,"\n"),t="",i=0;i<e.length;i++){var n=e.charCodeAt(i);n<128?t+=String.fromCharCode(n):(n>127&&n<2048?t+=String.fromCharCode(n>>6|192):(t+=String.fromCharCode(n>>12|224),t+=String.fromCharCode(n>>6&63|128)),t+=String.fromCharCode(n&63|128))}return t}(t),l=function(e){var t,i=e.length;t=i+8;for(var n=((t-t%64)/64+1)*16,r=Array(n-1),o=0,a=0;a<i;){t=(a-a%4)/4,o=a%4*8,r[t]|=e.charCodeAt(a)<<o,a++}r[(a-a%4)/4]|=128<<a%4*8;r[n-2]=i<<3;r[n-1]=i>>>29;return r}(t);d=1732584193;p=4023233417;v=2562383102;y=271733878;for(t=0;t<l.length;t+=16){u=d,f=p,h=v,c=y,d=i(d,p,v,y,l[t+0],7,3614090360),y=i(y,d,p,v,l[t+1],12,3905402710),v=i(v,y,d,p,l[t+2],17,606105819),p=i(p,v,y,d,l[t+3],22,3250441966),d=i(d,p,v,y,l[t+4],7,4118548399),y=i(y,d,p,v,l[t+5],12,1200080426),v=i(v,y,d,p,l[t+6],17,2821735955),p=i(p,v,y,d,l[t+7],22,4249261313),d=i(d,p,v,y,l[t+8],7,1770035416),y=i(y,d,p,v,l[t+9],12,2336552879),v=i(v,y,d,p,l[t+10],17,4294925233),p=i(p,v,y,d,l[t+11],22,2304563134),d=i(d,p,v,y,l[t+12],7,1804603682),y=i(y,d,p,v,l[t+13],12,4254626195),v=i(v,y,d,p,l[t+14],17,2792965006),p=i(p,v,y,d,l[t+15],22,1236535329),d=n(d,p,v,y,l[t+1],5,4129170786),y=n(y,d,p,v,l[t+6],9,3225465664),v=n(v,y,d,p,l[t+11],14,643717713),p=n(p,v,y,d,l[t+0],20,3921069994),d=n(d,p,v,y,l[t+5],5,3593408605),y=n(y,d,p,v,l[t+10],9,38016083),v=n(v,y,d,p,l[t+15],14,3634488961),p=n(p,v,y,d,l[t+4],20,3889429448),d=n(d,p,v,y,l[t+9],5,568446438),y=n(y,d,p,v,l[t+14],9,3275163606),v=n(v,y,d,p,l[t+3],14,4107603335),p=n(p,v,y,d,l[t+8],20,1163531501),d=n(d,p,v,y,l[t+13],5,2850285829),y=n(y,d,p,v,l[t+2],9,4243563512),v=n(v,y,d,p,l[t+7],14,1735328473),p=n(p,v,y,d,l[t+12],20,2368359562),d=r(d,p,v,y,l[t+5],4,4294588738),y=r(y,d,p,v,l[t+8],11,2272392833),v=r(v,y,d,p,l[t+11],16,1839030562),p=r(p,v,y,d,l[t+14],23,4259657740),d=r(d,p,v,y,l[t+1],4,2763975236),y=r(y,d,p,v,l[t+4],11,1272893353),v=r(v,y,d,p,l[t+7],16,4139469664),p=r(p,v,y,d,l[t+10],23,3200236656),d=r(d,p,v,y,l[t+13],4,681279174),y=r(y,d,p,v,l[t+0],11,3936430074),v=r(v,y,d,p,l[t+3],16,3572445317),p=r(p,v,y,d,l[t+6],23,76029189),d=r(d,p,v,y,l[t+9],4,3654602809),y=r(y,d,p,v,l[t+12],11,3873151461),v=r(v,y,d,p,l[t+15],16,530742520),p=r(p,v,y,d,l[t+2],23,3299628645),d=o(d,p,v,y,l[t+0],6,4096336452),y=o(y,d,p,v,l[t+7],10,1126891415),v=o(v,y,d,p,l[t+14],15,2878612391),p=o(p,v,y,d,l[t+5],21,4237533241),d=o(d,p,v,y,l[t+12],6,1700485571),y=o(y,d,p,v,l[t+3],10,2399980690),v=o(v,y,d,p,l[t+10],15,4293915773),p=o(p,v,y,d,l[t+1],21,2240044497),d=o(d,p,v,y,l[t+8],6,1873313359),y=o(y,d,p,v,l[t+15],10,4264355552),v=o(v,y,d,p,l[t+6],15,2734768916),p=o(p,v,y,d,l[t+13],21,1309151649),d=o(d,p,v,y,l[t+4],6,4149444226),y=o(y,d,p,v,l[t+11],10,3174756917),v=o(v,y,d,p,l[t+2],15,718787259),p=o(p,v,y,d,l[t+9],21,3951481745),d=s(d,u),p=s(p,f),v=s(v,h),y=s(y,c)}return(a(d)+a(p)+a(v)+a(y)).toLowerCase()};function Bt(e,t){return new y(e,t)}function Lt(e,t){for(var i="",n=0;n+t<e.length;){i+=e.substring(n,n+t)+"\n",n+=t}return i+e.substring(n,e.length)}function qt(e){return e<16?"0"+e.toString(16):e.toString(16)}function Nt(e,t){if(t<e.length+11)throw"Message too long for RSA (n="+t+", l="+e.length+")";for(var i=[],n=e.length-1;n>=0&&t>0;){var r=e.charCodeAt(n--);r<128?i[--t]=r:r>127&&r<2048?(i[--t]=r&63|128,i[--t]=r>>6|192):(i[--t]=r&63|128,i[--t]=r>>6&63|128,i[--t]=r>>12|224)}i[--t]=0;n=new Et;for(r=[];t>2;){for(r[0]=0;r[0]==0;){n.nextBytes(r)}i[--t]=r[0]}i[--t]=2;i[--t]=0;return new y(i)}function Ht(){this.n=null;this.e=0;this.coeff=this.dmq1=this.dmp1=this.q=this.p=this.d=null}function jt(e,t){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=Bt(e,16),this.e=parseInt(t,16)):alert("Invalid RSA public key")}function Ut(e){return e.modPowInt(this.e,this.n)}function Wt(e){e=Nt(e,this.n.bitLength()+7>>3);if(e==null)return null;e=this.doPublic(e);if(e==null)return null;e=e.toString(16);return(e.length&1)==0?e:"0"+e}Ht.prototype.doPublic=Ut;Ht.prototype.setPublic=jt;Ht.prototype.encrypt=Wt;function Gt(e,t){for(var i=e.toByteArray(),n=0;n<i.length&&i[n]==0;){++n}if(i.length-n!=t-1||i[n]!=2)return null;for(++n;i[n]!=0;){if(++n>=i.length)return null}for(var r="";++n<i.length;){var o=i[n]&255;o<128?r+=String.fromCharCode(o):o>191&&o<224?(r+=String.fromCharCode((o&31)<<6|i[n+1]&63),++n):(r+=String.fromCharCode((o&15)<<12|(i[n+1]&63)<<6|i[n+2]&63),n+=2)}return r}function Vt(e,t,i){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=Bt(e,16),this.e=parseInt(t,16),this.d=Bt(i,16)):alert("Invalid RSA private key")}function Yt(e,t,i,n,r,o,a,s){e!=null&&t!=null&&e.length>0&&t.length>0?(this.n=Bt(e,16),this.e=parseInt(t,16),this.d=Bt(i,16),this.p=Bt(n,16),this.q=Bt(r,16),this.dmp1=Bt(o,16),this.dmq1=Bt(a,16),this.coeff=Bt(s,16)):alert("Invalid RSA private key")}function Xt(e,t){var i=new vt,n=e>>1;this.e=parseInt(t,16);for(var r=new y(t,16);;){for(;;){if(this.p=new y(e-n,1,i),this.p.subtract(y.ONE).gcd(r).compareTo(y.ONE)==0&&this.p.isProbablePrime(10))break}for(;;){if(this.q=new y(n,1,i),this.q.subtract(y.ONE).gcd(r).compareTo(y.ONE)==0&&this.q.isProbablePrime(10))break}if(this.p.compareTo(this.q)<=0){var o=this.p;this.p=this.q;this.q=o}var o=this.p.subtract(y.ONE),a=this.q.subtract(y.ONE),s=o.multiply(a);if(s.gcd(r).compareTo(y.ONE)==0){this.n=this.p.multiply(this.q);this.d=r.modInverse(s);this.dmp1=this.d.mod(o);this.dmq1=this.d.mod(a);this.coeff=this.q.modInverse(this.p);break}}}function Jt(e){if(this.p==null||this.q==null)return e.modPow(this.d,this.n);for(var t=e.mod(this.p).modPow(this.dmp1,this.p),e=e.mod(this.q).modPow(this.dmq1,this.q);t.compareTo(e)<0;){t=t.add(this.p)}return t.subtract(e).multiply(this.coeff).mod(this.p).multiply(this.q).add(e)}function Kt(e){e=this.doPrivate(Bt(e,16));return e==null?null:Gt(e,this.n.bitLength()+7>>3)}Ht.prototype.doPrivate=Jt;Ht.prototype.setPrivate=Vt;Ht.prototype.setPrivateEx=Yt;Ht.prototype.generate=Xt;Ht.prototype.decrypt=Kt;var $t=[];$t.sha1="3021300906052b0e03021a05000414";$t.sha256="3031300d060960864801650304020105000420";var Zt=[];Zt.sha1=Ot.hex;Zt.sha256=zt.hex;function Qt(e,t,i){t/=4;for(var e=(0,Zt[i])(e),i="00"+$t[i]+e,e="",t=t-4-i.length,n=0;n<t;n+=2){e+="ff"}return sPaddedMessageHex="0001"+e+i}function ei(e,t){var i=Qt(e,this.n.bitLength(),t);return this.doPrivate(Bt(i,16)).toString(16)}function ti(e){e=Qt(e,this.n.bitLength(),"sha1");return this.doPrivate(Bt(e,16)).toString(16)}function ii(e){e=Qt(e,this.n.bitLength(),"sha256");return this.doPrivate(Bt(e,16)).toString(16)}function ni(e,t,i){var n=new Ht;n.setPublic(t,i);return n.doPublic(e)}function ri(e,t,i){return ni(e,t,i).toString(16).replace(/^1f+00/,"")}function oi(e){for(var t in $t){var i=$t[t],n=i.length;if(e.substring(0,n)==i)return[t,e.substring(n)]}return[]}function ai(e,t,i,n){t=ri(t,i,n);i=oi(t);if(i.length==0)return!1;t=i[1];e=(0,Zt[i[0]])(e);return t==e}function si(e,t){var i=Bt(e,16);return ai(t,i,this.n.toString(16),this.e.toString(16))}function li(e,t){var t=t.replace(/[ \n]+/g,""),i=this.doPublic(Bt(t,16)).toString(16).replace(/^1f+00/,""),n=oi(i);if(n.length==0)return!1;i=n[1];n=(0,Zt[n[0]])(e);return i==n}Ht.prototype.signString=ei;Ht.prototype.signStringWithSHA1=ti;Ht.prototype.signStringWithSHA256=ii;Ht.prototype.verifyString=li;Ht.prototype.verifyHexSignatureForMessage=si;var ui=function(){var u={Sbox:[99,124,119,123,242,107,111,197,48,1,103,43,254,215,171,118,202,130,201,125,250,89,71,240,173,212,162,175,156,164,114,192,183,253,147,38,54,63,247,204,52,165,229,241,113,216,49,21,4,199,35,195,24,150,5,154,7,18,128,226,235,39,178,117,9,131,44,26,27,110,90,160,82,59,214,179,41,227,47,132,83,209,0,237,32,252,177,91,106,203,190,57,74,76,88,207,208,239,170,251,67,77,51,133,69,249,2,127,80,60,159,168,81,163,64,143,146,157,56,245,188,182,218,33,16,255,243,210,205,12,19,236,95,151,68,23,196,167,126,61,100,93,25,115,96,129,79,220,34,42,144,136,70,238,184,20,222,94,11,219,224,50,58,10,73,6,36,92,194,211,172,98,145,149,228,121,231,200,55,109,141,213,78,169,108,86,244,234,101,122,174,8,186,120,37,46,28,166,180,198,232,221,116,31,75,189,139,138,112,62,181,102,72,3,246,14,97,53,87,185,134,193,29,158,225,248,152,17,105,217,142,148,155,30,135,233,206,85,40,223,140,161,137,13,191,230,66,104,65,153,45,15,176,84,187,22],ShiftRowTab:[0,5,10,15,4,9,14,3,8,13,2,7,12,1,6,11]};u.Init=function(){u.Sbox_Inv=Array(256);for(var e=0;e<256;e++){u.Sbox_Inv[u.Sbox[e]]=e}u.ShiftRowTab_Inv=Array(16);for(e=0;e<16;e++){u.ShiftRowTab_Inv[u.ShiftRowTab[e]]=e}u.xtime=Array(256);for(e=0;e<128;e++){u.xtime[e]=e<<1,u.xtime[128+e]=e<<1^27}};u.Done=function(){delete u.Sbox_Inv;delete u.ShiftRowTab_Inv;delete u.xtime};u.ExpandKey=function(e){var t=e.length,i,n=1;switch(t){case 16:i=176;break;case 24:i=208;break;case 32:i=240;break;default:alert("my.ExpandKey: Only key lengths of 16, 24 or 32 bytes allowed!")}for(var r=t;r<i;r+=4){var o=e.slice(r-4,r);if(r%t==0){if(o=[u.Sbox[o[1]]^n,u.Sbox[o[2]],u.Sbox[o[3]],u.Sbox[o[0]]],(n<<=1)>=256)n^=283}else t>24&&r%t==16&&(o=[u.Sbox[o[0]],u.Sbox[o[1]],u.Sbox[o[2]],u.Sbox[o[3]]]);for(var a=0;a<4;a++){e[r+a]=e[r+a-t]^o[a]}}};u.Encrypt=function(e,t){var i=t.length;u.AddRoundKey(e,t.slice(0,16));for(var n=16;n<i-16;n+=16){u.SubBytes(e,u.Sbox),u.ShiftRows(e,u.ShiftRowTab),u.MixColumns(e),u.AddRoundKey(e,t.slice(n,n+16))}u.SubBytes(e,u.Sbox);u.ShiftRows(e,u.ShiftRowTab);u.AddRoundKey(e,t.slice(n,i))};u.Decrypt=function(e,t){var i=t.length;u.AddRoundKey(e,t.slice(i-16,i));u.ShiftRows(e,u.ShiftRowTab_Inv);u.SubBytes(e,u.Sbox_Inv);for(i-=32;i>=16;i-=16){u.AddRoundKey(e,t.slice(i,i+16)),u.MixColumns_Inv(e),u.ShiftRows(e,u.ShiftRowTab_Inv),u.SubBytes(e,u.Sbox_Inv)}u.AddRoundKey(e,t.slice(0,16))};u.SubBytes=function(e,t){for(var i=0;i<16;i++){e[i]=t[e[i]]}};u.AddRoundKey=function(e,t){for(var i=0;i<16;i++){e[i]^=t[i]}};u.ShiftRows=function(e,t){for(var i=[].concat(e),n=0;n<16;n++){e[n]=i[t[n]]}};u.MixColumns=function(e){for(var t=0;t<16;t+=4){var i=e[t+0],n=e[t+1],r=e[t+2],o=e[t+3],a=i^n^r^o;e[t+0]^=a^u.xtime[i^n];e[t+1]^=a^u.xtime[n^r];e[t+2]^=a^u.xtime[r^o];e[t+3]^=a^u.xtime[o^i]}};u.MixColumns_Inv=function(e){for(var t=0;t<16;t+=4){var i=e[t+0],n=e[t+1],r=e[t+2],o=e[t+3],a=i^n^r^o,s=u.xtime[a],l=u.xtime[u.xtime[s^i^r]]^a;a^=u.xtime[u.xtime[s^n^o]];e[t+0]^=l^u.xtime[i^n];e[t+1]^=a^u.xtime[n^r];e[t+2]^=l^u.xtime[r^o];e[t+3]^=a^u.xtime[o^i]}};return u}(),fi=function(){var l={};ui.Init();l.b256to64=function(e){var t,i,n,r="",o=0,a=0,s=e.length;for(n=0;n<s;n++){i=e.charCodeAt(n),a==0?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>2&63),t=(i&3)<<4):a==1?(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|i>>4&15),t=(i&15)<<2):a==2&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t|i>>6&3),o+=1,r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i&63)),o+=1,a+=1,a==3&&(a=0)}a>0&&(r+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(t),r+="=");a==1&&(r+="=");return r};l.b64to256=function(e){var t,i,n="",r=0,o=0,a=e.length;for(i=0;i<a;i++){t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(i)),t>=0&&(r&&(n+=String.fromCharCode(o|t>>6-r&255)),r=r+2&7,o=t<<r&255)}return n};l.b16to64=function(e){var t,i,n="";e.length%2==1&&(e="0"+e);for(t=0;t+3<=e.length;t+=3){i=parseInt(e.substring(t,t+3),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>6)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i&63)}t+1==e.length?(i=parseInt(e.substring(t,t+1),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i<<2)):t+2==e.length&&(i=parseInt(e.substring(t,t+2),16),n+="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(i>>2)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt((i&3)<<4));for(;(n.length&3)>0;){n+="="}return n};l.b64to16=function(e){var t="",i,n=0,r;for(i=0;i<e.length;++i){if(e.charAt(i)=="=")break;v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".indexOf(e.charAt(i));v<0||(n==0?(t+=b(v>>2),r=v&3,n=1):n==1?(t+=b(r<<2|v>>4),r=v&15,n=2):n==2?(t+=b(r),t+=b(v>>2),r=v&3,n=3):(t+=b(r<<2|v>>4),t+=b(v&15),n=0))}n==1&&(t+=b(r<<2));return t};l.string2bytes=function(e){for(var t=[],i=0;i<e.length;i++){t.push(e.charCodeAt(i))}return t};l.bytes2string=function(e){for(var t="",i=0;i<e.length;i++){t+=String.fromCharCode(e[i])}return t};l.blockXOR=function(e,t){for(var i=Array(16),n=0;n<16;n++){i[n]=e[n]^t[n]}return i};l.blockIV=function(){var e=new Et,t=Array(16);e.nextBytes(t);return t};l.pad16=function(e){var t=e.slice(0),n=(16-e.length%16)%16;for(i=e.length;i<e.length+n;i++){t.push(0)}return t};l.depad=function(e){for(e=e.slice(0);e[e.length-1]==0;){e=e.slice(0,e.length-1)}return e};l.encryptAESCBC=function(e,t){var i=t.slice(0);ui.ExpandKey(i);for(var n=l.string2bytes(e),n=l.pad16(n),r=l.blockIV(),o=0;o<n.length/16;o++){var a=n.slice(o*16,o*16+16),s=r.slice(o*16,o*16+16),a=l.blockXOR(s,a);ui.Encrypt(a,i);r=r.concat(a)}i=l.bytes2string(r);return l.b256to64(i)};l.decryptAESCBC=function(e,t){var i=t.slice(0);ui.ExpandKey(i);for(var e=l.b64to256(e),n=l.string2bytes(e),r=[],o=1;o<n.length/16;o++){var a=n.slice(o*16,o*16+16),s=n.slice((o-1)*16,(o-1)*16+16);ui.Decrypt(a,i);a=l.blockXOR(s,a);r=r.concat(a)}r=l.depad(r);return l.bytes2string(r)};l.wrap60=function(e){for(var t="",i=0;i<e.length;i++){i%60==0&&i!=0&&(t+="\n"),t+=e[i]}return t};l.generateAESKey=function(){var e=Array(16);(new Et).nextBytes(e);return e};l.generateRSAKey=function(e,t){Math.seedrandom(zt.hex(e));var i=new Ht;i.generate(t,"10001");return i};l.publicKeyString=function(e){return pubkey=e.n.toString(16)};l.publicKeyID=function(e){return Ft(e)};l.publicKeyFromString=function(e){var e=e.split("|")[0],t=new Ht;t.setPublic(e,"10001");return t};l.encrypt=function(e,t,i){var n="";try{var r=l.publicKeyFromString(t);n+=r.encrypt(e)+"?"}catch(e){return{status:"Invalid public key"}}return{status:"success",cipher:n}};l.decrypt=function(e,t){var i=e.split("?"),n=t.decrypt(i[0]);return{status:"success",plaintext:n,signature:"unsigned"}};return l}();e.exports=fi},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var P=P||function(l,n){var e={},t=e.lib={},r=function e(){},i=t.Base={extend:function e(t){r.prototype=this;var i=new r;t&&i.mixIn(t);i.hasOwnProperty("init")||(i.init=function(){i.$super.init.apply(this,arguments)});i.init.prototype=i;i.$super=this;return i},create:function e(){var t=this.extend();t.init.apply(t,arguments);return t},init:function e(){},mixIn:function e(t){for(var i in t){t.hasOwnProperty(i)&&(this[i]=t[i])}t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function e(){return this.init.prototype.extend(this)}},u=t.WordArray=i.extend({init:function e(t,i){t=this.words=t||[];this.sigBytes=i!=n?i:4*t.length},toString:function e(t){return(t||a).stringify(this)},concat:function e(t){var i=this.words,n=t.words,r=this.sigBytes;t=t.sigBytes;this.clamp();if(r%4)for(var o=0;o<t;o++){i[r+o>>>2]|=(n[o>>>2]>>>24-8*(o%4)&255)<<24-8*((r+o)%4)}else if(65535<n.length)for(o=0;o<t;o+=4){i[r+o>>>2]=n[o>>>2]}else i.push.apply(i,n);this.sigBytes+=t;return this},clamp:function e(){var t=this.words,i=this.sigBytes;t[i>>>2]&=4294967295<<32-8*(i%4);t.length=l.ceil(i/4)},clone:function e(){var t=i.clone.call(this);t.words=this.words.slice(0);return t},random:function e(t){for(var i=[],n=0;n<t;n+=4){i.push(4294967296*l.random()|0)}return new u.init(i,t)}}),o=e.enc={},a=o.Hex={stringify:function e(t){var i=t.words;t=t.sigBytes;for(var n=[],r=0;r<t;r++){var o=i[r>>>2]>>>24-8*(r%4)&255;n.push((o>>>4).toString(16));n.push((o&15).toString(16))}return n.join("")},parse:function e(t){for(var i=t.length,n=[],r=0;r<i;r+=2){n[r>>>3]|=parseInt(t.substr(r,2),16)<<24-4*(r%8)}return new u.init(n,i/2)}},s=o.Latin1={stringify:function e(t){var i=t.words;t=t.sigBytes;for(var n=[],r=0;r<t;r++){n.push(String.fromCharCode(i[r>>>2]>>>24-8*(r%4)&255))}return n.join("")},parse:function e(t){for(var i=t.length,n=[],r=0;r<i;r++){n[r>>>2]|=(t.charCodeAt(r)&255)<<24-8*(r%4)}return new u.init(n,i)}},f=o.Utf8={stringify:function e(t){try{return decodeURIComponent(escape(s.stringify(t)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function e(t){return s.parse(unescape(encodeURIComponent(t)))}},h=t.BufferedBlockAlgorithm=i.extend({reset:function e(){this._data=new u.init;this._nDataBytes=0},_append:function e(t){"string"==typeof t&&(t=f.parse(t));this._data.concat(t);this._nDataBytes+=t.sigBytes},_process:function e(t){var i=this._data,n=i.words,r=i.sigBytes,o=this.blockSize,a=r/(4*o),a=t?l.ceil(a):l.max((a|0)-this._minBufferSize,0);t=a*o;r=l.min(4*t,r);if(t){for(var s=0;s<t;s+=o){this._doProcessBlock(n,s)}s=n.splice(0,t);i.sigBytes-=r}return new u.init(s,r)},clone:function e(){var t=i.clone.call(this);t._data=this._data.clone();return t},_minBufferSize:0});t.Hasher=h.extend({cfg:i.extend(),init:function e(t){this.cfg=this.cfg.extend(t);this.reset()},reset:function e(){h.reset.call(this);this._doReset()},update:function e(t){this._append(t);this._process();return this},finalize:function e(t){t&&this._append(t);return this._doFinalize()},blockSize:16,_createHelper:function e(i){return function(e,t){return new i.init(t).finalize(e)}},_createHmacHelper:function e(i){return function(e,t){return new c.HMAC.init(i,t).finalize(e)}}});var c=e.algo={};return e}(Math);(function(){var e=P,u=e.lib.WordArray;e.enc.Base64={stringify:function e(t){var i=t.words,n=t.sigBytes,r=this._map;t.clamp();t=[];for(var o=0;o<n;o+=3){for(var a=(i[o>>>2]>>>24-8*(o%4)&255)<<16|(i[o+1>>>2]>>>24-8*((o+1)%4)&255)<<8|i[o+2>>>2]>>>24-8*((o+2)%4)&255,s=0;4>s&&o+.75*s<n;s++){t.push(r.charAt(a>>>6*(3-s)&63))}}if(i=r.charAt(64))for(;t.length%4;){t.push(i)}return t.join("")},parse:function e(t){var i=t.length,n=this._map,r=n.charAt(64);r&&(r=t.indexOf(r),-1!=r&&(i=r));for(var r=[],o=0,a=0;a<i;a++){if(a%4){var s=n.indexOf(t.charAt(a-1))<<2*(a%4),l=n.indexOf(t.charAt(a))>>>6-2*(a%4);r[o>>>2]|=(s|l)<<24-8*(o%4);o++}}return u.create(r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}})();(function(a){function C(e,t,i,n,r,o,a){e=e+(t&i|~t&n)+r+a;return(e<<o|e>>>32-o)+t}function x(e,t,i,n,r,o,a){e=e+(t&n|i&~n)+r+a;return(e<<o|e>>>32-o)+t}function T(e,t,i,n,r,o,a){e=e+(t^i^n)+r+a;return(e<<o|e>>>32-o)+t}function k(e,t,i,n,r,o,a){e=e+(i^(t|~n))+r+a;return(e<<o|e>>>32-o)+t}for(var e=P,t=e.lib,i=t.WordArray,n=t.Hasher,t=e.algo,D=[],r=0;64>r;r++){D[r]=4294967296*a.abs(a.sin(r+1))|0}t=t.MD5=n.extend({_doReset:function e(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function e(t,i){for(var n=0;16>n;n++){var r=i+n,o=t[r];t[r]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360}var n=this._hash.words,r=t[i+0],o=t[i+1],a=t[i+2],s=t[i+3],l=t[i+4],u=t[i+5],f=t[i+6],h=t[i+7],c=t[i+8],d=t[i+9],p=t[i+10],v=t[i+11],y=t[i+12],m=t[i+13],g=t[i+14],S=t[i+15],b=n[0],w=n[1],_=n[2],P=n[3],b=C(b,w,_,P,r,7,D[0]),P=C(P,b,w,_,o,12,D[1]),_=C(_,P,b,w,a,17,D[2]),w=C(w,_,P,b,s,22,D[3]),b=C(b,w,_,P,l,7,D[4]),P=C(P,b,w,_,u,12,D[5]),_=C(_,P,b,w,f,17,D[6]),w=C(w,_,P,b,h,22,D[7]),b=C(b,w,_,P,c,7,D[8]),P=C(P,b,w,_,d,12,D[9]),_=C(_,P,b,w,p,17,D[10]),w=C(w,_,P,b,v,22,D[11]),b=C(b,w,_,P,y,7,D[12]),P=C(P,b,w,_,m,12,D[13]),_=C(_,P,b,w,g,17,D[14]),w=C(w,_,P,b,S,22,D[15]),b=x(b,w,_,P,o,5,D[16]),P=x(P,b,w,_,f,9,D[17]),_=x(_,P,b,w,v,14,D[18]),w=x(w,_,P,b,r,20,D[19]),b=x(b,w,_,P,u,5,D[20]),P=x(P,b,w,_,p,9,D[21]),_=x(_,P,b,w,S,14,D[22]),w=x(w,_,P,b,l,20,D[23]),b=x(b,w,_,P,d,5,D[24]),P=x(P,b,w,_,g,9,D[25]),_=x(_,P,b,w,s,14,D[26]),w=x(w,_,P,b,c,20,D[27]),b=x(b,w,_,P,m,5,D[28]),P=x(P,b,w,_,a,9,D[29]),_=x(_,P,b,w,h,14,D[30]),w=x(w,_,P,b,y,20,D[31]),b=T(b,w,_,P,u,4,D[32]),P=T(P,b,w,_,c,11,D[33]),_=T(_,P,b,w,v,16,D[34]),w=T(w,_,P,b,g,23,D[35]),b=T(b,w,_,P,o,4,D[36]),P=T(P,b,w,_,l,11,D[37]),_=T(_,P,b,w,h,16,D[38]),w=T(w,_,P,b,p,23,D[39]),b=T(b,w,_,P,m,4,D[40]),P=T(P,b,w,_,r,11,D[41]),_=T(_,P,b,w,s,16,D[42]),w=T(w,_,P,b,f,23,D[43]),b=T(b,w,_,P,d,4,D[44]),P=T(P,b,w,_,y,11,D[45]),_=T(_,P,b,w,S,16,D[46]),w=T(w,_,P,b,a,23,D[47]),b=k(b,w,_,P,r,6,D[48]),P=k(P,b,w,_,h,10,D[49]),_=k(_,P,b,w,g,15,D[50]),w=k(w,_,P,b,u,21,D[51]),b=k(b,w,_,P,y,6,D[52]),P=k(P,b,w,_,s,10,D[53]),_=k(_,P,b,w,p,15,D[54]),w=k(w,_,P,b,o,21,D[55]),b=k(b,w,_,P,c,6,D[56]),P=k(P,b,w,_,S,10,D[57]),_=k(_,P,b,w,f,15,D[58]),w=k(w,_,P,b,m,21,D[59]),b=k(b,w,_,P,l,6,D[60]),P=k(P,b,w,_,v,10,D[61]),_=k(_,P,b,w,a,15,D[62]),w=k(w,_,P,b,d,21,D[63]);n[0]=n[0]+b|0;n[1]=n[1]+w|0;n[2]=n[2]+_|0;n[3]=n[3]+P|0},_doFinalize:function e(){var t=this._data,i=t.words,n=8*this._nDataBytes,r=8*t.sigBytes;i[r>>>5]|=128<<24-r%32;var o=a.floor(n/4294967296);i[(r+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360;i[(r+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360;t.sigBytes=4*(i.length+1);this._process();t=this._hash;i=t.words;for(n=0;4>n;n++){r=i[n],i[n]=(r<<8|r>>>24)&16711935|(r<<24|r>>>8)&4278255360}return t},clone:function e(){var t=n.clone.call(this);t._hash=this._hash.clone();return t}});e.MD5=n._createHelper(t);e.HmacMD5=n._createHmacHelper(t)})(Math);(function(){var e=P,t=e.lib,i=t.Base,f=t.WordArray,t=e.algo,n=t.EvpKDF=i.extend({cfg:i.extend({keySize:4,hasher:t.MD5,iterations:1}),init:function e(t){this.cfg=this.cfg.extend(t)},compute:function e(t,i){for(var n=this.cfg,r=n.hasher.create(),o=f.create(),a=o.words,s=n.keySize,n=n.iterations;a.length<s;){l&&r.update(l);var l=r.update(t).finalize(i);r.reset();for(var u=1;u<n;u++){l=r.finalize(l),r.reset()}o.concat(l)}o.sigBytes=4*s;return o}});e.EvpKDF=function(e,t,i){return n.create(i).compute(e,t)}})();P.lib.Cipher||function(a){var e=P,t=e.lib,i=t.Base,s=t.WordArray,n=t.BufferedBlockAlgorithm,r=e.enc.Base64,o=e.algo.EvpKDF,l=t.Cipher=n.extend({cfg:i.extend(),createEncryptor:function e(t,i){return this.create(this._ENC_XFORM_MODE,t,i)},createDecryptor:function e(t,i){return this.create(this._DEC_XFORM_MODE,t,i)},init:function e(t,i,n){this.cfg=this.cfg.extend(n);this._xformMode=t;this._key=i;this.reset()},reset:function e(){n.reset.call(this);this._doReset()},process:function e(t){this._append(t);return this._process()},finalize:function e(t){t&&this._append(t);return this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function e(r){return{encrypt:function e(t,i,n){return("string"==typeof i?p:d).encrypt(r,t,i,n)},decrypt:function e(t,i,n){return("string"==typeof i?p:d).decrypt(r,t,i,n)}}}});t.StreamCipher=l.extend({_doFinalize:function e(){return this._process(!0)},blockSize:1});var u=e.mode={},f=function e(t,i,n){var r=this._iv;r?this._iv=a:r=this._prevBlock;for(var o=0;o<n;o++){t[i+o]^=r[o]}},h=(t.BlockCipherMode=i.extend({createEncryptor:function e(t,i){return this.Encryptor.create(t,i)},createDecryptor:function e(t,i){return this.Decryptor.create(t,i)},init:function e(t,i){this._cipher=t;this._iv=i}})).extend();h.Encryptor=h.extend({processBlock:function e(t,i){var n=this._cipher,r=n.blockSize;f.call(this,t,i,r);n.encryptBlock(t,i);this._prevBlock=t.slice(i,i+r)}});h.Decryptor=h.extend({processBlock:function e(t,i){var n=this._cipher,r=n.blockSize,o=t.slice(i,i+r);n.decryptBlock(t,i);f.call(this,t,i,r);this._prevBlock=o}});u=u.CBC=h;h=(e.pad={}).Pkcs7={pad:function e(t,i){for(var n=4*i,n=n-t.sigBytes%n,r=n<<24|n<<16|n<<8|n,o=[],a=0;a<n;a+=4){o.push(r)}n=s.create(o,n);t.concat(n)},unpad:function e(t){t.sigBytes-=t.words[t.sigBytes-1>>>2]&255}};t.BlockCipher=l.extend({cfg:l.cfg.extend({mode:u,padding:h}),reset:function e(){l.reset.call(this);var t=this.cfg,i=t.iv,t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var n=t.createEncryptor;else n=t.createDecryptor,this._minBufferSize=1;this._mode=n.call(t,this,i&&i.words)},_doProcessBlock:function e(t,i){this._mode.processBlock(t,i)},_doFinalize:function e(){var t=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){t.pad(this._data,this.blockSize);var i=this._process(!0)}else i=this._process(!0),t.unpad(i);return i},blockSize:4});var c=t.CipherParams=i.extend({init:function e(t){this.mixIn(t)},toString:function e(t){return(t||this.formatter).stringify(this)}}),u=(e.format={}).OpenSSL={stringify:function e(t){var i=t.ciphertext;t=t.salt;return(t?s.create([1398893684,1701076831]).concat(t).concat(i):i).toString(r)},parse:function e(t){t=r.parse(t);var i=t.words;if(1398893684==i[0]&&1701076831==i[1]){var n=s.create(i.slice(2,4));i.splice(0,4);t.sigBytes-=16}return c.create({ciphertext:t,salt:n})}},d=t.SerializableCipher=i.extend({cfg:i.extend({format:u}),encrypt:function e(t,i,n,r){r=this.cfg.extend(r);var o=t.createEncryptor(n,r);i=o.finalize(i);o=o.cfg;return c.create({ciphertext:i,key:n,iv:o.iv,algorithm:t,mode:o.mode,padding:o.padding,blockSize:t.blockSize,formatter:r.format})},decrypt:function e(t,i,n,r){r=this.cfg.extend(r);i=this._parse(i,r.format);return t.createDecryptor(n,r).finalize(i.ciphertext)},_parse:function e(t,i){return"string"==typeof t?i.parse(t,this):t}}),e=(e.kdf={}).OpenSSL={execute:function e(t,i,n,r){r||(r=s.random(8));t=o.create({keySize:i+n}).compute(t,r);n=s.create(t.words.slice(i),4*n);t.sigBytes=4*i;return c.create({key:t,iv:n,salt:r})}},p=t.PasswordBasedCipher=d.extend({cfg:d.cfg.extend({kdf:e}),encrypt:function e(t,i,n,r){r=this.cfg.extend(r);n=r.kdf.execute(n,t.keySize,t.ivSize);r.iv=n.iv;t=d.encrypt.call(this,t,i,n.key,r);t.mixIn(n);return t},decrypt:function e(t,i,n,r){r=this.cfg.extend(r);i=this._parse(i,r.format);n=r.kdf.execute(n,t.keySize,t.ivSize,i.salt);r.iv=n.iv;return d.decrypt.call(this,t,i,n.key,r)}})}();(function(){for(var e=P,t=e.lib.BlockCipher,i=e.algo,s=[],r=[],n=[],o=[],a=[],l=[],u=[],f=[],h=[],c=[],d=[],p=0;256>p;p++){d[p]=128>p?p<<1:p<<1^283}for(var v=0,y=0,p=0;256>p;p++){var m=y^y<<1^y<<2^y<<3^y<<4,m=m>>>8^m&255^99;s[v]=m;r[m]=v;var g=d[v],S=d[g],b=d[S],w=257*d[m]^16843008*m;n[v]=w<<24|w>>>8;o[v]=w<<16|w>>>16;a[v]=w<<8|w>>>24;l[v]=w;w=16843009*b^65537*S^257*g^16843008*v;u[m]=w<<24|w>>>8;f[m]=w<<16|w>>>16;h[m]=w<<8|w>>>24;c[m]=w;v?(v=g^d[d[d[b^g]]],y^=d[d[y]]):v=y=1}var _=[0,1,2,4,8,16,32,64,128,27,54],i=i.AES=t.extend({_doReset:function e(){for(var t=this._key,i=t.words,n=t.sigBytes/4,t=4*((this._nRounds=n+6)+1),r=this._keySchedule=[],o=0;o<t;o++){if(o<n)r[o]=i[o];else{var a=r[o-1];o%n?6<n&&4==o%n&&(a=s[a>>>24]<<24|s[a>>>16&255]<<16|s[a>>>8&255]<<8|s[a&255]):(a=a<<8|a>>>24,a=s[a>>>24]<<24|s[a>>>16&255]<<16|s[a>>>8&255]<<8|s[a&255],a^=_[o/n|0]<<24);r[o]=r[o-n]^a}}i=this._invKeySchedule=[];for(n=0;n<t;n++){o=t-n,a=n%4?r[o]:r[o-4],i[n]=4>n||4>=o?a:u[s[a>>>24]]^f[s[a>>>16&255]]^h[s[a>>>8&255]]^c[s[a&255]]}},encryptBlock:function e(t,i){this._doCryptBlock(t,i,this._keySchedule,n,o,a,l,s)},decryptBlock:function e(t,i){var n=t[i+1];t[i+1]=t[i+3];t[i+3]=n;this._doCryptBlock(t,i,this._invKeySchedule,u,f,h,c,r);n=t[i+1];t[i+1]=t[i+3];t[i+3]=n},_doCryptBlock:function e(t,i,n,r,o,a,s,l){for(var u=this._nRounds,f=t[i]^n[0],h=t[i+1]^n[1],c=t[i+2]^n[2],d=t[i+3]^n[3],p=4,v=1;v<u;v++){var y=r[f>>>24]^o[h>>>16&255]^a[c>>>8&255]^s[d&255]^n[p++],m=r[h>>>24]^o[c>>>16&255]^a[d>>>8&255]^s[f&255]^n[p++],g=r[c>>>24]^o[d>>>16&255]^a[f>>>8&255]^s[h&255]^n[p++],d=r[d>>>24]^o[f>>>16&255]^a[h>>>8&255]^s[c&255]^n[p++],f=y,h=m,c=g}y=(l[f>>>24]<<24|l[h>>>16&255]<<16|l[c>>>8&255]<<8|l[d&255])^n[p++];m=(l[h>>>24]<<24|l[c>>>16&255]<<16|l[d>>>8&255]<<8|l[f&255])^n[p++];g=(l[c>>>24]<<24|l[d>>>16&255]<<16|l[f>>>8&255]<<8|l[h&255])^n[p++];d=(l[d>>>24]<<24|l[f>>>16&255]<<16|l[h>>>8&255]<<8|l[c&255])^n[p++];t[i]=y;t[i+1]=m;t[i+2]=g;t[i+3]=d},keySize:8});e.AES=t._createHelper(i)})();t.default=P},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();function r(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var o=function(){var e=function(){function e(){r(this,e)}n(e,[{key:"createClientObject",value:function e(t,i,n,r){return{socket:t,id:i,playURL:n,deviceSerial:r.deviceSerial||"",verificationCode:r.verificationCode||"",resolve:null,reject:null}}},{key:"playCmd",value:function e(t){var i={sequence:0,cmd:"realplay",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL};return JSON.stringify(i)}},{key:"playbackCmd",value:function e(t,i,n){var r={sequence:0,cmd:"playback",deviceSerial:t.deviceSerial,verificationCode:t.verificationCode,url:t.playURL,startTime:i,endTime:n};return JSON.stringify(r)}}]);return e}();return e}();t.LocalService=o},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var f=0;var v=1;var h=2;var r=3;var o=4;var a=5;var s=6;var u=7;var y=11;var c=12;var d=13;var p=14;var m=15;var g=16;var S=17;var b=18;var w=19;var _=20;var P=21;var C=22;var x=24;var T=25;var k=26;var D=27;var M=28;var R=29;var E=30;var I=31;var z=33;var A=34;var O=99;var F=40;var B=41;var L=42;var q=43;var N=44;var H=45;var j=46;var U=47;var W=48;var G=60;var V=61;var Y=62;var X=63;var J={AUDIO_G711_U:28944,AUDIO_G711_A:28945,AUDIO_G722_1:29217,AUDIO_G726_U:29280,AUDIO_G726_A:29281,AUDIO_G726_2:29282,AUDIO_AACLC:8193,AUDIO_MPEG:8192,AUDIO_NULL:0};var K=0;var $=1;var Z=1;var Q=256;var ee=28944;var te=28945;var ie=0;var ne=1;var re="BMP";var oe="JPEG";var ae=0;var se=1;var le=15;var ue=8;var fe=1;var he=25;var ce=20;var de=5;var pe=5*1024*1024;var ve=5e3;var ye={left:0,top:0,right:0,bottom:0};var me=false;var ge=false;var Se={id:null,cmd:null,data:null,errorCode:0,status:null};var be=t.JSPlayCtrl=function(){function s(e,t,a){l(this,s);if(e!=null&&e!==undefined&&typeof e==="string"){this.szBasePath=e}else{return v}if(t&&typeof t==="function"){this.fnCallBack=t}else{return v}this.decodeWorker=null;this.streamOpenMode=null;this.bOpenStream=false;this.audioRenderer=null;this.aAudioBuffer=[];this.iAudioBufferSize=0;this.oSuperRender=null;this.aVideoFrameBuffer=[];this.YUVBufferSize=fe;this.szOSDTime=null;this.bPlaySound=false;this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bPlayRateChange=false;this.bAudioTypeSupport=true;this.bOnlyPlaySound=false;this.dataCallBackFun=null;this.YUVBufSizeCBFun=null;this.nWidth=0;this.nHeight=0;this.sCanvasId=null;this.aDisplayBuf=null;this.bVisibility=true;this.nDecFrameType=ae;this.iCanvasWidth=0;this.iCanvasHeight=0;this.iZoomNum=0;this.iRatio_x=1;this.iRatio_y=1;this.stDisplayRect={top:0,left:0,right:0,bottom:0};this.bDisRect=false;this.stYUVRect={top:0,left:0,right:0,bottom:0};this.aInputDataLens=[];this.aInputDataBuffer=[];this.bIsGetYUV=false;this.bIsFirstFrame=true;this.iInputMaxBufSize=pe;this.bIsInput=false;this.bIsInputBufOver=false;this.iInputDataLen=ve;var p=this;this.setCallBack=function(e,t,i,n,r){var o=Se;o.id=a;o.cmd=t;o.data=i;o.errorCode=n;o.status=r;e.fnCallBack(o)};if(!me){me=true;var i=document.createElement("script");i.type="text/javascript";i.src=p.szBasePath+"AudioRenderer.js";var n=document.getElementsByTagName("head")[0];n.appendChild(i);i.onload=i.onreadystatechange=function(){if(!this.readyState||this.readyState==="loaded"||this.readyState==="complete"){}}}if(!ge){ge=true;var r=document.createElement("script");r.type="text/javascript";r.src=p.szBasePath+"SuperRender_10.js";var o=document.getElementsByTagName("head")[0];o.appendChild(r);r.onload=r.onreadystatechange=function(){if(!this.readyState||this.readyState==="loaded"||this.readyState==="complete"){}}}this.convertErrorCode=function(e){switch(e){case 1:return f;case 98:return v;default:return e}};this.arrayBufferCopy=function(e){var t=e.byteLength;var i=new Uint8Array(t);var n=new Uint8Array(e);var r=0;for(r=0;r<t;r++){i[r]=n[r]}return i};this.inputDataFun=function(){var e;var t=0;p.bIsGetYUV=false;if(p.bIsInputBufOver){console.log("inputDataFun over");e=new Uint8Array(1)}else{while(p.aInputDataLens.length>0){t+=p.aInputDataLens.shift();if(t>p.iInputDataLen){break}}e=p.aInputDataBuffer.splice(0,t)}var i=new Uint8Array(e);var n={command:"InputData",data:i.buffer,dataSize:t};if(this.bOnlyPlaySound){p.decodeWorker.postMessage(n,[n.data])}else{if(p.bPlay){if(!p.bPause){p.decodeWorker.postMessage(n,[n.data])}else{if(p.bOnebyOne){p.decodeWorker.postMessage(n,[n.data])}}}}e=null;i=null};this.getPic=function(e,t){if(this.decodeWorker==null||this.oSuperRender==null){return h}if(!this.bPlay){return h}if(e&&typeof e==="function"){this.dataCallBackFun=e}else{return v}if(0===this.iZoomNum){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{if(0===this.iCanvasWidth||0===this.iCanvasHeight){this.stYUVRect.left=0;this.stYUVRect.top=0;this.stYUVRect.right=0;this.stYUVRect.bottom=0}else{var i=this.nWidth/this.iCanvasWidth;var n=this.nHeight/this.iCanvasHeight;this.stYUVRect.left=Math.round(this.stDisplayRect.left*i);this.stYUVRect.top=Math.round(this.stDisplayRect.top*n);this.stYUVRect.right=Math.round(this.stDisplayRect.right*i);this.stYUVRect.bottom=Math.round(this.stDisplayRect.bottom*n)}if(this.stYUVRect.right-this.stYUVRect.left<32||this.stYUVRect.bottom-this.stYUVRect.top<32){return v}}if(this.aDisplayBuf==null){return h}var r=this.arrayBufferCopy(this.aDisplayBuf);var o={command:t,data:r.buffer,width:this.nWidth,height:this.nHeight,rect:this.stYUVRect};this.decodeWorker.postMessage(o,[o.data]);return f};this.createWorker=function(d){if(window.Worker){if(this.decodeWorker==null){this.decodeWorker=new Worker(p.szBasePath+"DecodeWorker.js");if(this.decodeWorker==null){return G}}this.decodeWorker.onmessage=function(e){var t=null;var i=e.data;switch(i.function){case"loaded":t="loaded";d.setCallBack(d,"loaded",0,0,true);break;case"SetStreamOpenMode":t="SetStreamOpenMode";break;case"OpenStream":t="OpenStream";if(1===i.errorCode){p.bOpenStream=true;return}break;case"InputData":t="InputData";if(i.errorCode===y){p.bIsInputBufOver=true;p.inputDataFun()}if(i.errorCode===I){p.bIsInputBufOver=false}break;case"GetFrameData":t="GetFrameData";if(!p.bOnlyPlaySound){if(i.data!=null&&i.frameInfo!=null){var n=i.frameInfo.width;var r=i.frameInfo.height}if(!p.bPlay){return}if(!p.bIsFirstFrame&&i.errorCode===I){p.bIsInputBufOver=false;setTimeout(p.inputDataFun(),5);break}else if(p.bIsInputBufOver){p.inputDataFun()}else{if(i.type==="videoType"){if(p.aInputDataLens.length>0&&p.bIsInput){p.inputDataFun();p.bIsInput=false}else{p.bIsGetYUV=true}p.bIsFirstFrame=false}}}if(p.bVisibility){switch(i.type){case"videoType":if(i.data==null||i.frameInfo==null){return v}p.bIsFirstFrame=false;d.nWidth=i.frameInfo.width;d.nHeight=i.frameInfo.height;var o=new Object;o.data=i.data;o.osdTime=i.osd;o.nWidth=i.frameInfo.width;o.nHeight=i.frameInfo.height;d.aVideoFrameBuffer.push(o);o=null;var a=d.aVideoFrameBuffer.length;if(a>ce){if(!d.bOnebyOne){d.aVideoFrameBuffer.splice(0,de)}}if(d.bOnebyOne){if(d.aVideoFrameBuffer.length>=le){d.setCallBack(d,"OnebyOne",0,0,false);d.bIsFirstFrame=true;break}}break;case"audioType":if(d.bPlaySound&&!d.bPlayRateChange||p.bOnlyPlaySound){var s=new Uint8Array(i.data);var l=d.aAudioBuffer.length;for(var u=0,f=s.length;u<f;u++){d.aAudioBuffer[l+u]=s[u]}d.iAudioBufferSize++;s=null;if(d.iAudioBufferSize>=he){d.audioRenderer.Play(d.aAudioBuffer,d.aAudioBuffer.length,i.frameInfo);d.aAudioBuffer.splice(0,d.aAudioBuffer.length);d.aAudioBuffer.length=0;d.iAudioBufferSize=0}}break;case"privateType":break;default:break}}break;case"PlaySound":t="PlaySound";break;case"GetJPEG":t="GetJPEG";var h=i.data;d.dataCallBackFun(h);break;case"GetBMP":t="GetBMP";var c=i.data;d.dataCallBackFun(c);break;default:break}if("GetFrameData"!==t){d.setCallBack(d,t,0,d.convertErrorCode(i.errorCode),true)}else{if(g===i.errorCode){d.setCallBack(d,t,0,d.convertErrorCode(i.errorCode),true)}}}}};this.createWorker(p);this.draw=function(){if(p.bPlay){if(!p.bPause){requestAnimationFrame(p.draw)}var e=p.aVideoFrameBuffer.length;if(p.YUVBufSizeCBFun!=null){p.YUVBufSizeCBFun(e)}if(p.bOnebyOne){if(e<=ue){p.setCallBack(p,"OnebyOne",0,I,true)}}if(e>p.YUVBufferSize){var t=p.aVideoFrameBuffer.shift();p.aDisplayBuf=t.data;var i=new Uint8Array(p.aDisplayBuf);if((p.nWidth==1920&&p.nHeight==1088||p.nWidth==2688&&p.nHeight==1520)&&!p.bDisRect){var n=document.getElementById(p.sCanvasId).getBoundingClientRect();var r=n.width;var o=n.height-8;p.stDisRect={top:0,left:0,right:r,bottom:o};p.oSuperRender.SR_SetDisplayRect(p.stDisRect)}else if(p.nWidth==640&&p.nHeight==368&&!p.bDisRect){var n=document.getElementById(p.sCanvasId).getBoundingClientRect();var r=n.width;var o=Math.floor(n.height*360/368);p.stDisRect={top:0,left:0,right:r,bottom:o};p.oSuperRender.SR_SetDisplayRect(p.stDisRect)}p.oSuperRender.SR_DisplayFrameData(t.nWidth,t.nHeight,i);i=null;p.szOSDTime=t.osdTime;t=null}}else{if(!p.bPlay){p.aVideoFrameBuffer.splice(0,p.aVideoFrameBuffer.length);p.aAudioBuffer.splice(0,p.aAudioBuffer.length)}}};this.checkAudioType=function(e){var t=function e(t,i){var n=t[i]&255|(t[i+1]&255)<<8|(t[i+2]&255)<<16|(t[i+3]&255)<<24;return n};var i=[e[12],e[13],0,0];var n=t(i,0);switch(n){case J.AUDIO_G711_A:case J.AUDIO_G711_U:case J.AUDIO_G722_1:case J.AUDIO_G726_2:case J.AUDIO_G726_A:case J.AUDIO_G726_U:case J.AUDIO_AACLC:case J.AUDIO_MPEG:return f;default:return g}}}n(s,[{key:"PlayM4_SetStreamOpenMode",value:function e(t){if(t==null||t===undefined){return v}if(t!==ie&&t!==ne){return v}this.streamOpenMode=t;return f}},{key:"PlayM4_OpenStream",value:function e(t,i,n){if(this.decodeWorker==null){return h}if(t==null||i<=0||n<=0){return v}this.bPlay=false;this.bPause=false;this.bOnebyOne=false;this.bIsFirstFrame=true;this.bIsGetYUV=false;this.bIsInput=false;var r=this.checkAudioType(t);if(f!==r){this.bAudioTypeSupport=false}else{this.bAudioTypeSupport=true}this.decodeWorker.postMessage({command:"SetStreamOpenMode",data:this.streamOpenMode});this.decodeWorker.postMessage({command:"OpenStream",data:t,dataSize:i,bufPoolSize:n});return f}},{key:"PlayM4_CloseStream",value:function e(){if(this.decodeWorker===null||this.bOpenStream===false){return h}this.bOnlyPlaySound=false;this.PlayM4_Stop();this.decodeWorker.postMessage({command:"CloseStream"});if(this.oSuperRender!==null){this.oSuperRender.SR_Destroy();this.oSuperRender=null}if(this.audioRenderer!==null){this.audioRenderer.Stop();this.audioRenderer=null}this.aAudioBuffer.splice(0,this.aAudioBuffer.length);this.aVideoFrameBuffer.splice(0,this.aVideoFrameBuffer.length);this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);this.bOpenStream=false;this.iAudioBufferSize=0;this.szOSDTime=null;return f}},{key:"PlayM4_Destroy",value:function e(){if(this.decodeWorker===null){return f}this.PlayM4_CloseStream();this.decodeWorker.terminate();this.decodeWorker=null;return f}},{key:"PlayM4_InputData",value:function e(t,i){if(this.decodeWorker===null||this.bOpenStream===false){return h}var n=this.aInputDataBuffer.length;if(i===4){var r=new Uint8Array(t.buffer);if(r[0]===1&&r[1]===2&&r[2]===3&&r[3]===4){console.log("intput end");if(this.bIsFirstFrame){this.inputDataFun()}else{if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}}r=null;return f}}if(n>this.iInputMaxBufSize){console.log("input over\n");return y}var o=null;var a=i;switch(this.streamOpenMode){case ne:o=new Uint8Array(t.buffer);this.aInputDataLens.push(i);break;case ie:a=i+4;var s=new Uint32Array([i]);var l=new Uint8Array(s.buffer);o=new Uint8Array(a);o.set(l,0);o.set(t,4);s=null;l=null;this.aInputDataLens.push(i+4);break;default:return g}for(var u=0;u<a;u++){this.aInputDataBuffer[n+u]=o[u]}o=null;if(this.bOnlyPlaySound){this.inputDataFun()}else{if(this.bIsFirstFrame){this.inputDataFun()}else{if(this.bIsGetYUV){this.inputDataFun()}else{this.bIsInput=true}}}return f}},{key:"PlayM4_Play",value:function e(t){if(this.decodeWorker===null){return h}if(t===null){this.bOnlyPlaySound=true;this.sCanvasId=null}else{if(typeof t!=="string"){return v}if(this.bOnebyOne){this.bPlayRateChange=false;this.bOnebyOne=false;this.bPause=false;this.draw()}if(this.bPlay){return f}if(this.oSuperRender==null){this.oSuperRender=new SuperRender(t,this.szBasePath);if(this.oSuperRender==null){return V}}this.sCanvasId=t;this.bPlay=true;this.bPause=false;this.bOnebyOne=false;this.bPlaySound=false;this.bPlayRateChange=false;this.bOnlyPlaySound=false;this.draw()}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return V}}return f}},{key:"PlayM4_Stop",value:function e(){if(this.decodeWorker==null||this.oSuperRender==null){return h}if(!this.bPlay){return h}if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}this.bPlay=false;this.bOnebyOne=false;this.bPause=false;this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false;this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,null);return f}},{key:"PlayM4_PlayRate",value:function e(t){if(this.decodeWorker==null){return h}if(t===1){this.bPlayRateChange=false}else{this.bPlayRateChange=true}if(t<1){t=1}this.iInputDataLen=t*ve;return f}},{key:"PlayM4_Pause",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return h}if(!this.bPlay){return h}if(this.bOnebyOne){return h}if(typeof t!=="boolean"){return v}this.bPause=t;this.bIsFirstFrame=true;if(t){if(this.bPlaySound){this.PlayM4_StopSound();this.bPlaySound=true}}else{if(this.bPlaySound){this.PlayM4_PlaySound()}this.draw()}return f}},{key:"PlayM4_OneByOne",value:function e(){if(this.decodeWorker==null||this.oSuperRender==null){return h}if(!this.bPlay){return h}this.iInputDataLen=ve;this.bPause=true;this.bOnebyOne=true;this.bPlayRateChange=true;this.draw();return f}},{key:"PlayM4_PlaySound",value:function e(t){if(this.decodeWorker===null||this.bOpenStream===false){return h}if(!this.bAudioTypeSupport){return g}if(t<0||t>16){return v}if(this.audioRenderer==null){this.audioRenderer=new AudioRenderer;if(this.audioRenderer==null){return V}}this.audioRenderer.SetWndNum(t);this.bPlaySound=true;return f}},{key:"PlayM4_StopSound",value:function e(){if(this.decodeWorker==null||this.audioRenderer==null){return h}if(!this.bPlaySound){return h}this.bPlaySound=false;return f}},{key:"PlayM4_SetDisplayBuf",value:function e(t){if(this.decodeWorker==null){return h}if(t<=0){return v}this.YUVBufferSize=t;return f}},{key:"PlayM4_SetSecretKey",value:function e(t,i,n){if(this.decodeWorker==null||this.bOpenStream===false){return h}if(i==null){return v}if($===t){if(128===n){if(i==null||i===undefined){return v}}else{return v}}else if(K===t){}else{return v}this.decodeWorker.postMessage({command:"SetSecretKey",data:i,nKeyType:t,nKeyLen:n});return f}},{key:"PlayM4_SetDecodeFrameType",value:function e(t){if(this.decodeWorker==null||this.oSuperRender==null){return h}if(t!==ae&&t!==se){return v}this.nDecFrameType=t;this.decodeWorker.postMessage({command:"SetDecodeFrameType",data:t});return f}},{key:"PlayM4_SetIFrameDecInterval",value:function e(t){if(this.nDecFrameType!==se){return h}if(t<0){return v}this.decodeWorker.postMessage({command:"SetIFrameDecInterval",data:t});return f}},{key:"PlayM4_SetDisplayRegion",value:function e(t,i){if(this.decodeWorker===null||this.bPlay===false||this.oSuperRender===null){return h}if(this.canvasId===null){return h}if(i===true){if(t===null||t===undefined){return v}if(typeof t.left==="number"&&typeof t.top==="number"&&typeof t.right==="number"&&typeof t.bottom==="number"){if(t.right<0||t.left<0||t.top<0||t.bottom<0){return v}var n=t.left;var r=t.right;var o=t.top;var a=t.bottom;var s=document.getElementById(this.sCanvasId).getBoundingClientRect();this.iCanvasWidth=s.width;this.iCanvasHeight=s.height;if(r-n<16||a-o<16||r-n>this.iCanvasWidth||a-o>this.iCanvasHeight){return v}if(this.iZoomNum!==0){n=Math.round(n/this.iRatio_x)+this.stDisplayRect.left;o=Math.round(o/this.iRatio_y)+this.stDisplayRect.top;r=Math.round(r/this.iRatio_x)+this.stDisplayRect.left;a=Math.round(a/this.iRatio_y)+this.stDisplayRect.top}this.stDisplayRect={top:o,left:n,right:r,bottom:a};this.oSuperRender.SR_SetDisplayRect(this.stDisplayRect);this.bDisRect=true;var l=r-n;var u=a-o;this.iRatio_x=this.iCanvasWidth/l;this.iRatio_y=this.iCanvasHeight/u;this.iZoomNum++}else{return v}}else{this.oSuperRender.SR_SetDisplayRect(null);this.iZoomNum=0;this.bDisRect=false}if(this.bPause||this.bOnebyOne||this.bPlayRateChange){this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,new Uint8Array(this.aDisplayBuf))}return f}},{key:"PlayM4_GetBMP",value:function e(t){return this.getPic(t,"GetBMP")}},{key:"PlayM4_GetJPEG",value:function e(t){return this.getPic(t,"GetJPEG")}},{key:"PlayM4_SetVolume",value:function e(t){if(this.decodeWorker==null){return h}if(this.audioRenderer==null){return h}if(t<0||t>100){return v}this.audioRenderer.SetVolume(t/100);return f}},{key:"PlayM4_GetVolume",value:function e(t){if(this.decodeWorker==null){return h}if(this.audioRenderer==null){return h}if(t&&typeof t==="function"){var i=this.audioRenderer.GetVolume();if(i===null){return X}else{t(Math.round(i*10)*10);return f}}else{return v}}},{key:"PlayM4_GetOSDTime",value:function e(t){if(this.decodeWorker==null){return h}if(!this.bPlay){return h}if(t&&typeof t==="function"){t(this.szOSDTime);return f}else{return v}}},{key:"PlayM4_IsVisible",value:function e(t){this.bVisibility=t;return f}},{key:"PlayM4_GetSdkVersion",value:function e(){return"07020137"}},{key:"PlayM4_GetInputBufSize",value:function e(){return this.aInputDataBuffer.length}},{key:"PlayM4_SetInputBufSize",value:function e(t){if(t>0){this.iInputMaxBufSize=t;return f}else{return v}}},{key:"PlayM4_GetYUVBufSize",value:function e(){return this.aVideoFrameBuffer.length}},{key:"PlayM4_GetFrameResolution",value:function e(t){if(this.decodeWorker==null){return h}if(t&&typeof t==="function"){t(this.nWidth,this.nHeight);return f}else{return v}}},{key:"PlayM4_RegisterYUVBufSizeCB",value:function e(t){if(t&&typeof t==="function"){this.YUVBufSizeCBFun=t;return f}else{return v}}},{key:"PlayM4_UnRegisterYUVBufSizeCB",value:function e(){if(this.YUVBufSizeCBFun!=null){this.YUVBufSizeCBFun=null}return f}},{key:"PlayM4_ClearCanvas",value:function e(){if(this.oSuperRender==null){return h}this.oSuperRender.SR_DisplayFrameData(this.nWidth,this.nHeight,null);return f}},{key:"PlayM4_ReleaseInputBuffer",value:function e(){if(this.aInputDataBuffer===null){return h}this.aInputDataBuffer.splice(0,this.aInputDataBuffer.length);this.aInputDataLens.splice(0,this.aInputDataLens.length);return f}},{key:"PlayM4_GetDecodeFrameType",value:function e(){return this.nDecFrameType}}]);return s}()},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.StorageManager=undefined;var n=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();var r=i(1);var c=a(r);var o=i(0);function a(e){return e&&e.__esModule?e:{default:e}}function l(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s="Web/RecordFiles/";var u="Web/PlaybackFiles/";var f=1e3;var h=1;var d=3001;window.requestFileSystem=window.requestFileSystem||window.webkitRequestFileSystem;window.URL=window.URL||window.webkitURL;var p=function(){function r(e,t,i,n){l(this,r);this.szUUID=e;this.szFileName=t;this.iStreamType=i;this.szPath="";this.bStart=false;this.aStreamList=[];this.options=n}n(r,[{key:"init",value:function e(){var i=this;if(0===this.iStreamType){this.szPath=s}else if(1===this.iStreamType){this.szPath=u}this.szPath+=this.getDateDir();var n=i.szPath.split("/");var t=new Promise(function(t){window.requestFileSystem(window.TEMPORARY,i.options.iFileSize,function(e){i.createDir(e.root,n,function(){t()})},i.errorHandler)});return t}},{key:"getDateDir",value:function e(){return o.oTool.dateFormat(new Date,"yyyy-MM-dd")}},{key:"createDir",value:function e(t,i,n){var r=this;if(i.length){t.getDirectory(i[0],{create:true},function(e){r.createDir(e,i.slice(1),n)},r.errorHandler)}else{n()}}},{key:"errorHandler",value:function e(){}},{key:"writeFileHeader",value:function e(i){var n=this;window.requestFileSystem(window.TEMPORARY,n.options.iFileSize,function(e){e.root.getFile(n.szPath+"/"+n.szFileName,{create:true},function(e){e.createWriter(function(e){e.onwriteend=function(){n.bStart=true;n.writeFile(e)};e.onerror=function(){};e.seek(e.length);var t=new Blob([i]);e.write(t)},n.errorHandler)},n.errorHandler)},n.errorHandler)}},{key:"writeFileContent",value:function e(t){this.aStreamList.push(t)}},{key:"writeFile",value:function e(t){var i=this;if(this.bStart){if(this.aStreamList.length>0){var n=this.aStreamList.shift();t.seek(t.length);if(t.length>=this.options.iFileSize){if(this.options.cbEventHandler){this.options.cbEventHandler(d,this.szUUID)}return}var r=new Blob([n]);t.write(r)}else{setTimeout(function(){i.writeFile(t)},f)}}}},{key:"stopWriteFile",value:function e(){var i=this;this.bStart=false;this.aStreamList.length=0;var t=new Promise(function(t){window.requestFileSystem(window.TEMPORARY,i.options.iFileSize,function(e){e.root.getFile(i.szPath+"/"+i.szFileName,{create:false},function(e){e.file(function(e){t();o.oTool.downloadFile(e,e.name)})},i.errorHandler)},i.errorHandler)});return t}}]);return r}();var v=function(){function s(e,t,i,n,r,o,a){l(this,s);this.szBasePath=e;this.szUUID=t;this.szFileName=i;this.aHeadBuf=new Uint8Array(n);this.iPackType=r;this.iStreamType=o;this.oWorker=null;this.oFileSystem=null;this.options=a;this.bHead=true}n(s,[{key:"init",value:function e(){var i=this;var t=new Promise(function(e,t){i.initFileSystem().then(function(){i.initWorker().then(function(){e(i.szUUID)},function(e){t(e)})},function(e){t(e)})});return t}},{key:"initFileSystem",value:function e(){var i=this;this.oFileSystem=new p(this.szUUID,this.szFileName,this.iStreamType,this.options);var t=new Promise(function(e,t){i.oFileSystem.init().then(function(){e()},function(e){t(e)})});return t}},{key:"initWorker",value:function e(){var o=this;var t=new Promise(function(r){o.oWorker=new Worker(o.szBasePath+"/systemTransform-worker.min.js");o.oWorker.onmessage=function(e){var t=e.data;var i=o.iPackType;if(o.options.iPackage===1){i=12}if("loaded"===t.type){o.oWorker.postMessage({type:"create",buf:o.aHeadBuf.buffer,len:40,packType:i},[o.aHeadBuf.buffer])}else if("created"===t.type){r()}else if("outputData"===t.type){var n=new Uint8Array(t.buf);if(o.options.iPackage===1){if(o.bHead){o.oFileSystem.writeFileHeader(n);o.bHead=false}else{o.oFileSystem.writeFileContent(n)}}else{if(h===t.dType){o.oFileSystem.writeFileHeader(n)}else{o.oFileSystem.writeFileContent(n)}}}}});return t}},{key:"inputData",value:function e(t){if(this.oWorker){var i=new Uint8Array(t);this.oWorker.postMessage({type:"inputData",buf:i.buffer,len:i.length},[i.buffer])}}},{key:"stopRecord",value:function e(){var i=this;var t=new Promise(function(e,t){if(i.oWorker){i.oWorker.postMessage({type:"release"})}else{t()}if(i.oFileSystem){i.oFileSystem.stopWriteFile().then(function(){i.bHead=true;e()},function(){t()})}else{t()}});return t}}]);return s}();var y=function(){var h=Symbol("STORAGELIST");var e=function(){function i(e,t){l(this,i);this.szBasePath=e;this[h]={};this.options={iFileSize:1024*1024*1024};Object.assign(this.options,t)}n(i,[{key:"startRecord",value:function e(t,i,n,r,o){var a=this;var s=c.default.v4();var l=Object.assign({},this.options,o);var u=new v(this.szBasePath,s,t,i,n,r,l);var f=new Promise(function(t,i){u.init().then(function(e){a[h][e]=u;t(e)},function(e){i(e)})});return f}},{key:"inputData",value:function e(t,i){var n=this[h][t];if(n){n.inputData(i)}}},{key:"stopRecord",value:function e(n){var r=this;var t=new Promise(function(e,t){var i=r[h][n];if(i){i.stopRecord().then(function(){delete r[h][n];e()},function(){t()})}else{t()}});return t}}]);return i}();return e}();t.StorageManager=y},function(e,t,i){"use strict";Object.defineProperty(t,"__esModule",{value:true});t.ESCanvas=undefined;var o=function(){function n(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(e,n.key,n)}}return function(e,t,i){if(t)n(e.prototype,t);if(i)n(e,i);return e}}();var n=i(4);var s=r(n);function r(e){return e&&e.__esModule?e:{default:e}}function u(e,t){if(!e){throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return t&&(typeof t==="object"||typeof t==="function")?t:e}function T(e,t){if(typeof t!=="function"&&t!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof t)}e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}});if(t)Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t}function k(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var a=function(){var g=null;var f=Symbol("CANVAS");var S=Symbol("CONTEXT");var h=Symbol("SHAPES");var c=Symbol("DRAWSTATUS");var d=Symbol("SHAPETYPE");var p=Symbol("MAXSHAPENUMSUPPORT");var r=Symbol("SHAPESTYLE");var v=Symbol("POLYGONDRAWING");var y=Symbol("CURRENTSHAPEINFO");var m=Symbol("DRAWSHAPEMULTIONETIME");var n=Symbol("EVENTCALLBACK");function b(){g[S].clearRect(0,0,g.m_iCanvasWidth,g.m_iCanvasHeight);for(var e=0,t=g[h].length;e<t;e++){g[h][e].draw()}}function w(e){var t=g[h].length;if(t<g[p]){g[h].push(e)}}function i(){var r=false;var o=0;var a=0;var s="draw";var l=null;function u(){var e=-1;for(var t=0,i=g[h].length;t<i;t++){if(g[h][t].m_bChoosed){e=t;break}}return e}g[f][0].oncontextmenu=function(){return false};g[f][0].onselectstart=function(){return false};g[f].unbind();g[f].bind("mousedown",function(e){if(e.button===2){if(g[v]&&l){if(l.m_aPoint.length>=l.m_iMinClosed){l.m_bClosed=true;g[v]=false;l.setPointInfo(l.m_aPoint);w(l);b();r=false;if(!g[m]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}else if(e.button===0){o=e.offsetX;a=e.offsetY;s="draw";if(!g[v]){var t=u();if(t!==-1){if(g[h][t].inArc(e.offsetX,e.offsetY,5)){s="stretch"}}if(s!=="stretch"){for(var i=0,n=g[h].length;i<n;i++){if(g[h][i].inShape(e.offsetX,e.offsetY)&&g[h][i].m_iEditType!==2){g[h][i].m_bChoosed=true;g[h][i].getMouseDownPoints(e.offsetX,e.offsetY);s="drag";window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][i].m_szType,"onChoose",g[h][i].m_szId)}else{g[h][i].m_bChoosed=false}}}if(s==="drag"){g[f][0].style.cursor="move"}else{g[f][0].style.cursor="default"}if("draw"===s&&1===g[h].length&&1===g[h][0].m_iRedrawMode){g.deleteRepeatPolyonById(g[h][0].m_szId);g[c]=true}if(g[c]&&!g[m]){s="draw"}}if(s==="draw"){if(g[c]){if(g[p]<=g[h].length&&g[d]!=="Grid"&&g[d]!=="Point"){return}if(g[d]==="Rect"){l=new _;l.m_szTips=g[y].szTips||""}else if(g[d]==="Grid"){if(g[h].length===0){l=new P;w(l)}}else if(g[d]==="Polygon"){if(!g[v]){g[v]=true;l=new C;l.m_szId=g[y].szId||"";l.m_szTips=g[y].szTips||"";l.m_iMinClosed=g[y].iMinClosed||3;l.m_iMaxPointNum=g[y].iMaxPointNum||11;l.m_iPolygonType=g[y].iPolygonType;l.m_szDrawColor=g[y].szDrawColor;l.m_szFillColor=g[y].szFillColor;l.m_iTranslucent=g[y].iTranslucent;l.m_iRedrawMode=g[y].iRedrawMode}if(l.m_iPolygonType===1){l.addPoint(o,a);if(l.m_aPoint.length===l.m_iMaxPointNum){l.m_bClosed=true;g[v]=false;w(l);b();r=false;if(!g[m]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}else if(g[d]==="Point"){g.clearShapeByType("Point");l=new x;l.m_szId=g[y].szId||"";l.m_szDrawColor=g[y].szDrawColor;l.setPointInfo([[o,a]]);w(l);b()}}}r=true}});g[f].bind("mousemove",function(e){if(!g[v]){var t=u();if(t>-1){if(r){if(s==="drag"){g[h][t].drag(e.offsetX,e.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][t].m_szType,"onDrag",g[h][t].m_szId)}else if(s==="stretch"){g[h][t].stretch(e.offsetX,e.offsetY);window.onDrawShapeEvent&&window.onDrawShapeEvent(g[h][t].m_szType,"onStretch",g[h][t].m_szId)}}}else{if(g[c]){if(r){if(g[d]==="Rect"){l.move([[o,a],[e.offsetX,e.offsetY]])}else if(g[d]==="Grid"){g[h][0].move(o,a,e.offsetX,e.offsetY)}}}}}else{if(g[c]){if(r){if(g[d]==="Polygon"&&l.m_iPolygonType===0){l.m_bClosed=true}b();l.move(e.offsetX,e.offsetY,o,a)}}}});g[f].bind("mouseup",function(e){g[f][0].style.cursor="default";if(l!==null&&typeof l!=="undefined"&&s==="draw"){if(g[d]==="Rect"){if(Math.abs(e.offsetX-o)>2&&Math.abs(e.offsetY-a)>2){w(l);if(!g[m]){g[c]=false}}if(g[n]){var t={startPos:[],endPos:[]};if(e.offsetX>o&&e.offsetY>a){t.startPos=l.m_aPoint[0]||[e.offsetX,e.offsetY];t.endPos=l.m_aPoint[2]||[e.offsetX,e.offsetY]}else{t.startPos=l.m_aPoint[2]||[e.offsetX,e.offsetY];t.endPos=l.m_aPoint[0]||[e.offsetX,e.offsetY]}g[n]&&g[n](t);g.clearAllShape()}l=null}else if(g[d]==="Polygon"&&l.m_iPolygonType===0&&g[v]){if(Math.abs(e.offsetX-o)>2&&Math.abs(e.offsetY-a)>2){w(l);g[v]=false;if(!g[m]){g[c]=false}window.onDrawShapeEvent&&window.onDrawShapeEvent(l.m_szType,"onDrawEnd",l.m_szId)}}}if(!g[v]){r=false}else{r=true}if(!g[v]){b()}});g[f].bind("dblclick",function(){if(g[c]){if(g[d]==="Grid"){g[h][0].m_szGridMap="fffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffcfffffc";b()}}});g[f].bind("mouseout",function(){g[f][0].style.cursor="default";if(!g[v]){r=false}else{r=true}})}var e=function(){function e(){k(this,e);this.m_szId="";this.m_aPoint=[];this.m_bChoosed=false;this.m_szDrawColor=g[r].szDrawColor;this.m_szFillColor=g[r].szFillColor;this.m_iTranslucent=g[r].iTranslucent;this.m_iIndexChoosePoint=-1;this.m_iDriftStartX=0;this.m_iDriftStartY=0;this.m_oEdgePoints={top:{x:0,y:0},left:{x:0,y:0},right:{x:0,y:0},bottom:{x:0,y:0}};this.m_szTips="";this.m_iEditType=0;this.m_iMinClosed=3;this.m_iMaxPointNum=11;this.m_bClosed=false;this.m_iRedrawMode=0}o(e,[{key:"draw",value:function e(){}},{key:"drag",value:function e(t,i){if(this.m_iEditType!==2){var n=this.m_aPoint.length;var r=0;for(r=0;r<n;r++){if(this.m_aPoint[r][0]+t-this.m_iDriftStartX>g.m_iCanvasWidth||this.m_aPoint[r][1]+i-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][0]+t-this.m_iDriftStartX<0||this.m_aPoint[r][1]+i-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=i;return}}for(r=0;r<n;r++){this.m_aPoint[r][0]=this.m_aPoint[r][0]+t-this.m_iDriftStartX;this.m_aPoint[r][1]=this.m_aPoint[r][1]+i-this.m_iDriftStartY}this.m_iDriftStartX=t;this.m_iDriftStartY=i;this.setPointInfo(this.m_aPoint);b()}}},{key:"stretch",value:function e(t,i){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=t;this.m_aPoint[this.m_iIndexChoosePoint][1]=i}this.setPointInfo(this.m_aPoint);b()}}},{key:"inShape",value:function e(t,i){var n=false;var r=this.m_aPoint.length;for(var o=0,a=r-1;o<r;a=o++){if(this.m_aPoint[o][1]>i!==this.m_aPoint[a][1]>i&&t<(this.m_aPoint[a][0]-this.m_aPoint[o][0])*(i-this.m_aPoint[o][1])/(this.m_aPoint[a][1]-this.m_aPoint[o][1])+this.m_aPoint[o][0]){n=!n}}return n}},{key:"inArc",value:function e(t,i,n){var r=false;for(var o=0,a=this.m_aPoint.length;o<a;o++){var s=Math.sqrt((t-this.m_aPoint[o][0])*(t-this.m_aPoint[o][0])+(i-this.m_aPoint[o][1])*(i-this.m_aPoint[o][1]));if(s<n){r=true;this.m_iIndexChoosePoint=o;break}}return r}},{key:"getMouseDownPoints",value:function e(t,i){this.m_iDriftStartX=t;this.m_iDriftStartY=i}},{key:"getPointInfo",value:function e(){return this.m_aPoint}},{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"&&t.length>0){this.m_aPoint=t;this.setEdgePoints(t)}}},{key:"addPoint",value:function e(t,i){if(this.m_aPoint.length<this.m_iMaxPointNum){this.m_aPoint.push([t,i])}if(this.m_aPoint.length===this.m_iMaxPointNum){this.setPointInfo(this.m_aPoint)}}},{key:"setEdgePoints",value:function e(t){for(var i=0,n=t.length;i<n;i++){if(i===0){this.m_oEdgePoints.top.x=t[i][0];this.m_oEdgePoints.top.y=t[i][1];this.m_oEdgePoints.left.x=t[i][0];this.m_oEdgePoints.left.y=t[i][1];this.m_oEdgePoints.right.x=t[i][0];this.m_oEdgePoints.right.y=t[i][1];this.m_oEdgePoints.bottom.x=t[i][0];this.m_oEdgePoints.bottom.y=t[i][1]}else{if(t[i][1]<this.m_oEdgePoints.top.y){this.m_oEdgePoints.top.x=t[i][0];this.m_oEdgePoints.top.y=t[i][1]}if(t[i][0]>this.m_oEdgePoints.right.x){this.m_oEdgePoints.right.x=t[i][0];this.m_oEdgePoints.right.y=t[i][1]}if(t[i][1]>this.m_oEdgePoints.bottom.y){this.m_oEdgePoints.bottom.x=t[i][0];this.m_oEdgePoints.bottom.y=t[i][1]}if(t[i][0]<this.m_oEdgePoints.left.x){this.m_oEdgePoints.left.x=t[i][0];this.m_oEdgePoints.left.y=t[i][1]}}}}}]);return e}();var _=function(e){T(t,e);function t(){k(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Rect";return e}o(t,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){var i=t[0][0];var n=t[0][1];var r=t[0][0];var o=t[0][1];for(var a=0,s=t.length;a<s;a++){if(i>t[a][0]){i=t[a][0]}if(n>t[a][1]){n=t[a][1]}if(r<t[a][0]){r=t[a][0]}if(o<t[a][1]){o=t[a][1]}}this.m_aPoint=[[i,n],[r,n],[r,o],[i,o]]}}},{key:"draw",value:function e(){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;var t=this.m_aPoint[0][0];var i=this.m_aPoint[0][1];var n=this.m_aPoint[2][0]-t;var r=this.m_aPoint[2][1]-i;g[S].globalAlpha=this.m_iTranslucent;g[S].fillRect(t,i,n,r);g[S].globalAlpha=1;g[S].fillText(this.m_szTips,(t+this.m_aPoint[2][0])/2,(i+this.m_aPoint[2][1])/2);if(this.m_bChoosed){var o=Math.round(n/2);var a=Math.round(r/2);if(this.m_iEditType===0){var s=[t,t+o,t+n,t,t+n,t,t+o,t+n];var l=[i,i,i,i+a,i+a,i+r,i+r,i+r];for(var u=0;u<8;u++){g[S].beginPath();g[S].arc(s[u],l[u],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}}}g[S].strokeRect(t,i,n,r)}},{key:"stretch",value:function e(t,i){if(this.m_iEditType===0){if(this.m_iIndexChoosePoint===0){if(t<this.m_aPoint[2][0]&&i<this.m_aPoint[2][1]){this.m_aPoint[0][0]=t;this.m_aPoint[0][1]=i;this.m_aPoint[3][0]=t;this.m_aPoint[1][1]=i}}else if(this.m_iIndexChoosePoint===1){if(i<this.m_aPoint[2][1]){this.m_aPoint[0][1]=i;this.m_aPoint[1][1]=i}}else if(this.m_iIndexChoosePoint===2){if(t>this.m_aPoint[3][0]&&i<this.m_aPoint[3][1]){this.m_aPoint[1][0]=t;this.m_aPoint[1][1]=i;this.m_aPoint[2][0]=t;this.m_aPoint[0][1]=i}}else if(this.m_iIndexChoosePoint===3){if(t<this.m_aPoint[2][0]){this.m_aPoint[0][0]=t;this.m_aPoint[3][0]=t}}else if(this.m_iIndexChoosePoint===4){if(t>this.m_aPoint[0][0]){this.m_aPoint[1][0]=t;this.m_aPoint[2][0]=t}}else if(this.m_iIndexChoosePoint===5){if(t<this.m_aPoint[1][0]&&i>this.m_aPoint[1][1]){this.m_aPoint[3][0]=t;this.m_aPoint[3][1]=i;this.m_aPoint[0][0]=t;this.m_aPoint[2][1]=i}}else if(this.m_iIndexChoosePoint===6){if(i>this.m_aPoint[1][1]){this.m_aPoint[2][1]=i;this.m_aPoint[3][1]=i}}else if(this.m_iIndexChoosePoint===7){if(t>this.m_aPoint[0][0]&&i>this.m_aPoint[0][1]){this.m_aPoint[2][0]=t;this.m_aPoint[2][1]=i;this.m_aPoint[1][0]=t;this.m_aPoint[3][1]=i}}b()}}},{key:"move",value:function e(t){b();this.m_bChoosed=true;var i=t[0][0];var n=t[0][1];var r=t[1][0];var o=t[1][1];this.setPointInfo([[i,n],[r,n],[r,o],[i,o]]);this.draw()}},{key:"inArc",value:function e(t,i,n){var r=this.m_aPoint[0][0];var o=this.m_aPoint[0][1];var a=this.m_aPoint[2][0]-r;var s=this.m_aPoint[2][1]-o;var l=Math.round(a/2);var u=Math.round(s/2);var f=[r,r+l,r+a,r,r+a,r,r+l,r+a];var h=[o,o,o,o+u,o+u,o+s,o+s,o+s];for(var c=0;c<8;c++){var d=Math.sqrt((t-f[c])*(t-f[c])+(i-h[c])*(i-h[c]));if(d<n){this.m_iIndexChoosePoint=c;return true}}return false}}]);return t}(e);var l=function(e){T(n,e);function n(e,t){k(this,n);var i=u(this,(n.__proto__||Object.getPrototypeOf(n)).call(this));i.m_szType="RectOSD";i.m_szOSDType="overlay-date";i.m_szText=e||"";i.m_szEnabled=t||"";i.m_szDateStyle="";i.m_szClockType="";i.m_szDisplayWeek="";i.m_szId="";i.m_szAlignment="0";return i}o(n,[{key:"draw",value:function e(){if(this.m_szEnabled==="true"){var t=this.m_aPoint[0][0];var i=this.m_aPoint[0][1];var n=this.m_aPoint[2][0]-t;var r=this.m_aPoint[2][1]-i;g[S].beginPath();g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].rect(t,i,n,r);g[S].font="15px serif";g[S].strokeText(this.m_szText,t,i+15);g[S].stroke()}}},{key:"drag",value:function e(t,i){var n=this.m_aPoint.length;var r=0;if("0"===this.m_szAlignment){for(r=0;r<n;r++){if(this.m_aPoint[r][1]+i-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][0]+t-this.m_iDriftStartX<0||this.m_aPoint[r][1]+i-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=i;return}}for(r=0;r<n;r++){this.m_aPoint[r][0]=this.m_aPoint[r][0]+t-this.m_iDriftStartX;this.m_aPoint[r][1]=this.m_aPoint[r][1]+i-this.m_iDriftStartY}}else if("1"===this.m_szAlignment||"2"===this.m_szAlignment){for(r=0;r<n;r++){if(this.m_aPoint[r][1]+i-this.m_iDriftStartY>g.m_iCanvasHeight||this.m_aPoint[r][1]+i-this.m_iDriftStartY<0){this.m_iDriftStartX=t;this.m_iDriftStartY=i;return}}for(r=0;r<n;r++){this.m_aPoint[r][1]=this.m_aPoint[r][1]+i-this.m_iDriftStartY}}this.m_iDriftStartX=t;this.m_iDriftStartY=i;this.setEdgePoints(this.m_aPoint);b()}},{key:"stretch",value:function e(){}}]);return n}(e);var P=function(e){T(t,e);function t(){k(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Grid";e.m_iGridColNum=22;e.m_iGridRowNum=18;e.m_szGridMap="";e.m_aAddGridMap=[];return e}o(t,[{key:"draw",value:function e(){var t=g.m_iCanvasWidth/this.m_iGridColNum;var i=g.m_iCanvasHeight/this.m_iGridRowNum;var n="";for(var r=0;r<this.m_iGridRowNum;r++){var o=this.m_szGridMap.substring(r*6,r*6+6);var a=parseInt("f"+o,16).toString(2).split("").slice(4);var s="";for(var l=0;l<this.m_iGridColNum;l++){var u="";if(a[l]==="1"){g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].strokeRect(t*l,i*r,t,i);u="1"}else{u="0"}if(this.m_aAddGridMap.length){if(this.m_aAddGridMap[r][l]===1){g[S].strokeStyle=this.m_szDrawColor;g[S].strokeRect(t*l,i*r,t,i);u="1"}}s+=u}n+=parseInt("1111"+s+"00",2).toString(16).substring(1)}this.m_szGridMap=n}},{key:"move",value:function e(t,i,n,r){var o=g.m_iCanvasWidth/this.m_iGridColNum;var a=g.m_iCanvasHeight/this.m_iGridRowNum;var s=Math.floor(t/o);var l=Math.floor(i/a);var u=Math.floor(Math.abs(n-t)/o);var f=Math.floor(Math.abs(r-i)/a);var h=1;var c=1;if(n-t>0){h=1}else{h=-1}if(r-i>0){c=1}else{c=-1}var d=[];for(var p=0;p<this.m_iGridRowNum;p++){d[p]=[];for(var v=0;v<this.m_iGridColNum;v++){if(h===1){if(c===1){if(p>=l&&p<=l+f&&v>=s&&v<=s+u){d[p][v]=1}else{d[p][v]=0}}else{if(p<=l&&p>=l-f&&v>=s&&v<=s+u){d[p][v]=1}else{d[p][v]=0}}}else{if(c===1){if(p>=l&&p<=l+f&&v<=s&&v>=s-u){d[p][v]=1}else{d[p][v]=0}}else{if(p<=l&&p>=l-f&&v<=s&&v>=s-u){d[p][v]=1}else{d[p][v]=0}}}}}this.m_aAddGridMap=d;this.draw()}}]);return t}(e);var a=function(e){T(t,e);function t(){k(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Line";e.m_iLineType=0;e.m_iDirection=0;e.m_iArrowType=0;e.m_aCrossArrowPoint=[];return e}o(t,[{key:"draw",value:function e(){if(this.m_iLineType===0){this.drawNormalLine()}else if(this.m_iLineType===1){this.drawArrowLine()}else if(this.m_iLineType===3){this.drawCrossLine()}else if(this.m_iLineType===4){this.drawLineCount()}}},{key:"drawNormalLine",value:function e(){g[S].globalAlpha=1;if(this.m_aPoint.length>0){g[S].beginPath();g[S].strokeStyle=this.m_szDrawColor;g[S].lineWidth=2;g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(var t=1,i=this.m_aPoint.length;t<i;t++){g[S].lineTo(this.m_aPoint[t][0],this.m_aPoint[t][1])}g[S].stroke();if(this.m_bChoosed){for(var n=0,r=this.m_aPoint.length;n<r;n++){g[S].beginPath();g[S].fillStyle=this.m_szDrawColor;g[S].arc(this.m_aPoint[n][0],this.m_aPoint[n][1],3,0,Math.PI*2,true);g[S].closePath();g[S].fill()}}if(this.m_szTips!==""){g[S].strokeStyle=this.m_szDrawColor;g[S].fillText(this.m_szTips,this.m_aPoint[0][0]+10,this.m_aPoint[0][1]+4)}}}},{key:"drawArrowLine",value:function e(t,i,n,r,o,a,s,l){a=typeof a!=="undefined"?a:30;s=typeof s!=="undefined"?s:10;l=typeof l!=="undefined"?l:1;var u=Math.atan2(n-o,i-r)*180/Math.PI;var f=(u+a)*Math.PI/180;var h=(u-a)*Math.PI/180;var c=s*Math.cos(f);var d=s*Math.sin(f);var p=s*Math.cos(h);var v=s*Math.sin(h);g[S].save();g[S].beginPath();var y=i-c;var m=n-d;g[S].moveTo(y,m);g[S].lineTo(i,n);y=i-p;m=n-v;g[S].lineTo(y,m);g[S].moveTo(i,n);g[S].lineTo(r,o);if(t===1){y=r+c;m=o+d;g[S].moveTo(y,m);g[S].lineTo(r,o);y=r+p;m=o+v;g[S].lineTo(y,m)}g[S].strokeStyle=this.m_szDrawColor;g[S].lineWidth=l;g[S].stroke();g[S].restore()}},{key:"drawCrossLine",value:function e(){this.drawNormalLine();var t=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var i=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var n=Math.atan2(i-this.m_aPoint[0][1],t-this.m_aPoint[0][0])*180/Math.PI;var r=(n+90)*Math.PI/180;var o=(n-90)*Math.PI/180;var a=25*Math.cos(r);var s=25*Math.sin(r);var l=25*Math.cos(o);var u=25*Math.sin(o);var f=0;var h=0;f=t-a;h=i-s;var c=0;var d=0;if(this.m_iDirection===0){c=-10;d=-15}else if(this.m_iDirection===1){c=10;d=10}else{c=10;d=-15}if(this.m_iDirection!==0){this.drawArrowLine(0,f,h,t,i)}g[S].strokeStyle=this.m_szDrawColor;g[S].font="8px";g[S].strokeText("A",f+c,h+4);f=t-l;h=i-u;if(this.m_iDirection!==1){this.drawArrowLine(0,f,h,t,i)}g[S].strokeStyle=this.m_szDrawColor;g[S].font="8px";g[S].strokeText("B",f+d,h+4)}},{key:"drawLineCount",value:function e(){this.drawNormalLine();var t=(this.m_aPoint[0][0]+this.m_aPoint[1][0])/2;var i=(this.m_aPoint[0][1]+this.m_aPoint[1][1])/2;var n=Math.atan2(i-this.m_aPoint[0][1],t-this.m_aPoint[0][0])*180/Math.PI;var r=(n+90)*Math.PI/180;var o=(n-90)*Math.PI/180;var a=25*Math.cos(r);var s=25*Math.sin(r);var l=25*Math.cos(o);var u=25*Math.sin(o);var f=0;var h=0;f=t-a;h=i-s;if(this.m_iArrowType===1){f=t-l;h=i-u;this.drawArrowLine(0,f,h,t,i)}else if(this.m_iArrowType===0){this.drawArrowLine(0,f,h,t,i)}this.m_aCrossArrowPoint=[[t,i],[f,h]]}},{key:"inShape",value:function e(t,i){var n=false;for(var r=0,o=this.m_aPoint.length-1;r<o;r++){var a=Math.sqrt((this.m_aPoint[r+1][0]-this.m_aPoint[r][0])*(this.m_aPoint[r+1][0]-this.m_aPoint[r][0])+(this.m_aPoint[r+1][1]-this.m_aPoint[r][1])*(this.m_aPoint[r+1][1]-this.m_aPoint[r][1]));var s=Math.sqrt((t-this.m_aPoint[r][0])*(t-this.m_aPoint[r][0])+(i-this.m_aPoint[r][1])*(i-this.m_aPoint[r][1]));var l=Math.sqrt((t-this.m_aPoint[r+1][0])*(t-this.m_aPoint[r+1][0])+(i-this.m_aPoint[r+1][1])*(i-this.m_aPoint[r+1][1]));if(s+l-a<1){n=true}}return n}}]);return t}(e);var C=function(e){T(t,e);function t(){k(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Polygon";e.m_iPolygonType=1;return e}o(t,[{key:"setPointInfo",value:function e(t){if(t!==null&&typeof t!=="undefined"){if(this.m_iPolygonType===0){var i=t[0][0];var n=t[0][1];var r=t[0][0];var o=t[0][1];for(var a=0,s=t.length;a<s;a++){if(i>t[a][0]){i=t[a][0]}if(n>t[a][1]){n=t[a][1]}if(r<t[a][0]){r=t[a][0]}if(o<t[a][1]){o=t[a][1]}}this.m_aPoint=[[i,n],[r,n],[r,o],[i,o]]}else if(this.m_iPolygonType===1){this.m_aPoint=t}else{this.m_aPoint=t}this.setEdgePoints(t)}}},{key:"draw",value:function e(){if(this.m_aPoint.length>0){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;var t=0;var i=0;if(this.m_bChoosed){for(t=0,i=this.m_aPoint.length;t<i;t++){g[S].beginPath();g[S].arc(this.m_aPoint[t][0],this.m_aPoint[t][1],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}}g[S].beginPath();g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(t=0,i=this.m_aPoint.length;t<i;t++){if(t!==0){g[S].lineTo(this.m_aPoint[t][0],this.m_aPoint[t][1])}}g[S].stroke();if(this.m_bClosed){g[S].fillText(this.m_szTips,(this.m_oEdgePoints.left.x+this.m_oEdgePoints.right.x)/2,(this.m_oEdgePoints.top.y+this.m_oEdgePoints.bottom.y)/2);g[S].closePath();g[S].stroke();g[S].globalAlpha=this.m_iTranslucent;g[S].fill()}}}},{key:"move",value:function e(t,i,n,r){if(this.m_iPolygonType===1){if(this.m_aPoint.length<this.m_iMaxPointNum&&this.m_aPoint.length>0){g[S].fillStyle=this.m_szFillColor;g[S].strokeStyle=this.m_szDrawColor;g[S].globalAlpha=1;var o=0;var a=0;for(o=0,a=this.m_aPoint.length;o<a;o++){g[S].beginPath();g[S].arc(this.m_aPoint[o][0],this.m_aPoint[o][1],3,0,360,false);g[S].fillStyle=this.m_szDrawColor;g[S].closePath();g[S].fill()}g[S].beginPath();g[S].moveTo(this.m_aPoint[0][0],this.m_aPoint[0][1]);for(o=0,a=this.m_aPoint.length;o<a;o++){if(o!==0){g[S].lineTo(this.m_aPoint[o][0],this.m_aPoint[o][1])}}g[S].lineTo(t,i);g[S].closePath();g[S].stroke()}}else if(this.m_iPolygonType===0){this.m_bChoosed=true;var s=n;var l=r;var u=t;var f=i;this.setPointInfo([[s,l],[u,l],[u,f],[s,f]]);this.draw()}}},{key:"stretch",value:function e(t,i){if(this.m_iEditType===0){if(this.m_iPolygonType===1){if(this.m_iIndexChoosePoint!==-1){this.m_aPoint[this.m_iIndexChoosePoint][0]=t;this.m_aPoint[this.m_iIndexChoosePoint][1]=i}}else{if(this.m_iIndexChoosePoint===0){if(t<this.m_aPoint[2][0]&&i<this.m_aPoint[2][1]){this.m_aPoint[0][0]=t;this.m_aPoint[0][1]=i;this.m_aPoint[3][0]=t;this.m_aPoint[1][1]=i}}else if(this.m_iIndexChoosePoint===1){if(t>this.m_aPoint[3][0]&&i<this.m_aPoint[3][1]){this.m_aPoint[1][0]=t;this.m_aPoint[1][1]=i;this.m_aPoint[2][0]=t;this.m_aPoint[0][1]=i}}else if(this.m_iIndexChoosePoint===2){if(t>this.m_aPoint[0][0]&&i>this.m_aPoint[0][1]){this.m_aPoint[2][0]=t;this.m_aPoint[2][1]=i;this.m_aPoint[1][0]=t;this.m_aPoint[3][1]=i}}else if(this.m_iIndexChoosePoint===3){if(t<this.m_aPoint[1][0]&&i>this.m_aPoint[1][1]){this.m_aPoint[3][0]=t;this.m_aPoint[3][1]=i;this.m_aPoint[0][0]=t;this.m_aPoint[2][1]=i}}}this.setPointInfo(this.m_aPoint);b()}}}]);return t}(e);var x=function(e){T(t,e);function t(){k(this,t);var e=u(this,(t.__proto__||Object.getPrototypeOf(t)).call(this));e.m_szType="Point";e.m_szId="";return e}o(t,[{key:"draw",value:function e(){g[S].beginPath();g[S].fillStyle=this.m_szDrawColor;g[S].globalAlpha=1;g[S].arc(this.m_aPoint[0][0],this.m_aPoint[0][1],10,0,Math.PI*2,true);g[S].closePath();g[S].fill()}},{key:"drag",value:function e(){}},{key:"stretch",value:function e(){}}]);return t}(e);var t=function(){function t(e){k(this,t);g=this;this[f]=(0,s.default)("#"+e);this[S]=this[f][0].getContext("2d");this[h]=[];this[c]=false;this[d]="Rect";this[p]=10;this[m]=true;this[y]={};this[n]=null;this[r]={szDrawColor:"#ff0000",szFillColor:"#343434",iTranslucent:.7};this[v]=false;this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height();this.m_iHorizontalResolution=0;this.m_iVerticalResolution=0;this.m_szDisplayMode="";this.m_szVideoFormat="";i();this[h].length=0}o(t,[{key:"setDrawMutiShapeOneTime",value:function e(t){this[m]=t}},{key:"setMaxShapeSupport",value:function e(t){this[p]=t}},{key:"getMaxShapeSupport",value:function e(){return this[p]}},{key:"setDrawStatus",value:function e(t,i){this[c]=t;if(i&&t){this[n]=i}if(!t){this[n]=null}}},{key:"setShapeType",value:function e(t){this[d]=t;b()}},{key:"setCurrentShapeInfo",value:function e(t){this[y]=t||{szId:"",szTips:"",iMinClosed:3,iMaxPointNum:11,iPolygonType:1,iRedrawMode:0}}},{key:"getShapeType",value:function e(){return this[d]}},{key:"getAllShapesInfo",value:function e(){var t=[];for(var i=0,n=this[h].length;i<n;i++){if(this[h][i].m_szType==="Grid"){t.push({szType:this[h][i].m_szType,szGridMap:this[h][i].m_szGridMap,iGridColNum:this[h][i].m_iGridColNum,iGridRowNum:this[h][i].m_iGridRowNum})}else if(this[h][i].m_szType==="RectOSD"){t.push({szType:this[h][i].m_szType,szText:this[h][i].m_szText,szEnabled:this[h][i].m_szEnabled,szOSDType:this[h][i].m_szOSDType,iPositionX:this[h][i].m_aPoint[0][0],iPositionY:this[h][i].m_aPoint[0][1],szDateStyle:this[h][i].m_szDateStyle,szClockType:this[h][i].m_szClockType,szDisplayWeek:this[h][i].m_szDisplayWeek,szId:this[h][i].m_szId,szAlignment:this[h][i].m_szAlignment})}else{t.push({szType:this[h][i].m_szType,aPoint:this[h][i].m_aPoint,szId:this[h][i].m_szId,bChoosed:this[h][i].m_bChoosed})}}return t}},{key:"deleteRepeatPolyonById",value:function e(t){var i=this.getAllShapesInfo();var n=i.length;if(n>0){for(var r=0;r<n;r++){if(i[r].szType==="Polygon"){if(i[r].szId===t){this.deleteShape(r)}}}}}},{key:"getShapesInfoByType",value:function e(t){var i=[];for(var n=0,r=this[h].length;n<r;n++){if(this[h][n].m_szType===t){if(this[h][n].m_szType==="Grid"){i.push({szType:this[h][n].m_szType,szGridMap:this[h][n].m_szGridMap,iGridColNum:this[h][n].m_iGridColNum,iGridRowNum:this[h][n].m_iGridRowNum})}else if(this[h][n].m_szType==="RectOSD"){i.push({szType:this[h][n].m_szType,szText:this[h][n].m_szText,szEnabled:this[h][n].m_szEnabled,szOSDType:this[h][n].m_szOSDType,iPositionX:this[h][n].m_aPoint[0][0],iPositionY:this[h][n].m_aPoint[0][1],szDateStyle:this[h][n].m_szDateStyle,szClockType:this[h][n].m_szClockType,szDisplayWeek:this[h][n].m_szDisplayWeek,szId:this[h][n].m_szId,szAlignment:this[h][n].m_szAlignment})}else if(t==="Polygon"){i.push({szType:this[h][n].m_szType,szId:this[h][n].m_szId,iPolygonType:this[h][n].m_iPolygonType,iMinClosed:this[h][n].m_iMinClosed,iMaxPointNum:this[h][n].m_iMaxPointNum,iEditType:this[h][n].m_iEditType,aPoint:this[h][n].m_aPoint,bClosed:this[h][n].m_bClosed,szTips:this[h][n].m_szTips,szDrawColor:this[h][n].m_szDrawColor,szFillColor:this[h][n].m_szFillColor,iTranslucent:this[h][n].m_iTranslucent})}else if(t==="Line"){i.push({szType:this[h][n].m_szType,szId:this[h][n].m_szId,aPoint:this[h][n].m_aPoint,szTips:this[h][n].m_szTips,iLineType:this[h][n].m_iLineType,iDirection:this[h][n].m_iDirection,iArrowType:this[h][n].m_iArrowType,szDrawColor:this[h][n].m_szDrawColor,aCrossArrowPoint:this[h][n].m_aCrossArrowPoint})}else if(t==="Rect"){i.push({szType:this[h][n].m_szType,iEditType:this[h][n].m_iEditType,aPoint:this[h][n].m_aPoint,szTips:this[h][n].m_szTips,szDrawColor:this[h][n].m_szDrawColor,szFillColor:this[h][n].m_szFillColor,iTranslucent:this[h][n].m_iTranslucent})}else{i.push({szType:this[h][n].m_szType,aPoint:this[h][n].m_aPoint})}}}return i}},{key:"setShapesInfoByType",value:function e(t,i){if(!i){i=[]}var n=null;if(t==="Rect"||t==="Polygon"||t==="Line"||t==="Point"){for(var r=0,o=i.length;r<o;r++){if(t==="Rect"){n=new _;n.m_iEditType=i[r].iEditType;n.m_szTips=i[r].szTips||"";n.m_szDrawColor=i[r].szDrawColor;n.m_szFillColor=i[r].szFillColor;n.m_iTranslucent=i[r].iTranslucent;n.m_iRedrawMode=i[r].iRedrawMode}else if(t==="Polygon"){n=new C;if(i[r].iPolygonType===0){n.m_bClosed=true}else{n.m_bClosed=i[r].bClosed}n.m_szTips=i[r].szTips||"";n.m_szId=i[r].szId||"";n.m_iPolygonType=i[r].iPolygonType;n.m_iMinClosed=i[r].iMinClosed||3;n.m_iMaxPointNum=i[r].iMaxPointNum||11;n.m_iEditType=i[r].iEditType;n.m_szDrawColor=i[r].szDrawColor;n.m_szFillColor=i[r].szFillColor;n.m_iTranslucent=i[r].iTranslucent;n.m_iRedrawMode=i[r].iRedrawMode}else if(t==="Line"){n=new a;n.m_iLineType=i[r].iLineType;n.m_szTips=i[r].szTips||"";n.m_szId=i[r].szId;n.m_iDirection=i[r].iDirection;n.m_iArrowType=i[r].iArrowType;n.m_szDrawColor=i[r].szDrawColor;n.setPointInfo(i[r].aPoint)}else if(t==="Point"){n=new x;n.m_szId=i[r].szId;n.m_szDrawColor=i[r].szDrawColor;n.setPointInfo(i[r].aPoint)}n.setPointInfo(i[r].aPoint);if(r===0){n.m_bChoosed=true}w(n)}}else if(t==="Grid"){n=new P;n.m_szGridMap=i[0].szGridMap||"";n.m_iGridColNum=i[0].iGridColNum||22;n.m_iGridRowNum=i[0].iGridRowNum||18;w(n)}b()}},{key:"addOSDShape",value:function e(t,i,n,r,o){if(!n&&!r){n=0;r=0}if(!o){o={}}var a=new l(t,i);var s=t.replace(/[^x00-xff]/g,"rr").length*10;a.m_szOSDType=o.szOSDType||"";a.m_szDateStyle=o.szDateStyle||"";a.m_szClockType=o.szClockType||"";a.m_szDisplayWeek=o.szDisplayWeek||"";a.m_szId=o.szId||"";a.m_szAlignment=""+o.szAlignment||"0";if("0"===a.m_szAlignment){a.m_aPoint=[[n,r],[s+n,r],[s+n,r+20],[n,r+20]]}else if("1"===a.m_szAlignment){a.m_aPoint=[[0,r],[s,r],[s,r+20],[0,r+20]]}else if("2"===a.m_szAlignment){a.m_aPoint=[[this.m_iCanvasWidth-s,r],[this.m_iCanvasWidth,r],[this.m_iCanvasWidth,r+20],[this.m_iCanvasWidth-s,r+20]]}else{a.m_aPoint=[[n,r],[s+n,r],[s+n,r+20],[n,r+20]]}w(a);b()}},{key:"selectShapeById",value:function e(t,i){for(var n=0,r=g[h].length;n<r;n++){if(t===g[h][n].m_szType){if(i===g[h][n].m_szId){g[h][n].m_bChoosed=true}else{g[h][n].m_bChoosed=false}}}b()}},{key:"setCanvasSize",value:function e(t,i){if(t>0&&i>0){this.m_iCanvasWidth=t;this.m_iCanvasHeight=i;b()}}},{key:"setDrawStyle",value:function e(t,i,n){this[r]={szDrawColor:t,szFillColor:i,iTranslucent:n}}},{key:"clearAllShape",value:function e(){this[h].length=0;g[v]=false;b()}},{key:"clearShapeByType",value:function e(t){var i=this[h].length;for(var n=i;n>0;n--){if(this[h][n-1].m_szType===t){if(t==="Grid"){this[h][n-1].m_szGridMap="";this[h][n-1].m_aAddGridMap=[]}else{this[h].splice(n-1,1)}}}if("Polygon"===t){g[v]=false}b()}},{key:"deleteShape",value:function e(t){if(this[h].length>t){this[h].splice(t,1)}b()}},{key:"updateCanvas",value:function e(t){this[f]=(0,s.default)("#"+t);this[S]=this[f][0].getContext("2d");this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height();i()}},{key:"resizeCanvas",value:function e(){this.m_iCanvasWidth=this[f].width();this.m_iCanvasHeight=this[f].height()}},{key:"canvasRedraw",value:function e(){b()}}]);return t}();return t}();t.ESCanvas=a}])});