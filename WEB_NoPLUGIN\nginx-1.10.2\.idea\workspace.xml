<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="f6f911eb-7d54-48c2-afa0-fab6923ab839" name="Default" comment="" />
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf />
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>127.0.0.1</find>
    </findStrings>
    <dirStrings>
      <dir>D:\叶工的工作\开放包事宜\定制\DZ20180309_077_英国GB 无插件Web SDK\code\nginx-1.10.2</dir>
    </dirStrings>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodePackageJsonFileManager">
    <packageJsonPaths />
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="1912" />
    <option name="y" value="-8" />
    <option name="width" value="1936" />
    <option name="height" value="1096" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="nginx-1.10.2" type="b2602c69:ProjectViewProjectNode" />
              <item name="nginx-1.10.2" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="nginx-1.10.2" type="b2602c69:ProjectViewProjectNode" />
              <item name="nginx-1.10.2" type="462c0819:PsiDirectoryNode" />
              <item name="docs" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="nginx-1.10.2" type="b2602c69:ProjectViewProjectNode" />
              <item name="nginx-1.10.2" type="462c0819:PsiDirectoryNode" />
              <item name="html" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="nginx-1.10.2" type="b2602c69:ProjectViewProjectNode" />
              <item name="nginx-1.10.2" type="462c0819:PsiDirectoryNode" />
              <item name="logs" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="D:/Pre-Research/Other/component_gallery" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="f6f911eb-7d54-48c2-afa0-fab6923ab839" name="Default" comment="" />
      <created>1529993039221</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1529993039221</updated>
      <workItem from="1529993040760" duration="1874000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="1874000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="1912" y="-8" width="1936" height="1096" extended-state="6" />
    <layout>
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.25" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" show_stripe_button="false" />
      <window_info anchor="bottom" id="Event Log" side_tool="true" />
      <window_info active="true" anchor="bottom" id="Find" order="1" visible="true" weight="0.3156823" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Version Control" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Terminal" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info id="Favorites" side_tool="true" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Message" order="0" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/conf/nginx.conf">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="449">
          <caret line="32" lean-forward="true" selection-start-line="32" selection-end-line="32" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/conf/nginx.conf.demo">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="362">
          <caret line="37" column="30" lean-forward="true" selection-start-line="37" selection-start-column="20" selection-end-line="37" selection-end-column="30" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/stop.bat">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/start.bat">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
  </component>
</project>