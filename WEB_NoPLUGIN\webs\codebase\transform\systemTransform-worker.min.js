!function(){function e(){var e=new XMLHttpRequest;return e.open("GET","SystemTransform.js.mem"),e.responseType="arraybuffer",e.send(),e}self.Module={memoryInitializerRequest:e()},importScripts("SystemTransform.js"),Module.postRun.push(function(){postMessage({type:"loaded"})}),onmessage=function(e){var a=e.data;if("create"===a.type){var t=a.len,r=Module._malloc(t),o=Module.HEAPU8.subarray(r,r+t);o.set(new Uint8Array(a.buf));var s=a.packType,l=Module._ST_Create(r,t,s);0!=l?console.log("_ST_Create failed!"):(Module._ST_Start(),postMessage({type:"created"}))}else if("inputData"===a.type){var u=a.len,n=Module._malloc(u),o=Module.HEAPU8.subarray(n,n+u);o.set(new Uint8Array(a.buf));var l=Module._ST_InputData(0,n,u);Module._free(n)}else"release"===a.type&&(Module._ST_Stop(),Module._ST_Release(),close())}}();