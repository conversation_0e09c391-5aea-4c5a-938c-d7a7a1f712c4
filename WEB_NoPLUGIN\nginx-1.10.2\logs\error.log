2021/09/18 19:29:10 [error] 10456#9844: *8 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "127.0.0.1", referrer: "http://127.0.0.1/cn/demo.html"
2021/09/18 19:30:37 [error] 10456#9844: *25 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: 127.0.0.1, request: "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=c7e4c4848b4d3f50ffe70fcca78200e5 HTTP/1.1", upstream: "http://*************:7681/101?version=0.1&cipherSuites=0&token=c7e4c4848b4d3f50ffe70fcca78200e5", host: "127.0.0.1"
2021/09/18 19:30:38 [error] 10456#9844: *45 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "127.0.0.1", referrer: "http://127.0.0.1/cn/demo.html"
2021/09/18 19:31:08 [error] 10456#9844: *67 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase//playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/09/18 19:33:50 [error] 10456#9844: *60 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: 127.0.0.1, request: "GET /webSocketVideoCtrlProxy/?version=0.1&cipherSuites=0&token=ce098bdf1907a689e6cfe494c814a777 HTTP/1.1", upstream: "http://*************:7681/101?version=0.1&cipherSuites=0&token=ce098bdf1907a689e6cfe494c814a777", host: "127.0.0.1"
2021/09/18 19:34:06 [error] 10456#9844: *77 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "127.0.0.1", referrer: "http://127.0.0.1/cn/demo.html"
2021/09/18 19:34:56 [error] 10456#9844: *92 WSARecv() failed (10054: An existing connection was forcibly closed by the remote host) while proxying upgraded connection, client: 127.0.0.1, server: 127.0.0.1, request: "GET /webSocketVideoCtrlProxy/?version=1.0&cipherSuites=1&token=2a77356a98196f9b430b1ed8508d9085 HTTP/1.1", upstream: "http://*************:7681/101?version=1.0&cipherSuites=1&token=2a77356a98196f9b430b1ed8508d9085", host: "127.0.0.1"
2021/10/14 20:23:06 [error] 11540#24100: *4 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:23:52 [error] 11540#24100: *24 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:24:14 [error] 11540#24100: *41 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:25:58 [error] 11540#24100: *25 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:36:35 [error] 11540#24100: *83 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:37:53 [error] 11540#24100: *119 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:39:32 [error] 11540#24100: *136 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:46:30 [error] 11540#24100: *142 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:48:08 [error] 11540#24100: *161 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:48:29 [error] 11540#24100: *162 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:53:47 [error] 11540#24100: *201 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 20:54:17 [error] 11540#24100: *231 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:07:04 [error] 15776#9668: *4 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:07:20 [error] 15776#9668: *4 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:10:34 [error] 15776#9668: *4 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:15:02 [error] 15776#9668: *56 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:16:37 [error] 15776#9668: *54 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2021/10/14 21:16:45 [error] 15776#9668: *80 CreateFile() "./../webs/codebase/playctrl/AudioRenderer.js.map" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /codebase/playctrl/AudioRenderer.js.map HTTP/1.1", host: "127.0.0.1"
2025/07/31 22:32:10 [error] 24368#24516: *1 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "www.google.com"
2025/07/31 22:32:10 [error] 24368#24516: *2 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
2025/07/31 22:32:10 [error] 24368#24516: *3 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
2025/07/31 22:32:10 [error] 24368#24516: *4 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
2025/07/31 22:32:10 [error] 24368#24516: *5 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
2025/07/31 22:32:10 [error] 24368#24516: *6 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
2025/07/31 22:32:10 [error] 24368#24516: *7 CreateFile() "./../webs/favicon.ico" failed (2: The system cannot find the file specified), client: 127.0.0.1, server: 127.0.0.1, request: "GET /favicon.ico HTTP/1.1", host: "localhost"
