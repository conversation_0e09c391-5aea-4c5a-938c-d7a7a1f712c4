
Изменения в nginx 1.26.3                                          05.02.2025

    *) Безопасность: недостаточная проверка в обработке виртуальных серверов
       при использовании SNI в TLSv1.3 позволяла повторно использовать
       SSL-сессию в контексте другого виртуального сервера, чтобы обойти
       проверку клиентских SSL-сертификатов (CVE-2025-23419).

    *) Исправление: в модуле ngx_http_mp4_module.
       Спасибо Nils Bars.

    *) Изменение: при использовании zlib-ng в логах появлялись сообщения
       "gzip filter failed to use preallocated memory".

    *) Исправление: nginx не мог собрать библиотеку libatomic из исходных
       текстов, если использовался параметр --with-libatomic=DIR.

    *) Исправление: теперь nginx игнорирует пакеты согласования версий QUIC
       от клиентов.

    *) Исправление: nginx не собирался на Solaris 10 и более ранних с
       модулем ngx_http_v3_module.

    *) Исправления в HTTP/3.


Изменения в nginx 1.26.2                                          14.08.2024

    *) Безопасность: обработка специально созданного mp4-файла модулем
       ngx_http_mp4_module могла приводить к падению рабочего процесса
       (CVE-2024-7347).
       Спасибо Nils Bars.


Изменения в nginx 1.26.1                                          29.05.2024

    *) Безопасность: при использовании HTTP/3 обработка специально созданной
       QUIC-сессии могла приводить к падению рабочего процесса, отправке
       клиенту содержимого памяти рабочего процесса на системах с MTU больше
       4096 байт, а также потенциально могла иметь другие последствия
       (CVE-2024-32760, CVE-2024-31079, CVE-2024-35200, CVE-2024-34161).
       Спасибо Nils Bars из CISPA.

    *) Исправление: уменьшено потребление памяти для долгоживущих запросов,
       если используются директивы gzip, gunzip, ssi, sub_filter или
       grpc_pass.

    *) Исправление: nginx не собирался gcc 14, если использовался параметр
       --with-libatomic.
       Спасибо Edgar Bonet.

    *) Исправление: в HTTP/3.


Изменения в nginx 1.26.0                                          23.04.2024

    *) Стабильная ветка 1.26.x.


Изменения в nginx 1.25.5                                          16.04.2024

    *) Добавление: виртуальные сервера в модуле stream.

    *) Добавление: модуль ngx_stream_pass_module.

    *) Добавление: параметры deferred, accept_filter и setfib директивы
       listen в модуле stream.

    *) Добавление: определение размера строки кеша процессора для некоторых
       архитектур.
       Спасибо Piotr Sikora.

    *) Добавление: поддержка Homebrew на Apple Silicon.
       Спасибо Piotr Sikora.

    *) Исправление: улучшения и исправления кросс-компиляции для Windows.
       Спасибо Piotr Sikora.

    *) Исправление: неожиданное закрытие соединения при использовании 0-RTT
       в QUIC.
       Спасибо Владимиру Хомутову.


Изменения в nginx 1.25.4                                          14.02.2024

    *) Безопасность: при использовании HTTP/3 в рабочем процессе мог
       произойти segmentation fault во время обработки специально созданной
       QUIC-сессии (CVE-2024-24989, CVE-2024-24990).

    *) Исправление: соединения с незавершенными AIO-операциями могли
       закрываться преждевременно во время плавного завершения старых
       рабочих процессов.

    *) Исправление: теперь nginx не пишет в лог сообщения об утечке сокетов,
       если во время плавного завершения старых рабочих процессов было
       запрошено быстрое завершение.

    *) Исправление: при использовании AIO в подзапросе могла происходить
       ошибка на сокете, утечка сокетов, либо segmentation fault в рабочем
       процессе (при SSL-проксировании).

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалось SSL-проксирование и директива image_filter, а
       ошибки с кодом 415 перенаправлялись с помощью директивы error_page.

    *) Исправления и улучшения в HTTP/3.


Изменения в nginx 1.25.3                                          24.10.2023

    *) Изменение: улучшено детектирование некорректного поведения клиентов
       при использовании HTTP/2.

    *) Добавление: уменьшение времени запуска при использовании большого
       количества location'ов.
       Спасибо Yusuke Nojima.

    *) Исправление: при использовании HTTP/2 без SSL в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 1.25.1.

    *) Исправление: строка "Status" в заголовке ответа бэкенда с пустой
       поясняющей фразой обрабатывалась некорректно.

    *) Исправление: утечки памяти во время переконфигурации при
       использовании библиотеки PCRE2.
       Спасибо ZhenZhong Wu.

    *) Исправления и улучшения в HTTP/3.


Изменения в nginx 1.25.2                                          15.08.2023

    *) Добавление: path MTU discovery при использовании HTTP/3.

    *) Добавление: поддержка шифра TLS_AES_128_CCM_SHA256 при использовании
       HTTP/3.

    *) Изменение: теперь при загрузке конфигурации OpenSSL nginx использует
       appname "nginx".

    *) Изменение: теперь nginx не пытается загружать конфигурацию OpenSSL,
       если для сборки OpenSSL использовался параметр --with-openssl и
       переменная окружения OPENSSL_CONF не установлена.

    *) Исправление: в переменной $body_bytes_sent при использовании HTTP/3.

    *) Исправление: в HTTP/3.


Изменения в nginx 1.25.1                                          13.06.2023

    *) Добавление: директива http2, позволяющая включать HTTP/2 в отдельных
       блоках server; параметр http2 директивы listen объявлен устаревшим.

    *) Изменение: поддержка HTTP/2 server push упразднена.

    *) Изменение: устаревшая директива ssl больше не поддерживается.

    *) Исправление: в HTTP/3 при использовании OpenSSL.


Изменения в nginx 1.25.0                                          23.05.2023

    *) Добавление: экспериментальная поддержка HTTP/3.


Изменения в nginx 1.23.4                                          28.03.2023

    *) Изменение: теперь протокол TLSv1.3 разрешён по умолчанию.

    *) Изменение: теперь nginx выдаёт предупреждение при переопределении
       параметров listen-сокета, задающих используемые протоколы.

    *) Изменение: теперь, если клиент использует pipelining, nginx закрывает
       соединения с ожиданием дополнительных данных (lingering close).

    *) Добавление: поддержка byte ranges для ответов модуля
       ngx_http_gzip_static_module.

    *) Исправление: диапазоны портов в директиве listen не работали; ошибка
       появилась в 1.23.3.
       Спасибо Валентину Бартеневу.

    *) Исправление: для обработки запроса мог быть выбран неверный location,
       если в конфигурации использовался префиксный location длиннее 255
       символов.

    *) Исправление: не-ASCII символы в именах файлов на Windows не
       поддерживались модулями ngx_http_autoindex_module и
       ngx_http_dav_module, а также директивой include.

    *) Изменение: уровень логгирования ошибок SSL "data length too long",
       "length too short", "bad legacy version", "no shared signature
       algorithms", "bad digest length", "missing sigalgs extension",
       "encrypted length too long", "bad length", "bad key update", "mixed
       handshake and non handshake data", "ccs received early", "data
       between ccs and finished", "packet length too long", "too many warn
       alerts", "record too small", и "got a fin before a ccs" понижен с
       уровня crit до info.

    *) Исправление: при использовании HTTP/2 и директивы error_page для
       перенаправления ошибок с кодом 400 могла происходить утечка сокетов.

    *) Исправление: сообщения об ошибках записи в syslog не содержали
       информации о том, что ошибки происходили в процессе записи в syslog.
       Спасибо Safar Safarly.

    *) Изменение: при использовании zlib-ng в логах появлялись сообщения
       "gzip filter failed to use preallocated memory".

    *) Исправление: в почтовом прокси-сервере.


Изменения в nginx 1.23.3                                          13.12.2022

    *) Исправление: при чтении заголовка протокола PROXY версии 2,
       содержащего большое количество TLV, могла возникать ошибка.

    *) Исправление: при использовании SSI для обработки подзапросов,
       созданных другими модулями, в рабочем процессе мог произойти
       segmentation fault.
       Спасибо Ciel Zhao.

    *) Изменение: теперь, если при преобразовании в адреса имени хоста,
       указанного в директиве listen, возвращается несколько адресов, nginx
       игнорирует дубликаты среди этих адресов.

    *) Исправление: nginx мог нагружать процессор при небуферизированном
       проксировании, если использовались SSL-соединения с бэкендами.


Изменения в nginx 1.23.2                                          19.10.2022

    *) Безопасность: обработка специально созданного mp4-файла модулем
       ngx_http_mp4_module могла приводить к падению рабочего процесса,
       отправке клиенту части содержимого памяти рабочего процесса, а также
       потенциально могла иметь другие последствия (CVE-2022-41741,
       CVE-2022-41742).

    *) Добавление: переменные "$proxy_protocol_tlv_...".

    *) Добавление: ключи шифрования TLS session tickets теперь автоматически
       меняются при использовании разделяемой памяти в ssl_session_cache.

    *) Изменение: уровень логгирования ошибок SSL "bad record type" понижен
       с уровня crit до info.
       Спасибо Murilo Andrade.

    *) Изменение: теперь при использовании разделяемой памяти в
       ssl_session_cache сообщения "could not allocate new session"
       логгируются на уровне warn вместо alert и не чаще одного раза в
       секунду.

    *) Исправление: nginx/Windows не собирался с OpenSSL 3.0.x.

    *) Исправление: в логгировании ошибок протокола PROXY.
       Спасибо Сергею Брестеру.

    *) Изменение: при использовании TLSv1.3 с OpenSSL разделяемая память из
       ssl_session_cache расходовалась в том числе на сессии, использующие
       TLS session tickets.

    *) Изменение: таймаут, заданный с помощью директивы ssl_session_timeout,
       не работал при использовании TLSv1.3 с OpenSSL или BoringSSL.


Изменения в nginx 1.23.1                                          19.07.2022

    *) Добавление: оптимизация использования памяти в конфигурациях с
       SSL-проксированием.

    *) Добавление: теперь с помощью параметра "ipv4=off" директивы
       "resolver" можно запретить поиск IPv4-адресов при преобразовании имён
       в адреса.

    *) Изменение: уровень логгирования ошибок SSL "bad key share", "bad
       extension", "bad cipher" и "bad ecpoint" понижен с уровня crit до
       info.

    *) Исправление: при возврате диапазонов nginx не удалял строку заголовка
       "Content-Range", если она присутствовала в исходном ответе бэкенда.

    *) Исправление: проксированный ответ мог быть отправлен не полностью при
       переконфигурации на Linux; ошибка появилась в 1.17.5.


Изменения в nginx 1.23.0                                          21.06.2022

    *) Изменение во внутреннем API: теперь строки заголовков представлены
       связными списками.

    *) Изменение: теперь nginx объединяет произвольные строки заголовков с
       одинаковыми именами при отправке на FastCGI-, SCGI- и uwsgi-бэкенды,
       в методе $r->header_in() модуля ngx_http_perl_module, и при доступе
       через переменные "$http_...", "$sent_http_...", "$sent_trailer_...",
       "$upstream_http_..." и "$upstream_trailer_...".

    *) Исправление: если в заголовке ответа бэкенда было несколько строк
       "Vary", при кэшировании nginx учитывал только последнюю из них.

    *) Исправление: если в заголовке ответа бэкенда было несколько строк
       "WWW-Authenticate" и использовался перехват ошибок с кодом 401 от
       бэкенда или директива auth_request, nginx пересылал клиенту только
       первую из этих строк.

    *) Изменение: уровень логгирования ошибок SSL "application data after
       close notify" понижен с уровня crit до info.

    *) Исправление: соединения могли зависать, если nginx был собран на
       Linux 2.6.17 и новее, а использовался на системах без поддержки
       EPOLLRDHUP, в частности, на системах с эмуляцией epoll; ошибка
       появилась в 1.17.5.
       Спасибо Marcus Ball.

    *) Исправление: nginx не кэшировал ответ, если строка заголовка ответа
       "Expires" запрещала кэширование, а последующая строка заголовка
       "Cache-Control" разрешала кэширование.


Изменения в nginx 1.21.6                                          25.01.2022

    *) Исправление: при использование EPOLLEXCLUSIVE на Linux распределение
       клиентских соединений между рабочими процессами было неравномерным.

    *) Исправление: во время плавного завершения старых рабочих процессов
       nginx возвращал в ответах строку заголовка "Connection: keep-alive".

    *) Исправление: в директиве ssl_session_ticket_key при использовании
       TLSv1.3.


Изменения в nginx 1.21.5                                          28.12.2021

    *) Изменение: теперь nginx по умолчанию собирается с библиотекой PCRE2.

    *) Изменение: теперь nginx всегда использует sendfile(SF_NODISKIO) на
       FreeBSD.

    *) Добавление: поддержка sendfile(SF_NOCACHE) на FreeBSD.

    *) Добавление: переменная $ssl_curve.

    *) Исправление: при использовании HTTP/2 без SSL вместе с директивами
       sendfile и aio соединения могли зависать.


Изменения в nginx 1.21.4                                          02.11.2021

    *) Изменение: поддержка NPN вместо ALPN для установления
       HTTP/2-соединений упразднена.

    *) Изменение: теперь nginx закрывает SSL соединение, если клиент
       использует ALPN, но nginx не поддерживает ни один из присланных
       клиентом протоколов.

    *) Изменение: в директиве sendfile_max_chunk значение по умолчанию
       изменено на 2 мегабайта.

    *) Добавление: директива proxy_half_close в модуле stream.

    *) Добавление: директива ssl_alpn в модуле stream.

    *) Добавление: переменная $ssl_alpn_protocol.

    *) Добавление: поддержка SSL_sendfile() при использовании OpenSSL 3.0.

    *) Добавление: директива mp4_start_key_frame в модуле
       ngx_http_mp4_module.
       Спасибо Tracey Jaquith.

    *) Исправление: в переменной $content_length при использовании chunked
       transfer encoding.

    *) Исправление: при получении ответа некорректной длины от проксируемого
       бэкенда nginx мог тем не менее закэшировать соединение.
       Спасибо Awdhesh Mathpal.

    *) Исправление: некорректные заголовки от бэкендов логгировались на
       уровне info вместо error; ошибка появилась в 1.21.1.

    *) Исправление: при использовании HTTP/2 и директивы aio_write запросы
       могли зависать.


Изменения в nginx 1.21.3                                          07.09.2021

    *) Изменение: оптимизация чтения тела запроса при использовании HTTP/2.

    *) Исправление: во внутреннем API для обработки тела запроса при
       использовании HTTP/2 и буферизации обрабатываемых данных.


Изменения в nginx 1.21.2                                          31.08.2021

    *) Изменение: теперь nginx возвращает ошибку, если в запросе по
       протоколу HTTP/1.0 присутствует строка заголовка "Transfer-Encoding".

    *) Изменение: экспортные шифры больше не поддерживаются.

    *) Добавление: совместимость с OpenSSL 3.0.

    *) Добавление: теперь серверу аутентификации почтового прокси-сервера
       передаются строки заголовка "Auth-SSL-Protocol" и "Auth-SSL-Cipher".
       Спасибо Rob Mueller.

    *) Добавление: API для обработки тела запроса теперь позволяет
       буферизировать обрабатываемые данные.

    *) Исправление: SSL-соединения к бэкендам в модуле stream могли зависать
       после SSL handshake.

    *) Исправление: уровень безопасности, доступный в OpenSSL 1.1.0 и новее,
       не учитывался при загрузке сертификатов сервера, если был задан через
       "@SECLEVEL=N" в директиве ssl_ciphers.

    *) Исправление: SSL-соединения с gRPC-бэкендами могли зависать, если
       использовались методы select, poll или /dev/poll.

    *) Исправление: при использовании HTTP/2 тело запроса всегда
       записывалось на диск, если в запросе не было строки заголовка
       "Content-Length".


Изменения в nginx 1.21.1                                          06.07.2021

    *) Изменение: теперь nginx для метода CONNECT всегда возвращает ошибку.

    *) Изменение: теперь nginx всегда возвращает ошибку, если в запросе
       одновременно присутствуют строки заголовка "Content-Length" и
       "Transfer-Encoding".

    *) Изменение: теперь nginx всегда возвращает ошибку, если в строке
       запроса используются пробелы или управляющие символы.

    *) Изменение: теперь nginx всегда возвращает ошибку, если в имени
       заголовка используются пробелы или управляющие символы.

    *) Изменение: теперь nginx всегда возвращает ошибку, если в строке
       "Host" заголовка запроса используются пробелы или управляющие
       символы.

    *) Изменение: оптимизация тестирования конфигурации при использовании
       большого количества listen-сокетов.

    *) Исправление: nginx не экранировал символы """, "<", ">", "\", "^",
       "`", "{", "|", и "}" при проксировании с изменением URI запроса.

    *) Исправление: SSL-переменные могли быть пустыми при записи в лог;
       ошибка появилась в 1.19.5.

    *) Исправление: keepalive-соединения с gRPC-бэкендами могли не
       закрываться после получения GOAWAY-фрейма.

    *) Исправление: уменьшено потребление памяти для долгоживущих запросов
       при проксировании с использованием более 64 буферов.


Изменения в nginx 1.21.0                                          25.05.2021

    *) Безопасность: при использовании директивы resolver во время обработки
       ответа DNS-сервера могла происходить перезапись одного байта памяти,
       что позволяло атакующему, имеющему возможность подделывать UDP-пакеты
       от DNS-сервера, вызвать падение рабочего процесса или, потенциально,
       выполнение произвольного кода (CVE-2021-23017).

    *) Добавление: директивы proxy_ssl_certificate,
       proxy_ssl_certificate_key, grpc_ssl_certificate,
       grpc_ssl_certificate_key, uwsgi_ssl_certificate и
       uwsgi_ssl_certificate_key поддерживают переменные.

    *) Добавление: директива max_errors в почтовом прокси-сервере.

    *) Добавление: почтовый прокси-сервер поддерживает POP3 и IMAP
       pipelining.

    *) Добавление: параметр fastopen директивы listen в модуле stream.
       Спасибо Anbang Wen.

    *) Исправление: специальные символы не экранировались при автоматическом
       перенаправлении с добавлением завершающего слэша.

    *) Исправление: при использовании SMTP pipelining соединения с клиентами
       в почтовом прокси-сервере могли неожиданно закрываться.


Изменения в nginx 1.19.10                                         13.04.2021

    *) Изменение: в директиве keepalive_requests значение по умолчанию
       изменено на 1000.

    *) Добавление: директива keepalive_time.

    *) Добавление: переменная $connection_time.

    *) Изменение: при использовании zlib-ng в логах появлялись сообщения
       "gzip filter failed to use preallocated memory".


Изменения в nginx 1.19.9                                          30.03.2021

    *) Исправление: nginx не собирался с почтовым прокси-сервером, но без
       модуля ngx_mail_ssl_module; ошибка появилась в 1.19.8.

    *) Исправление: при работе с gRPC-бэкендами могли возникать ошибки
       "upstream sent response body larger than indicated content length";
       ошибка появилась в 1.19.1.

    *) Исправление: если клиент закрывал соединение в момент отбрасывания
       тела запроса, nginx мог не закрыть соединение до истечения
       keepalive-таймаута.

    *) Исправление: при ожидании задержки limit_req или auth_delay, а также
       при работе с бэкендами nginx мог не обнаружить, что соединение уже
       закрыто клиентом.

    *) Исправление: в методе обработки соединений eventport.


Изменения в nginx 1.19.8                                          09.03.2021

    *) Добавление: в директиве proxy_cookie_flags теперь флаги можно
       задавать с помощью переменных.

    *) Добавление: параметр proxy_protocol в директиве listen, директивы
       proxy_protocol и set_real_ip_from в почтовом прокси-сервере.

    *) Исправление: HTTP/2-соединения сразу закрывались при использовании
       "keepalive_timeout 0"; ошибка появилась в 1.19.7.

    *) Исправление: некоторые ошибки логгировались как неизвестные, если
       nginx был собран с glibc 2.32.

    *) Исправление: в методе обработки соединений eventport.


Изменения в nginx 1.19.7                                          16.02.2021

    *) Изменение: обработка соединений в HTTP/2 была изменена и теперь более
       соответствует HTTP/1.x; директивы http2_recv_timeout,
       http2_idle_timeout и http2_max_requests упразднены, вместо них
       следует использовать директивы keepalive_timeout и
       keepalive_requests.

    *) Изменение: директивы http2_max_field_size и http2_max_header_size
       упразднены, вместо них следует использовать директиву
       large_client_header_buffers.

    *) Добавление: теперь при исчерпании свободных соединений nginx
       закрывает не только keepalive-соединения, но и соединения в lingering
       close.

    *) Исправление: в логах могли появляться сообщения "zero size buf in
       output", если бэкенд возвращал некорректный ответ при
       небуферизированном проксировании; ошибка появилась в 1.19.1.

    *) Исправление: при использовании директивы return вместе с image_filter
       или xslt_stylesheet HEAD-запросы обрабатывались некорректно.

    *) Исправление: в директиве add_trailer.


Изменения в nginx 1.19.6                                          15.12.2020

    *) Исправление: ошибки "no live upstreams", если server в блоке upstream
       был помечен как down.

    *) Исправление: при использовании HTTPS в рабочем процессе мог произойти
       segmentation fault; ошибка появилась в 1.19.5.

    *) Исправление: nginx возвращал ошибку 400 на запросы вида
       "GET http://example.com?args HTTP/1.0".

    *) Исправление: в модулях ngx_http_flv_module и ngx_http_mp4_module.
       Спасибо Chris Newton.


Изменения в nginx 1.19.5                                          24.11.2020

    *) Добавление: ключ -e.

    *) Добавление: при сборке дополнительных модулей теперь можно указывать
       одни и те же исходные файлы в разных модулях.

    *) Исправление: SSL shutdown не работал при закрытии соединений с
       ожиданием дополнительных данных (lingering close).

    *) Исправление: при работе с gRPC-бэкендами могли возникать ошибки
       "upstream sent frame for closed stream".

    *) Исправление: во внутреннем API для обработки тела запроса.


Изменения в nginx 1.19.4                                          27.10.2020

    *) Добавление: директивы ssl_conf_command, proxy_ssl_conf_command,
       grpc_ssl_conf_command и uwsgi_ssl_conf_command.

    *) Добавление: директива ssl_reject_handshake.

    *) Добавление: директива proxy_smtp_auth в почтовом прокси-сервере.


Изменения в nginx 1.19.3                                          29.09.2020

    *) Добавление: модуль ngx_stream_set_module.

    *) Добавление: директива proxy_cookie_flags.

    *) Добавление: директива userid_flags.

    *) Исправление: расширение управления кэшированием stale-if-error
       ошибочно применялось, если бэкенд возвращал ответ с кодом 500, 502,
       503, 504, 403, 404 или 429.

    *) Исправление: если использовалось кэширование и бэкенд возвращал
       ответы с строкой заголовка Vary, в логах могли появляться сообщения
       "[crit] cache file ... has too long header".

    *) Изменение: при использовании OpenSSL 1.1.1 в логах могли появляться
       сообщения "[crit] SSL_write() failed".

    *) Исправление: в логах могли появляться сообщения "SSL_shutdown()
       failed (SSL: ... bad write retry)"; ошибка появилась в 1.19.2.

    *) Исправление: при использовании HTTP/2 в рабочем процессе мог
       произойти segmentation fault, если ошибки с кодом 400 с помощью
       директивы error_page перенаправлялись в проксируемый location.

    *) Исправление: утечки сокетов при использовании HTTP/2 и подзапросов в
       модуле njs.


Изменения в nginx 1.19.2                                          11.08.2020

    *) Изменение: теперь nginx начинает закрывать keepalive-соединения, не
       дожидаясь исчерпания всех свободных соединений, а также пишет об этом
       предупреждение в лог ошибок.

    *) Изменение: оптимизация чтения тела запроса при использовании chunked
       transfer encoding.

    *) Исправление: утечки памяти при использовании директивы ssl_ocsp.

    *) Исправление: в логах могли появляться сообщения "zero size buf in
       output", если FastCGI-сервер возвращал некорректный ответ; ошибка
       появилась в 1.19.1.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если размеры large_client_header_buffers отличались в разных
       виртуальных серверах.

    *) Исправление: SSL shutdown мог не работать.

    *) Исправление: в логах могли появляться сообщения "SSL_shutdown()
       failed (SSL: ... bad write retry)".

    *) Исправление: в модуле ngx_http_slice_module.

    *) Исправление: в модуле ngx_http_xslt_filter_module.


Изменения в nginx 1.19.1                                          07.07.2020

    *) Изменение: директивы lingering_close, lingering_time и
       lingering_timeout теперь работают при использовании HTTP/2.

    *) Изменение: теперь лишние данные, присланные бэкендом, всегда
       отбрасываются.

    *) Изменение: теперь при получении слишком короткого ответа от
       FastCGI-сервера nginx пытается отправить клиенту доступную часть
       ответа, после чего закрывает соединение с клиентом.

    *) Изменение: теперь при получении ответа некорректной длины от
       gRPC-бэкенда nginx прекращает обработку ответа с ошибкой.

    *) Добавление: параметр min_free в директивах proxy_cache_path,
       fastcgi_cache_path, scgi_cache_path и uwsgi_cache_path.
       Спасибо Adam Bambuch.

    *) Исправление: nginx не удалял unix domain listen-сокеты при плавном
       завершении по сигналу SIGQUIT.

    *) Исправление: UDP-пакеты нулевого размера не проксировались.

    *) Исправление: проксирование на uwsgi-бэкенды с использованием SSL
       могло не работать.
       Спасибо Guanzhong Chen.

    *) Исправление: в обработке ошибок при использовании директивы ssl_ocsp.

    *) Исправление: при использовании файловых систем XFS и NFS размер кэша
       на диске мог считаться некорректно.

    *) Исправление: если сервер memcached возвращал некорректный ответ, в
       логах могли появляться сообщения "negative size buf in writer".


Изменения в nginx 1.19.0                                          26.05.2020

    *) Добавление: проверка клиентских сертификатов с помощью OCSP.

    *) Исправление: при работе с gRPC-бэкендами могли возникать ошибки
       "upstream sent frame for closed stream".

    *) Исправление: OCSP stapling мог не работать, если не была указана
       директива resolver.

    *) Исправление: соединения с некорректным HTTP/2 preface не
       логгировались.


Изменения в nginx 1.17.10                                         14.04.2020

    *) Добавление: директива auth_delay.


Изменения в nginx 1.17.9                                          03.03.2020

    *) Изменение: теперь nginx не разрешает несколько строк "Host" в
       заголовке запроса.

    *) Исправление: nginx игнорировал дополнительные строки
       "Transfer-Encoding" в заголовке запроса.

    *) Исправление: утечки сокетов при использовании HTTP/2.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался OCSP stapling.

    *) Исправление: в модуле ngx_http_mp4_module.

    *) Исправление: при перенаправлении ошибок с кодом 494 с помощью
       директивы error_page nginx возвращал ответ с кодом 494 вместо 400.

    *) Исправление: утечки сокетов при использовании подзапросов в модуле
       njs и директивы aio.


Изменения в nginx 1.17.8                                          21.01.2020

    *) Добавление: директива grpc_pass поддерживает переменные.

    *) Исправление: при обработке pipelined-запросов по SSL-соединению мог
       произойти таймаут; ошибка появилась в 1.17.5.

    *) Исправление: в директиве debug_points при использовании HTTP/2.
       Спасибо Даниилу Бондареву.


Изменения в nginx 1.17.7                                          24.12.2019

    *) Исправление: на старте или во время переконфигурации мог произойти
       segmentation fault, если в конфигурации использовалась директива
       rewrite с пустой строкой замены.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если директива break использовалась совместно с директивой alias или
       директивой proxy_pass с URI.

    *) Исправление: строка Location заголовка ответа могла содержать мусор,
       если URI запроса был изменён на URI, содержащий нулевой символ.

    *) Исправление: при возврате перенаправлений с помощью директивы
       error_page запросы с телом обрабатывались некорректно; ошибка
       появилась в 0.7.12.

    *) Исправление: утечки сокетов при использовании HTTP/2.

    *) Исправление: при обработке pipelined-запросов по SSL-соединению мог
       произойти таймаут; ошибка появилась в 1.17.5.

    *) Исправление: в модуле ngx_http_dav_module.


Изменения в nginx 1.17.6                                          19.11.2019

    *) Добавление: переменные $proxy_protocol_server_addr и
       $proxy_protocol_server_port.

    *) Добавление: директива limit_conn_dry_run.

    *) Добавление: переменные $limit_req_status и $limit_conn_status.


Изменения в nginx 1.17.5                                          22.10.2019

    *) Добавление: теперь nginx использует вызов ioctl(FIONREAD), если он
       доступен, чтобы избежать чтения из быстрого соединения в течение
       долгого времени.

    *) Исправление: неполные закодированные символы в конце URI запроса
       игнорировались.

    *) Исправление: "/." и "/.." в конце URI запроса не нормализовывались.

    *) Исправление: в директиве merge_slashes.

    *) Исправление: в директиве ignore_invalid_headers.
       Спасибо Alan Kemp.

    *) Исправление: nginx не собирался с MinGW-w64 gcc 8.1 и новее.


Изменения в nginx 1.17.4                                          24.09.2019

    *) Изменение: улучшено детектирование некорректного поведения клиентов в
       HTTP/2.

    *) Изменение: в обработке непрочитанного тела запроса при возврате
       ошибок в HTTP/2.

    *) Исправление: директива worker_shutdown_timeout могла не работать при
       использовании HTTP/2.

    *) Исправление: при использовании HTTP/2 и директивы
       proxy_request_buffering в рабочем процессе мог произойти segmentation
       fault.

    *) Исправление: на Windows при использовании SSL уровень записи в лог
       ошибки ECONNABORTED был "crit" вместо "error".

    *) Исправление: nginx игнорировал лишние данные при использовании
       chunked transfer encoding.

    *) Исправление: если использовалась директива return и при чтении тела
       запроса возникала ошибка, nginx всегда возвращал ошибку 500.

    *) Исправление: в обработке ошибок выделения памяти.


Изменения в nginx 1.17.3                                          13.08.2019

    *) Безопасность: при использовании HTTP/2 клиент мог вызвать чрезмерное
       потребление памяти и ресурсов процессора (CVE-2019-9511,
       CVE-2019-9513, CVE-2019-9516).

    *) Исправление: при использовании сжатия в логах могли появляться
       сообщения "zero size buf"; ошибка появилась в 1.17.2.

    *) Исправление: при использовании директивы resolver в SMTP
       прокси-сервере в рабочем процессе мог произойти segmentation fault.


Изменения в nginx 1.17.2                                          23.07.2019

    *) Изменение: минимальная поддерживаемая версия zlib - 1.2.0.4.
       Спасибо Илье Леошкевичу.

    *) Изменение: метод $r->internal_redirect() встроенного перла теперь
       ожидает закодированный URI.

    *) Добавление: теперь с помощью метода $r->internal_redirect()
       встроенного перла можно перейти в именованный location.

    *) Исправление: в обработке ошибок во встроенном перле.

    *) Исправление: на старте или во время переконфигурации мог произойти
       segmentation fault, если в конфигурации использовалось значение hash
       bucket size больше 64 килобайт.

    *) Исправление: при использовании методов обработки соединений select,
       poll и /dev/poll nginx мог нагружать процессор во время
       небуферизованного проксирования и при проксировании
       WebSocket-соединений.

    *) Исправление: в модуле ngx_http_xslt_filter_module.

    *) Исправление: в модуле ngx_http_ssi_filter_module.


Изменения в nginx 1.17.1                                          25.06.2019

    *) Добавление: директива limit_req_dry_run.

    *) Добавление: при использовании директивы hash в блоке upstream пустой
       ключ хэширования теперь приводит к переключению на round-robin
       балансировку.
       Спасибо Niklas Keller.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалось кэширование и директива image_filter, а ошибки с
       кодом 415 перенаправлялись с помощью директивы error_page; ошибка
       появилась в 1.11.10.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался встроенный перл; ошибка появилась в 1.7.3.


Изменения в nginx 1.17.0                                          21.05.2019

    *) Добавление: директивы limit_rate и limit_rate_after поддерживают
       переменные.

    *) Добавление: директивы proxy_upload_rate и proxy_download_rate в
       модуле stream поддерживают переменные.

    *) Изменение: минимальная поддерживаемая версия OpenSSL - 0.9.8.

    *) Изменение: теперь postpone-фильтр собирается всегда.

    *) Исправление: директива include не работала в блоках if и
       limit_except.

    *) Исправление: в обработке byte ranges.


Изменения в nginx 1.15.12                                         16.04.2019

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если в директивах ssl_certificate или ssl_certificate_key
       использовались переменные и был включён OCSP stapling.


Изменения в nginx 1.15.11                                         09.04.2019

    *) Исправление: в директиве ssl_stapling_file на Windows.


Изменения в nginx 1.15.10                                         26.03.2019

    *) Изменение: теперь при использовании имени хоста в директиве listen
       nginx создаёт listen-сокеты для всех адресов, соответствующих этому
       имени (ранее использовался только первый адрес).

    *) Добавление: диапазоны портов в директиве listen.

    *) Добавление: возможность загрузки SSL-сертификатов и секретных ключей
       из переменных.

    *) Изменение: переменная $ssl_server_name могла быть пустой при
       использовании OpenSSL 1.1.1.

    *) Исправление: nginx/Windows не собирался с Visual Studio 2015 и новее;
       ошибка появилась в 1.15.9.


Изменения в nginx 1.15.9                                          26.02.2019

    *) Добавление: директивы ssl_certificate и ssl_certificate_key
       поддерживают переменные.

    *) Добавление: метод poll теперь доступен на Windows при использовании
       Windows Vista и новее.

    *) Исправление: если при использовании метода select на Windows
       происходила ошибка при установлении соединения с бэкендом, nginx
       ожидал истечения таймаута на установление соединения.

    *) Исправление: директивы proxy_upload_rate и proxy_download_rate в
       модуле stream работали некорректно при проксировании UDP-пакетов.


Изменения в nginx 1.15.8                                          25.12.2018

    *) Добавление: переменная $upstream_bytes_sent.
       Спасибо Piotr Sikora.

    *) Добавление: новые директивы в скриптах подсветки синтаксиса для vim.
       Спасибо Геннадию Махомеду.

    *) Исправление: в директиве proxy_cache_background_update.

    *) Исправление: в директиве geo при использовании unix domain
       listen-сокетов.

    *) Изменение: при использовании директивы ssl_early_data с OpenSSL в
       логах могли появляться сообщения "ignoring stale global SSL error ...
       bad length".

    *) Исправление: в nginx/Windows.

    *) Исправление: в модуле ngx_http_autoindex_module на 32-битных
       платформах.


Изменения в nginx 1.15.7                                          27.11.2018

    *) Добавление: директива proxy_requests в модуле stream.

    *) Добавление: параметр "delay" директивы "limit_req".
       Спасибо Владиславу Шабанову и Петру Щучкину.

    *) Исправление: утечки памяти в случае ошибок при переконфигурации.

    *) Исправление: в переменных $upstream_response_time,
       $upstream_connect_time и $upstream_header_time.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_mp4_module на 32-битных
       платформах.


Изменения в nginx 1.15.6                                          06.11.2018

    *) Безопасность: при использовании HTTP/2 клиент мог вызвать чрезмерное
       потреблению памяти (CVE-2018-16843) и ресурсов процессора
       (CVE-2018-16844).

    *) Безопасность: при обработке специально созданного mp4-файла модулем
       ngx_http_mp4_module содержимое памяти рабочего процесса могло быть
       отправлено клиенту (CVE-2018-16845).

    *) Добавление: директивы proxy_socket_keepalive,
       fastcgi_socket_keepalive, grpc_socket_keepalive,
       memcached_socket_keepalive, scgi_socket_keepalive и
       uwsgi_socket_keepalive.

    *) Исправление: если nginx был собран с OpenSSL 1.1.0, а использовался с
       OpenSSL 1.1.1, протокол TLS 1.3 всегда был разрешён.

    *) Исправление: при работе с gRPC-бэкендами могло расходоваться большое
       количество памяти.


Изменения в nginx 1.15.5                                          02.10.2018

    *) Исправление: при использовании OpenSSL 1.1.0h и новее в рабочем
       процессе мог произойти segmentation fault; ошибка появилась в 1.15.4.

    *) Исправление: незначительных потенциальных ошибок.


Изменения в nginx 1.15.4                                          25.09.2018

    *) Добавление: теперь директиву ssl_early_data можно использовать с
       OpenSSL.

    *) Исправление: в модуле ngx_http_uwsgi_module.
       Спасибо Chris Caputo.

    *) Исправление: соединения к некоторым gRPC-бэкендам могли не
       кэшироваться при использовании директивы keepalive.

    *) Исправление: при использовании директивы error_page для
       перенаправления ошибок, возникающих на ранних этапах обработки
       запроса, в частности ошибок с кодом 400, могла происходить утечка
       сокетов.

    *) Исправление: директива return при возврате ошибок не изменяла код
       ответа, если запрос был перенаправлен с помощью директивы error_page.

    *) Исправление: стандартные сообщения об ошибках и ответы модуля
       ngx_http_autoindex_module содержали атрибут bgcolor, что могло
       приводить к их некорректному отображению при использовании
       пользовательских настроек цветов в браузерах.
       Спасибо Nova DasSarma.

    *) Изменение: уровень логгирования ошибок SSL "no suitable key share" и
       "no suitable signature algorithm" понижен с уровня crit до info.


Изменения в nginx 1.15.3                                          28.08.2018

    *) Добавление: теперь TLSv1.3 можно использовать с BoringSSL.

    *) Добавление: директива ssl_early_data, сейчас доступна при
       использовании BoringSSL.

    *) Добавление: директивы keepalive_timeout и keepalive_requests в блоке
       upstream.

    *) Исправление: модуль ngx_http_dav_module при копировании файла поверх
       существующего файла с помощью метода COPY не обнулял целевой файл.

    *) Исправление: модуль ngx_http_dav_module при перемещении файла между
       файловыми системами с помощью метода MOVE устанавливал нулевые права
       доступа на результирующий файл и не сохранял время изменения файла.

    *) Исправление: модуль ngx_http_dav_module при копировании файла с
       помощью метода COPY для результирующего файла использовал права
       доступа по умолчанию.

    *) Изменение: некоторые клиенты могли не работать при использовании
       HTTP/2; ошибка появилась в 1.13.5.

    *) Исправление: nginx не собирался с LibreSSL 2.8.0.


Изменения в nginx 1.15.2                                          24.07.2018

    *) Добавление: переменная $ssl_preread_protocol в модуле
       ngx_stream_ssl_preread_module.

    *) Добавление: теперь при использовании директивы
       reset_timedout_connection nginx сбрасывает соединения, закрываемые с
       кодом 444.

    *) Изменение: уровень логгирования ошибок SSL "http request", "https
       proxy request", "unsupported protocol" и "version too low" понижен с
       уровня crit до info.

    *) Исправление: запросы к DNS-серверу не отправлялись повторно, если при
       первой попытке отправки происходила ошибка.

    *) Исправление: параметр reuseport директивы listen игнорировался, если
       количество рабочих процессов было задано после директивы listen.

    *) Исправление: при использовании OpenSSL 1.1.0 и новее директиву
       ssl_prefer_server_ciphers нельзя было выключить в виртуальном
       сервере, если она была включена в сервере по умолчанию.

    *) Исправление: повторное использование SSL-сессий к бэкендам не
       работало с протоколом TLS 1.3.


Изменения в nginx 1.15.1                                          03.07.2018

    *) Добавление: директива random в блоке upstream.

    *) Добавление: улучшена производительность при использовании директив
       hash и ip_hash совместно с директивой zone.

    *) Добавление: параметр reuseport директивы listen теперь использует
       SO_REUSEPORT_LB на FreeBSD 12.

    *) Исправление: HTTP/2 server push не работал, если SSL терминировался
       прокси-сервером перед nginx'ом.

    *) Исправление: директива tcp_nopush всегда использовалась для
       соединений к бэкендам.

    *) Исправление: при отправке сохранённого на диск тела запроса на
       gRPC-бэкенд могли возникать ошибки.


Изменения в nginx 1.15.0                                          05.06.2018

    *) Изменение: директива "ssl" теперь считается устаревшей; вместо неё
       следует использовать параметр ssl директивы listen.

    *) Изменение: теперь при использовании директивы listen с параметром ssl
       nginx определяет отсутствие SSL-сертификатов при тестировании
       конфигурации.

    *) Добавление: теперь модуль stream умеет обрабатывать несколько
       входящих UDP-пакетов от клиента в рамках одной сессии.

    *) Исправление: в директиве proxy_cache_valid можно было указать
       некорректный код ответа.

    *) Исправление: nginx не собирался gcc 8.1.

    *) Исправление: логгирование в syslog останавливалось при изменении
       локального IP-адреса.

    *) Исправление: nginx не собирался компилятором clang, если был
       установлен CUDA SDK; ошибка появилась в 1.13.8.

    *) Исправление: при использовании unix domain listen-сокетов на FreeBSD
       в процессе обновления исполняемого файла в логе могли появляться
       сообщения "getsockopt(TCP_FASTOPEN) ... failed".

    *) Исправление: nginx не собирался на Fedora 28 Linux.

    *) Исправление: при использовании директивы limit_req заданная скорость
       обработки запросов могла не соблюдаться.

    *) Исправление: в обработке адресов клиентов при использовании unix
       domain listen-сокетов для работы с датаграммами на Linux.

    *) Исправление: в обработке ошибок выделения памяти.


Изменения в nginx 1.13.12                                         10.04.2018

    *) Исправление: при возврате большого ответа соединения с gRPC-бэкендами
       могли неожиданно закрываться.


Изменения в nginx 1.13.11                                         03.04.2018

    *) Добавление: параметр proxy_protocol директивы listen теперь
       поддерживает протокол PROXY версии 2.

    *) Исправление: nginx не собирался с OpenSSL 1.1.1 статически на Linux.

    *) Исправление: в параметрах http_404, http_500 и им подобных директивы
       proxy_next_upstream.


Изменения в nginx 1.13.10                                         20.03.2018

    *) Добавление: теперь параметр set в SSI-директиве include позволяет
       сохранять в переменную любые ответы; максимальный размер ответа
       задаётся директивой subrequest_output_buffer_size.

    *) Добавление: теперь nginx использует вызов
       clock_gettime(CLOCK_MONOTONIC), если он доступен, что позволяет
       избежать некорректного срабатывания таймаутов при изменениях
       системного времени.

    *) Добавление: параметр "escape=none" директивы log_format.
       Спасибо Johannes Baiter и Calin Don.

    *) Добавление: переменная $ssl_preread_alpn_protocols в модуле
       ngx_stream_ssl_preread_module.

    *) Добавление: модуль ngx_http_grpc_module.

    *) Исправление: в обработке ошибок выделения памяти в директиве geo.

    *) Исправление: при использовании переменных в директиве
       auth_basic_user_file в лог мог выводиться символ '\0'.
       Спасибо Вадиму Филимонову.


Изменения в nginx 1.13.9                                          20.02.2018

    *) Добавление: поддержка HTTP/2 server push; директивы http2_push и
       http2_push_preload.

    *) Исправление: при использовании кэша в логах могли появляться
       сообщения "header already sent"; ошибка появилась в 1.9.13.

    *) Исправление: при использовании директивы ssl_verify_client в рабочем
       процессе мог произойти segmentation fault, если в виртуальном сервере
       не был указан SSL-сертификат.

    *) Исправление: в модуле ngx_http_v2_module.

    *) Исправление: в модуле ngx_http_dav_module.


Изменения в nginx 1.13.8                                          26.12.2017

    *) Добавление: теперь при использовании параметра transparent директив
       proxy_bind, fastcgi_bind, memcached_bind, scgi_bind и uwsgi_bind
       nginx автоматически сохраняет capability CAP_NET_RAW в рабочих
       процессах.

    *) Добавление: улучшения в определении размера строки кэша процессора.
       Спасибо Debayan Ghosh.

    *) Добавление: новые директивы в скриптах подсветки синтаксиса для vim.
       Спасибо Геннадию Махомеду.

    *) Исправление: процедура обновления исполняемого файла не работала,
       если после завершения родительского процесса новым родительским
       процессом nginx'а становился процесс с PID, отличным от 1.

    *) Исправление: модуль ngx_http_autoindex_module неправильно обрабатывал
       запросы с телом.

    *) Исправление: в директиве proxy_limit_rate при использовании с
       директивой keepalive.

    *) Исправление: при использовании "proxy_buffering off" часть ответа
       могла буферизироваться, если клиентское соединение использовало SSL.
       Спасибо Patryk Lesiewicz.

    *) Исправление: в директиве proxy_cache_background_update.

    *) Исправление: переменную вида "${name}" с именем в фигурных скобках
       нельзя было использовать в начале параметра не заключив весь параметр
       в кавычки.


Изменения в nginx 1.13.7                                          21.11.2017

    *) Исправление: в переменной $upstream_status.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если бэкенд возвращал ответ "101 Switching Protocols" на подзапрос.

    *) Исправление: если при переконфигурации изменялся размер зоны
       разделяемой памяти и переконфигурация завершалась неудачно, то в
       главном процессе происходил segmentation fault.

    *) Исправление: в модуле ngx_http_fastcgi_module.

    *) Исправление: nginx возвращал ошибку 500, если в директиве
       xslt_stylesheet были заданы параметры без использования переменных.

    *) Изменение: при использовании варианта библиотеки zlib от Intel в лог
       писались сообщения "gzip filter failed to use preallocated memory".

    *) Исправление: директива worker_shutdown_timeout не работала при
       использовании почтового прокси-сервера и при проксировании
       WebSocket-соединений.


Изменения в nginx 1.13.6                                          10.10.2017

    *) Исправление: при использовании директивы ssl_preread в модуле stream
       не работало переключение на следующий бэкенд.

    *) Исправление: в модуле ngx_http_v2_module.
       Спасибо Piotr Sikora.

    *) Исправление: nginx не поддерживал даты после 2038 года на 32-битных
       платформах с 64-битным time_t.

    *) Исправление: в обработке дат до 1970 года и после 10000 года.

    *) Исправление: в модуле stream таймауты ожидания UDP-пакетов от
       бэкендов не логгировались или логгировались на уровне info вместо
       error.

    *) Исправление: при использовании HTTP/2 nginx мог вернуть ошибку 400,
       не указав в логе причину.

    *) Исправление: в обработке повреждённых файлов кэша.

    *) Исправление: при кэшировании ошибок, перехваченных error_page, не
       учитывались заголовки управления кэшированием.

    *) Исправление: при использовании HTTP/2 тело запроса могло быть
       повреждено.

    *) Исправление: в обработке адресов клиентов при использовании unix
       domain сокетов.

    *) Исправление: при использовании директивы "hash ... consistent" в
       блоке upstream nginx нагружал процессор, если использовались большие
       веса и все или почти все бэкенды были недоступны.


Изменения в nginx 1.13.5                                          05.09.2017

    *) Добавление: переменная $ssl_client_escaped_cert.

    *) Исправление: директива ssl_session_ticket_key и параметр include
       директивы geo не работали на Windows.

    *) Исправление: на 32-битных платформах при запросе более 4 гигабайт с
       помощью нескольких диапазонов возвращалась некорректная длина ответа.

    *) Исправление: директива "expires modified" и обработка строки If-Range
       заголовка запроса не учитывали время последнего изменения ответа,
       если использовалось проксирование без кэширования.


Изменения в nginx 1.13.4                                          08.08.2017

    *) Добавление: модуль ngx_http_mirror_module.

    *) Исправление: клиентские соединения могли сбрасываться при
       тестировании конфигурации, если использовался параметр reuseport
       директивы listen на Linux.

    *) Исправление: тело запроса могло быть недоступно в подзапросах, если
       оно было сохранено в файл и использовалось проксирование.

    *) Исправление: очистка кэша по max_size не работала на Windows.

    *) Исправление: любое выделение разделяемой памяти на Windows требовало
       4096 байт памяти.

    *) Исправление: при использовании директивы zone в блоке upstream на
       Windows рабочий процесс мог завершаться аварийно.


Изменения в nginx 1.13.3                                          11.07.2017

    *) Безопасность: специально созданный запрос мог вызвать целочисленное
       переполнение в range-фильтре и последующую некорректную обработку
       запрошенных диапазонов, что потенциально могло привести к утечке
       конфиденциальной информации (CVE-2017-7529).


Изменения в nginx 1.13.2                                          27.06.2017

    *) Изменение: теперь при запросе диапазона, начинающегося с 0, из
       пустого файла nginx возвращает ответ 200 вместо 416.

    *) Добавление: директива add_trailer.
       Спасибо Piotr Sikora.

    *) Исправление: nginx не собирался под Cygwin и NetBSD; ошибка появилась
       в 1.13.0.

    *) Исправление: nginx не собирался под MSYS2 / MinGW 64-bit.
       Спасибо Orgad Shaneh.

    *) Исправление: при использовании SSI с большим количеством подзапросов
       и proxy_pass с переменными в рабочем процессе мог произойти
       segmentation fault.

    *) Исправление: в модуле ngx_http_v2_module.
       Спасибо Piotr Sikora.


Изменения в nginx 1.13.1                                          30.05.2017

    *) Добавление: теперь в качестве параметра директивы set_real_ip_from
       можно указывать имя хоста.

    *) Добавление: улучшения в скриптах подсветки синтаксиса для vim.

    *) Добавление: директива worker_cpu_affinity теперь работает на
       DragonFly BSD.
       Спасибо Sepherosa Ziehau.

    *) Исправление: SSL renegotiation в соединениях к бэкендам не работал
       при использовании OpenSSL до 1.1.0.

    *) Изменение: nginx не собирался с Oracle Developer Studio 12.5.

    *) Изменение: теперь cache manager пропускает заблокированные записи при
       очистке кэша по max_size.

    *) Исправление: клиентские SSL-соединения сразу закрывались, если
       использовался отложенный accept и параметр proxy_protocol директивы
       listen.

    *) Исправление: в директиве proxy_cache_background_update.

    *) Изменение: теперь директива tcp_nodelay устанавливает опцию
       TCP_NODELAY перед SSL handshake.


Изменения в nginx 1.13.0                                          25.04.2017

    *) Изменение: теперь SSL renegotiation допускается в соединениях к
       бэкендам.

    *) Добавление: параметры rcvbuf и sndbuf директив listen в почтовом
       прокси-сервере и модуле stream.

    *) Добавление: директивы return и error_page теперь могут использоваться
       для возврата перенаправлений с кодом 308.
       Спасибо Simon Leblanc.

    *) Добавление: параметр TLSv1.3 в директиве ssl_protocols.

    *) Добавление: при логгировании сигналов теперь указывается PID
       отправившего сигнал процесса.

    *) Исправление: в обработке ошибок выделения памяти.

    *) Исправление: если сервер в модуле stream слушал на wildcard-адресе,
       исходящий адрес ответного UDP-пакета мог отличаться от адреса
       назначения исходного пакета.


Изменения в nginx 1.11.13                                         04.04.2017

    *) Добавление: параметр http_429 в директивах proxy_next_upstream,
       fastcgi_next_upstream, scgi_next_upstream и uwsgi_next_upstream.
       Спасибо Piotr Sikora.

    *) Исправление: в обработке ошибок выделения памяти.

    *) Исправление: при использовании директив sendfile и timer_resolution
       на Linux запросы могли зависать.

    *) Исправление: при использовании с подзапросами директив sendfile и
       aio_write запросы могли зависать.

    *) Исправление: в модуле ngx_http_v2_module.
       Спасибо Piotr Sikora.

    *) Исправление: при использовании HTTP/2 в рабочем процессе мог
       произойти segmentation fault.

    *) Исправление: запросы могли зависать при использовании с подзапросами
       директив limit_rate, sendfile_max_chunk, limit_req или метода
       $r->sleep() встроенного перла.

    *) Исправление: в модуле ngx_http_slice_module.


Изменения в nginx 1.11.12                                         24.03.2017

    *) Исправление: nginx мог нагружать процессор; ошибка появилась в
       1.11.11.


Изменения в nginx 1.11.11                                         21.03.2017

    *) Добавление: директива worker_shutdown_timeout.

    *) Добавление: улучшения в скриптах подсветки синтаксиса для vim.
       Спасибо Wei-Ko Kao.

    *) Исправление: при попытке установить переменную $limit_rate в пустую
       строку в рабочем процессе мог произойти segmentation fault.

    *) Исправление: директивы proxy_cache_background_update,
       fastcgi_cache_background_update, scgi_cache_background_update и
       uwsgi_cache_background_update могли работать некорректно, если
       использовалась директива if.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если количество large_client_header_buffers в виртуальном сервере
       отличалось от такового в сервере по умолчанию.

    *) Исправление: в почтовом прокси-сервере.


Изменения в nginx 1.11.10                                         14.02.2017

    *) Изменение: формат заголовка кэша был изменен, ранее закэшированные
       ответы будут загружены заново.

    *) Добавление: поддержка расширений stale-while-revalidate и
       stale-if-error в строке "Cache-Control" в заголовке ответа бэкенда.

    *) Добавление: директивы proxy_cache_background_update,
       fastcgi_cache_background_update, scgi_cache_background_update и
       uwsgi_cache_background_update.

    *) Добавление: теперь nginx может кэшировать ответы со строкой Vary
       заголовка длиной до 128 символов (вместо 42 символов в предыдущих
       версиях).

    *) Добавление: параметр build директивы server_tokens.
       Спасибо Tom Thorogood.

    *) Исправление: при обработке запросов со строкой "Expect: 100-continue"
       в заголовке запроса в логах могли появляться сообщения "[crit]
       SSL_write() failed".

    *) Исправление: модуль ngx_http_slice_module не работал в именованных
       location'ах.

    *) Исправление: при использовании AIO после перенаправления запроса с
       помощью X-Accel-Redirect в рабочем процессе мог произойти
       segmentation fault.

    *) Исправление: уменьшено потребление памяти для долгоживущих запросов,
       использующих сжатие.


Изменения в nginx 1.11.9                                          24.01.2017

    *) Исправление: при использовании модуля stream nginx мог нагружать
       процессор; ошибка появилась в 1.11.5.

    *) Исправление: метод аутентификации EXTERNAL в почтовом прокси-сервере
       можно было использовать, даже если он не был разрешён в конфигурации.

    *) Исправление: при использовании директивы ssl_verify_client модуля
       stream в рабочем процессе мог произойти segmentation fault.

    *) Исправление: директива ssl_verify_client модуля stream могла не
       работать.

    *) Исправление: при исчерпании рабочим процессом свободных соединений
       keepalive-соединения могли закрываться излишне агрессивно.
       Спасибо Joel Cunningham.

    *) Исправление: при использовании директивы sendfile на FreeBSD и macOS
       мог возвращаться некорректный ответ; ошибка появилась в 1.7.8.

    *) Исправление: при использовании директивы aio_write ответ мог
       сохраняться в кэш не полностью.

    *) Исправление: при использовании директивы aio_write могла происходить
       утечка сокетов.


Изменения в nginx 1.11.8                                          27.12.2016

    *) Добавление: директива absolute_redirect.

    *) Добавление: параметр escape директивы log_format.

    *) Добавление: проверка клиентских SSL-сертификатов в модуле stream.

    *) Добавление: директива ssl_session_ticket_key поддерживает шифрование
       TLS session tickets с помощью AES256 при использовании с 80-байтными
       ключами.

    *) Добавление: поддержка vim-commentary в скриптах для vim.
       Спасибо Armin Grodon.

    *) Исправление: рекурсия при получении значений переменных не
       ограничивалась.

    *) Исправление: в модуле ngx_stream_ssl_preread_module.

    *) Исправление: если сервер, описанный в блоке upstream в модуле stream,
       был признан неработающим, то после истечения fail_timeout он
       признавался работающим только после завершения тестового соединения;
       теперь достаточно, чтобы соединение было успешно установлено.

    *) Исправление: nginx/Windows не собирался с 64-битным Visual Studio.

    *) Исправление: nginx/Windows не собирался с OpenSSL 1.1.0.


Изменения в nginx 1.11.7                                          13.12.2016

    *) Изменение: переменная $ssl_client_verify теперь в случае ошибки
       проверки клиентского сертификата содержит строку с описанием ошибки,
       например, "FAILED:certificate has expired".

    *) Добавление: переменные $ssl_ciphers, $ssl_curves,
       $ssl_client_v_start, $ssl_client_v_end и $ssl_client_v_remain.

    *) Добавление: параметр volatile директивы map.

    *) Исправление: при сборке динамических модулей не учитывались заданные
       для модуля зависимости.

    *) Исправление: при использовании HTTP/2 и директив limit_req или
       auth_request тело запроса могло быть повреждено; ошибка появилась в
       1.11.0.

    *) Исправление: при использовании HTTP/2 в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 1.11.3.

    *) Исправление: в модуле ngx_http_mp4_module.
       Спасибо Congcong Hu.

    *) Исправление: в модуле ngx_http_perl_module.


Изменения в nginx 1.11.6                                          15.11.2016

    *) Изменение: формат переменных $ssl_client_s_dn и $ssl_client_i_dn
       изменён на соответствующий RFC 2253 (RFC 4514); значения в старом
       формате доступны через переменные $ssl_client_s_dn_legacy и
       $ssl_client_i_dn_legacy.

    *) Изменение: при сохранении временных файлов в каталоге кэша они теперь
       располагаются не в отдельном подкаталоге для временных файлов, а в
       том же подкаталоге, что и соответствующие файлы в кэше.

    *) Добавление: поддержка метода аутентификации EXTERNAL в почтовом
       прокси-сервере.
       Спасибо Robert Norris.

    *) Добавление: поддержка WebP в модуле ngx_http_image_filter_module.

    *) Добавление: директива proxy_method поддерживает переменные.
       Спасибо Дмитрию Лазуркину.

    *) Добавление: директива http2_max_requests в модуле ngx_http_v2_module.

    *) Добавление: директивы proxy_cache_max_range_offset,
       fastcgi_cache_max_range_offset, scgi_cache_max_range_offset и
       uwsgi_cache_max_range_offset.

    *) Исправление: плавное завершение старых рабочих процессов могло
       занимать бесконечное время при использовании HTTP/2.

    *) Исправление: в модуле ngx_http_mp4_module.

    *) Исправление: при проксировании WebSocket-соединений и включённом
       кэшировании в логах могли появляться сообщения "ignore long locked
       inactive cache entry".

    *) Исправление: если во время SSL handshake с бэкендом происходил
       таймаут, nginx ничего не писал в лог и возвращал ответ с кодом 502
       вместо 504.


Изменения в nginx 1.11.5                                          11.10.2016

    *) Изменение: параметр configure --with-ipv6 упразднён, поддержка IPv6
       теперь собирается автоматически.

    *) Изменение: теперь, если в блоке upstream не оказалось доступных
       серверов, nginx не сбрасывает статистику ошибок всех серверов, как
       делал ранее, а ожидает истечения fail_timeout.

    *) Добавление: модуль ngx_stream_ssl_preread_module.

    *) Добавление: директива server в блоке upstream поддерживает параметр
       max_conns.

    *) Добавление: параметр configure --with-compat.

    *) Добавление: параметры manager_files, manager_threshold и
       manager_sleep директив proxy_cache_path, fastcgi_cache_path,
       scgi_cache_path и uwsgi_cache_path.

    *) Исправление: при сборке perl-модуля не использовались флаги, заданные
       с помощью параметра configure --with-ld-opt.

    *) Исправление: в директиве add_after_body при использовании совместно с
       директивой sub_filter.

    *) Исправление: в переменной $realip_remote_addr.

    *) Исправление: директивы dav_access, proxy_store_access,
       fastcgi_store_access, scgi_store_access и uwsgi_store_access
       игнорировали права, заданные для пользователя.

    *) Исправление: unix domain listen-сокеты могли не наследоваться при
       обновлении исполняемого файла на Linux.

    *) Исправление: nginx возвращал ошибку 400 на запросы с символом "-" в
       HTTP-методе.


Изменения в nginx 1.11.4                                          13.09.2016

    *) Добавление: переменная $upstream_bytes_received.

    *) Добавление: переменные $bytes_received, $session_time, $protocol,
       $status, $upstream_addr, $upstream_bytes_sent,
       $upstream_bytes_received, $upstream_connect_time,
       $upstream_first_byte_time и $upstream_session_time в модуле stream.

    *) Добавление: модуль ngx_stream_log_module.

    *) Добавление: параметр proxy_protocol в директиве listen, переменные
       $proxy_protocol_addr и $proxy_protocol_port в модуле stream.

    *) Добавление: модуль ngx_stream_realip_module.

    *) Исправление: nginx не собирался с модулем stream и модулем
       ngx_http_ssl_module, но без модуля ngx_stream_ssl_module; ошибка
       появилась в 1.11.3.

    *) Добавление: опция сокета IP_BIND_ADDRESS_NO_PORT не использовалась;
       ошибка появилась в 1.11.2.

    *) Исправление: в параметре ranges директивы geo.

    *) Исправление: при использовании директив "aio threads" и sendfile мог
       возвращаться некорректный ответ; ошибка появилась в 1.9.13.


Изменения в nginx 1.11.3                                          26.07.2016

    *) Изменение: теперь accept_mutex по умолчанию выключен.

    *) Добавление: теперь nginx использует EPOLLEXCLUSIVE на Linux.

    *) Добавление: модуль ngx_stream_geo_module.

    *) Добавление: модуль ngx_stream_geoip_module.

    *) Добавление: модуль ngx_stream_split_clients_module.

    *) Добавление: директивы proxy_pass и proxy_ssl_name в модуле stream
       поддерживают переменные.

    *) Исправление: утечки сокетов при использовании HTTP/2.

    *) Исправление: в configure.
       Спасибо Piotr Sikora.


Изменения в nginx 1.11.2                                          05.07.2016

    *) Изменение: теперь nginx всегда использует внутренние реализации MD5 и
       SHA1; параметры configure --with-md5 и --with-sha1 упразднены.

    *) Добавление: поддержка переменных в модуле stream.

    *) Добавление: модуль ngx_stream_map_module.

    *) Добавление: модуль ngx_stream_return_module.

    *) Добавление: в директивах proxy_bind, fastcgi_bind, memcached_bind,
       scgi_bind и uwsgi_bind теперь можно указывать порт.

    *) Добавление: теперь nginx использует опцию сокета
       IP_BIND_ADDRESS_NO_PORT, если она доступна.

    *) Исправление: при использовании HTTP/2 и директивы
       proxy_request_buffering в рабочем процессе мог произойти segmentation
       fault.

    *) Исправление: при использовании HTTP/2 к запросам, передаваемым на
       бэкенд, всегда добавлялась строка заголовка "Content-Length", даже
       если у запроса не было тела.

    *) Исправление: при использовании HTTP/2 в логах могли появляться
       сообщения "http request count is zero".

    *) Исправление: при использовании директивы sub_filter могло
       буферизироваться больше данных, чем это необходимо; проблема
       появилась в 1.9.4.


Изменения в nginx 1.11.1                                          31.05.2016

    *) Безопасность: при записи тела специально созданного запроса во
       временный файл в рабочем процессе мог происходить segmentation fault
       (CVE-2016-4450); ошибка появилась в 1.3.9.


Изменения в nginx 1.11.0                                          24.05.2016

    *) Добавление: параметр transparent директив proxy_bind, fastcgi_bind,
       memcached_bind, scgi_bind и uwsgi_bind.

    *) Добавление: переменная $request_id.

    *) Добавление: директива map поддерживает комбинации нескольких
       переменных в качестве результирующих значений.

    *) Добавление: теперь при использовании метода epoll nginx проверяет,
       поддерживает ли ядро события EPOLLRDHUP, и соответственно
       оптимизирует обработку соединений.

    *) Добавление: директивы ssl_certificate и ssl_certificate_key теперь
       можно указывать несколько раз для загрузки сертификатов разных типов
       (например, RSA и ECDSA).

    *) Добавление: при использовании OpenSSL 1.0.2 и новее с помощью
       директивы ssl_ecdh_curve теперь можно задать список кривых; по
       умолчанию используется встроенный в OpenSSL список кривых.

    *) Изменение: для использования DHE-шифров теперь надо явно задавать
       файл параметров с помощью директивы ssl_dhparam.

    *) Добавление: переменная $proxy_protocol_port.

    *) Добавление: переменная $realip_remote_port в модуле
       ngx_http_realip_module.

    *) Добавление: модуль ngx_http_realip_module теперь позволяет
       устанавливать не только адрес, но и порт клиента.

    *) Изменение: при попытке запросить виртуальный сервер, отличающийся от
       согласованного в процессе SSL handshake, теперь возвращается ответ
       "421 Misdirected Request"; это улучшает совместимость с некоторыми
       HTTP/2-клиентами в случае использования клиентских сертификатов.

    *) Изменение: HTTP/2-клиенты теперь могут сразу присылать тело запроса;
       директива http2_body_preread_size позволяет указать размер буфера,
       который будет использоваться до того, как nginx начнёт читать тело.

    *) Исправление: при использовании директивы proxy_cache_bypass не
       обновлялись закэшированные ошибочные ответы.


Изменения в nginx 1.9.15                                          19.04.2016

    *) Исправление: при использовании HHVM в качестве FastCGI-сервера могли
       возникать ошибки "recv() failed".

    *) Исправление: при использовании HTTP/2 и директив limit_req или
       auth_request при чтении тела запроса мог произойти таймаут или ошибка
       "client violated flow control"; ошибка появилась в 1.9.14.

    *) Изменение: при использовании HTTP/2 ответ мог не показываться
       некоторыми браузерами, если тело запроса было прочитано не целиком;
       ошибка появилась в 1.9.14.

    *) Исправление: при использовании директивы "aio threads" соединения
       могли зависать.
       Спасибо Mindaugas Rasiukevicius.


Изменения в nginx 1.9.14                                          05.04.2016

    *) Добавление: совместимость с OpenSSL 1.1.0.

    *) Добавление: директивы proxy_request_buffering,
       fastcgi_request_buffering, scgi_request_buffering и
       uwsgi_request_buffering теперь работают при использовании HTTP/2.

    *) Исправление: при использовании HTTP/2 в логах могли появляться
       сообщения "zero size buf in output".

    *) Исправление: при использовании HTTP/2 директива client_max_body_size
       могла работать неверно.

    *) Исправление: незначительных ошибок логгирования.


Изменения в nginx 1.9.13                                          29.03.2016

    *) Изменение: неидемпотентные запросы (POST, LOCK, PATCH) теперь по
       умолчанию не передаются на другой сервер, если запрос уже был
       отправлен на бэкенд; параметр non_idempotent директивы
       proxy_next_upstream явно разрешает повторять такие запросы.

    *) Добавление: модуль ngx_http_perl_module теперь можно собрать
       динамически.

    *) Добавление: поддержка UDP в модуле stream.

    *) Добавление: директива aio_write.

    *) Добавление: теперь cache manager следит за количеством элементов в
       кэше и старается не допускать переполнений зоны разделяемой памяти.

    *) Исправление: при использовании директив sendfile и aio с подзапросами
       в логах могли появляться сообщения "task already active" и "second
       aio post".

    *) Исправление: при использовании кэширования в логах могли появляться
       сообщения "zero size buf in output", если клиент закрывал соединение
       преждевременно.

    *) Исправление: при использовании кэширования соединения с клиентами
       могли закрываться без необходимости.
       Спасибо Justin Li.

    *) Исправление: nginx мог нагружать процессор при использовании
       директивы sendfile на Linux и Solaris, если отправляемый файл был
       изменён в процессе отправки.

    *) Исправление: при использовании директив sendfile и "aio threads"
       соединения могли зависать.

    *) Исправление: в директивах proxy_pass, fastcgi_pass, scgi_pass и
       uwsgi_pass при использовании переменных.
       Спасибо Piotr Sikora.

    *) Исправление: в модуле ngx_http_sub_filter_module.

    *) Исправление: если в закэшированном соединении к бэкенду происходила
       ошибка, запрос передавался на другой сервер без учёта директивы
       proxy_next_upstream.

    *) Исправление: ошибки "CreateFile() failed" при создании временных
       файлов на Windows.


Изменения в nginx 1.9.12                                          24.02.2016

    *) Добавление: кодирование Хаффмана заголовков ответов в HTTP/2.
       Спасибо Владу Краснову.

    *) Добавление: директива worker_cpu_affinity теперь поддерживает более
       64 процессоров.

    *) Исправление: совместимость со сторонними модулями на C++; ошибка
       появилась в 1.9.11.
       Спасибо Piotr Sikora.

    *) Исправление: nginx не собирался статически с OpenSSL на Linux; ошибка
       появилась в 1.9.11.

    *) Исправление: директива "add_header ... always" с пустым значением не
       удаляла из заголовков ошибочных ответов строки Last-Modified и ETag.

    *) Изменение: при использовании OpenSSL 1.0.2f в логах могли появляться
       сообщения "called a function you should not call" и "shutdown while
       in init".

    *) Исправление: ошибочные заголовки могли логгироваться некорректно.

    *) Исправление: утечки сокетов при использовании HTTP/2.

    *) Исправление: в модуле ngx_http_v2_module.


Изменения в nginx 1.9.11                                          09.02.2016

    *) Добавление: теперь resolver поддерживает TCP.

    *) Добавление: динамические модули.

    *) Исправление: при использовании HTTP/2 переменная $request_length не
       учитывала размер заголовков запроса.

    *) Исправление: в модуле ngx_http_v2_module.


Изменения в nginx 1.9.10                                          26.01.2016

    *) Безопасность: при использовании директивы resolver во время обработки
       ответов DNS-сервера могло происходить разыменование некорректного
       адреса, что позволяло атакующему, имеющему возможность подделывать
       UDP-пакеты от DNS-сервера, вызвать segmentation fault в рабочем
       процессе (CVE-2016-0742).

    *) Безопасность: при использовании директивы resolver во время обработки
       CNAME-записей могло произойти обращение к ранее освобождённой памяти,
       что позволяло атакующему, имеющему возможность инициировать
       преобразование произвольных имён в адреса, вызвать segmentation fault
       в рабочем процессе, а также потенциально могло иметь другие
       последствия (CVE-2016-0746).

    *) Безопасность: при использовании директивы resolver во время обработки
       CNAME-записей не во всех случаях проверялось ограничение на
       максимальное количество записей в цепочке, что позволяло атакующему,
       имеющему возможность инициировать преобразование произвольных имён в
       адреса, вызвать чрезмерное потребление ресурсов рабочими процессами
       (CVE-2016-0747).

    *) Добавление: параметр auto директивы worker_cpu_affinity.

    *) Исправление: параметр proxy_protocol директивы listen не работал с
       IPv6 listen-сокетами.

    *) Исправление: при использовании директивы keepalive соединения к
       бэкендам могли кэшироваться некорректно.

    *) Исправление: после перенаправления запроса с помощью X-Accel-Redirect
       при проксировании использовался HTTP-метод оригинального запроса.


Изменения в nginx 1.9.9                                           09.12.2015

    *) Исправление: проксирование в unix domain сокеты не работало при
       использовании переменных; ошибка появилась в 1.9.8.


Изменения в nginx 1.9.8                                           08.12.2015

    *) Добавление: поддержка pwritev().

    *) Добавление: директива include в блоке upstream.

    *) Добавление: модуль ngx_http_slice_module.

    *) Исправление: при использовании LibreSSL в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 1.9.6.

    *) Исправление: nginx мог не собираться на OS X.


Изменения в nginx 1.9.7                                           17.11.2015

    *) Добавление: параметр nohostname логгирования в syslog.

    *) Добавление: директива proxy_cache_convert_head.

    *) Добавление: переменная $realip_remote_addr в модуле
       ngx_http_realip_module.

    *) Исправление: директива expires могла не срабатывать при использовании
       переменных.

    *) Исправление: при использовании HTTP/2 в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 1.9.6.

    *) Исправление: если nginx был собран с модулем ngx_http_v2_module,
       протокол HTTP/2 мог быть использован клиентом, даже если не был
       указан параметр http2 директивы listen.

    *) Исправление: в модуле ngx_http_v2_module.


Изменения в nginx 1.9.6                                           27.10.2015

    *) Исправление: при использовании HTTP/2 в рабочем процессе мог
       произойти segmentation fault.
       Спасибо Piotr Sikora и Denis Andzakovic.

    *) Исправление: при использовании HTTP/2 переменная $server_protocol
       была пустой.

    *) Исправление: SSL-соединения к бэкендам в модуле stream могли
       неожиданно завершаться по таймауту.

    *) Исправление: при использовании различных настроек ssl_session_cache в
       разных виртуальных серверах в рабочем процессе мог произойти
       segmentation fault.

    *) Исправление: nginx/Windows не собирался с MinGW gcc; ошибка появилась
       в 1.9.4.
       Спасибо Kouhei Sutou.

    *) Исправление: при использовании директивы timer_resolution на Windows
       время не обновлялось.

    *) Незначительные исправления и улучшения.
       Спасибо Markus Linnala, Kurtis Nusbaum и Piotr Sikora.


Изменения в nginx 1.9.5                                           22.09.2015

    *) Добавление: модуль ngx_http_v2_module (заменяет модуль
       ngx_http_spdy_module).
       Спасибо Dropbox и Automattic за спонсирование разработки.

    *) Изменение: теперь по умолчанию директива output_buffers использует
       два буфера.

    *) Изменение: теперь nginx ограничивает максимальную вложенность
       подзапросов, а не количество одновременных подзапросов.

    *) Изменение: теперь при возврате ответов из кэша nginx проверяет ключ
       полностью.
       Спасибо Геннадию Махомеду и Сергею Брестеру.

    *) Исправление: при использовании кэша в логах могли появляться
       сообщения "header already sent"; ошибка появилась в 1.7.5.

    *) Исправление: при использовании CephFS и директивы timer_resolution на
       Linux в логах могли появляться сообщения "writev() failed (4:
       Interrupted system call)".

    *) Исправление: в обработке ошибок конфигурации.
       Спасибо Markus Linnala.

    *) Исправление: при использовании директивы sub_filter на уровне http в
       рабочем процессе происходил segmentation fault; ошибка появилась в
       1.9.4.


Изменения в nginx 1.9.4                                           18.08.2015

    *) Изменение: директивы proxy_downstream_buffer и proxy_upstream_buffer
       в модуле stream заменены директивой proxy_buffer_size.

    *) Добавление: директива tcp_nodelay в модуле stream.

    *) Добавление: теперь можно указать несколько директив sub_filter
       одновременно.

    *) Добавление: директива sub_filter поддерживает переменные в строке
       поиска.

    *) Изменение: тестирование конфигурации могло не работать под Linux
       OpenVZ.
       Спасибо Геннадию Махомеду.

    *) Исправление: после переконфигурации старые рабочие процессы могли
       сильно нагружать процессор при больших значениях worker_connections.

    *) Исправление: при совместном использовании директив try_files и alias
       внутри location'а, заданного регулярным выражением, в рабочем
       процессе мог произойти segmentation fault; ошибка появилась в 1.7.1.

    *) Исправление: директива try_files внутри вложенного location'а,
       заданного регулярным выражением, работала неправильно, если во
       внешнем location'е использовалась директива alias.

    *) Исправление: в обработке ошибок при построении хэш-таблиц.

    *) Исправление: nginx не собирался с Visual Studio 2015.


Изменения в nginx 1.9.3                                           14.07.2015

    *) Изменение: дублирующиеся блоки http, mail и stream теперь запрещены.

    *) Добавление: ограничение количества соединений в модуле stream.

    *) Добавление: ограничение скорости в модуле stream.

    *) Исправление: директива zone в блоке upstream не работала на Windows.

    *) Исправление: совместимость с LibreSSL в модуле stream.
       Спасибо Piotr Sikora.

    *) Исправление: в параметре --builddir в configure.
       Спасибо Piotr Sikora.

    *) Исправление: директива ssl_stapling_file не работала; ошибка
       появилась в 1.9.2.
       Спасибо Faidon Liambotis и Brandon Black.

    *) Исправление: при использовании директивы ssl_stapling в рабочем
       процессе мог произойти segmentation fault; ошибка появилась в 1.9.2.
       Спасибо Matthew Baldwin.


Изменения в nginx 1.9.2                                           16.06.2015

    *) Добавление: параметр backlog директивы listen в почтовом
       прокси-сервере и модуле stream.

    *) Добавление: директивы allow и deny в модуле stream.

    *) Добавление: директива proxy_bind в модуле stream.

    *) Добавление: директива proxy_protocol в модуле stream.

    *) Добавление: ключ -T.

    *) Добавление: параметр REQUEST_SCHEME добавлен в стандартные
       конфигурационные файлы fastcgi.conf, fastcgi_params, scgi_params и
       uwsgi_params.

    *) Исправление: параметр reuseport директивы listen в модуле stream не
       работал.

    *) Исправление: OCSP stapling в некоторых случаях мог вернуть устаревший
       OCSP-ответ.


Изменения в nginx 1.9.1                                           26.05.2015

    *) Изменение: теперь протокол SSLv3 по умолчанию запрещён.

    *) Изменение: некоторые давно устаревшие директивы больше не
       поддерживаются.

    *) Добавление: параметр reuseport директивы listen.
       Спасибо Yingqi Lu из Intel и Sepherosa Ziehau.

    *) Добавление: переменная $upstream_connect_time.

    *) Исправление: в директиве hash на big-endian платформах.

    *) Исправление: nginx мог не запускаться на некоторых старых версиях
       Linux; ошибка появилась в 1.7.11.

    *) Исправление: в парсинге IP-адресов.
       Спасибо Сергею Половко.


Изменения в nginx 1.9.0                                           28.04.2015

    *) Изменение: устаревшие методы обработки соединений aio и rtsig больше
       не поддерживаются.

    *) Добавление: директива zone в блоке upstream.

    *) Добавление: модуль stream.

    *) Добавление: поддержка byte ranges для ответов модуля
       ngx_http_memcached_module.
       Спасибо Martin Mlynář.

    *) Добавление: разделяемую память теперь можно использовать на версиях
       Windows с рандомизацией адресного пространства.
       Спасибо Сергею Брестеру.

    *) Добавление: директиву error_log теперь можно использовать на уровнях
       mail и server в почтовом прокси-сервере.

    *) Исправление: параметр proxy_protocol директивы listen не работал,
       если не был указан в первой директиве listen для данного
       listen-сокета.


Изменения в nginx 1.7.12                                          07.04.2015

    *) Добавление: теперь директива tcp_nodelay работает для SSL-соединений
       с бэкендами.

    *) Добавление: теперь потоки могут использоваться для чтения заголовков
       файлов в кэше.

    *) Исправление: в директиве proxy_request_buffering.

    *) Исправление: при использовании потоков на Linux в рабочем процессе
       мог произойти segmentation fault.

    *) Исправление: в обработке ошибок при использовании директивы
       ssl_stapling.
       Спасибо Filipe da Silva.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.7.11                                          24.03.2015

    *) Изменение: параметр sendfile директивы aio более не нужен; теперь
       nginx автоматически использует AIO для подгрузки данных для sendfile,
       если одновременно используются директивы aio и sendfile.

    *) Добавление: экспериментальная поддержка потоков.

    *) Добавление: директивы proxy_request_buffering,
       fastcgi_request_buffering, scgi_request_buffering и
       uwsgi_request_buffering.

    *) Добавление: экспериментальное API для обработки тела запроса.

    *) Добавление: проверка клиентских SSL-сертификатов в почтовом
       прокси-сервере.
       Спасибо Sven Peter, Franck Levionnois и Filipe Da Silva.

    *) Добавление: уменьшение времени запуска при использовании директивы
       "hash ... consistent" в блоке upstream.
       Спасибо Wai Keen Woon.

    *) Добавление: отладочное логгирование в кольцевой буфер в памяти.

    *) Исправление: в обработке хэш-таблиц.
       Спасибо Chris West.

    *) Исправление: в директиве proxy_cache_revalidate.

    *) Исправление: SSL-соединения могли зависать, если использовался
       отложенный accept или параметр proxy_protocol директивы listen.
       Спасибо James Hamlin.

    *) Исправление: переменная $upstream_response_time могла содержать
       неверное значение при использовании директивы image_filter.

    *) Исправление: в обработке целочисленных переполнений.
       Спасибо Régis Leroy.

    *) Исправление: при использовании LibreSSL было невозможно включить
       поддержку SSLv3.

    *) Исправление: при использовании LibreSSL в логах появлялись сообщения
       "ignoring stale global SSL error ... called a function you should not
       call".

    *) Исправление: сертификаты, указанные в директивах
       ssl_client_certificate и ssl_trusted_certificate, использовались для
       автоматического построения цепочек сертификатов.


Изменения в nginx 1.7.10                                          10.02.2015

    *) Добавление: параметр use_temp_path директив proxy_cache_path,
       fastcgi_cache_path, scgi_cache_path и uwsgi_cache_path.

    *) Добавление: переменная $upstream_header_time.

    *) Изменение: теперь при переполнении диска nginx пытается писать
       error_log'и только раз в секунду.

    *) Исправление: директива try_files при тестировании каталогов не
       игнорировала обычные файлы.
       Спасибо Damien Tournoud.

    *) Исправление: при использовании директивы sendfile на OS X возникали
       ошибки "sendfile() failed"; ошибка появилась в nginx 1.7.8.

    *) Исправление: в лог могли писаться сообщения "sem_post() failed".

    *) Исправление: nginx не собирался с musl libc.
       Спасибо James Taylor.

    *) Исправление: nginx не собирался на Tru64 UNIX.
       Спасибо Goetz T. Fischer.


Изменения в nginx 1.7.9                                           23.12.2014

    *) Добавление: директивы proxy_cache, fastcgi_cache, scgi_cache и
       uwsgi_cache поддерживают переменные.

    *) Добавление: директива expires поддерживает переменные.

    *) Добавление: возможность загрузки секретных ключей с аппаратных
       устройств с помощью OpenSSL engines.
       Спасибо Дмитрию Пичулину.

    *) Добавление: директива autoindex_format.

    *) Исправление: ревалидация элементов кэша теперь используется только
       для ответов с кодами 200 и 206.
       Спасибо Piotr Sikora.

    *) Исправление: строка "TE" заголовка запроса клиента передавалась на
       бэкенд при проксировании.

    *) Исправление: директивы proxy_pass, fastcgi_pass, scgi_pass и
       uwsgi_pass могли неправильно работать внутри блоков if и
       limit_except.

    *) Исправление: директива proxy_store с параметром "on" игнорировалась,
       если на предыдущем уровне использовалась директива proxy_store с явно
       заданным путём к файлам.

    *) Исправление: nginx не собирался с BoringSSL.
       Спасибо Lukas Tribus.


Изменения в nginx 1.7.8                                           02.12.2014

    *) Изменение: теперь строки "If-Modified-Since", "If-Range" и им
       подобные в заголовке запроса клиента передаются бэкенду при
       включённом кэшировании, если nginx заранее знает, что не будет
       кэшировать ответ (например, при использовании proxy_cache_min_uses).

    *) Изменение: теперь после истечения proxy_cache_lock_timeout nginx
       отправляет запрос на бэкенд без кэширования; новые директивы
       proxy_cache_lock_age, fastcgi_cache_lock_age, scgi_cache_lock_age и
       uwsgi_cache_lock_age позволяют указать, через какое время блокировка
       будет принудительно снята и будет сделана ещё одна попытка
       закэшировать ответ.

    *) Изменение: директива log_format теперь может использоваться только на
       уровне http.

    *) Добавление: директивы proxy_ssl_certificate,
       proxy_ssl_certificate_key, proxy_ssl_password_file,
       uwsgi_ssl_certificate, uwsgi_ssl_certificate_key и
       uwsgi_ssl_password_file.
       Спасибо Piotr Sikora.

    *) Добавление: теперь с помощью X-Accel-Redirect можно перейти в
       именованный location.
       Спасибо Toshikuni Fukaya.

    *) Добавление: теперь директива tcp_nodelay работает для
       SPDY-соединений.

    *) Добавление: новые директивы в скриптах подсветки синтаксиса для vim.
       Спасибо Peter Wu.

    *) Исправление: nginx игнорировал значение "s-maxage" в строке
       "Cache-Control" в заголовке ответа бэкенда.
       Спасибо Piotr Sikora.

    *) Исправление: в модуле ngx_http_spdy_module.
       Спасибо Piotr Sikora.

    *) Исправление: в директиве ssl_password_file при использовании OpenSSL
       0.9.8zc, 1.0.0o, 1.0.1j.

    *) Исправление: при использовании директивы post_action в лог писались
       сообщения "header already sent"; ошибка появилась в nginx 1.5.4.

    *) Исправление: при использовании директивы "postpone_output 0" с
       SSI-подзапросами в лог могли писаться сообщения "the http output
       chain is empty".

    *) Исправление: в директиве proxy_cache_lock при использовании
       SSI-подзапросов.
       Спасибо Yichun Zhang.


Изменения в nginx 1.7.7                                           28.10.2014

    *) Изменение: теперь nginx учитывает при кэшировании строку "Vary" в
       заголовке ответа бэкенда.

    *) Добавление: директивы proxy_force_ranges, fastcgi_force_ranges,
       scgi_force_ranges и uwsgi_force_ranges.

    *) Добавление: директивы proxy_limit_rate, fastcgi_limit_rate,
       scgi_limit_rate и uwsgi_limit_rate.

    *) Добавление: параметр Vary директив proxy_ignore_headers,
       fastcgi_ignore_headers, scgi_ignore_headers и uwsgi_ignore_headers.

    *) Исправление: последняя часть ответа, полученного от бэкенда при
       небуферизированном проксировании, могла не отправляться клиенту, если
       использовались директивы gzip или gunzip.

    *) Исправление: в директиве proxy_cache_revalidate.
       Спасибо Piotr Sikora.

    *) Исправление: в обработке ошибок.
       Спасибо Yichun Zhang и Даниилу Бондареву.

    *) Исправление: в директивах proxy_next_upstream_tries и
       proxy_next_upstream_timeout.
       Спасибо Feng Gu.

    *) Исправление: nginx/Windows не собирался с MinGW-w64 gcc.
       Спасибо Kouhei Sutou.


Изменения в nginx 1.7.6                                           30.09.2014

    *) Изменение: устаревшая директива limit_zone больше не поддерживается.

    *) Добавление: в директивах limit_conn_zone и limit_req_zone теперь
       можно использовать комбинации нескольких переменных.

    *) Исправление: при повторной отправке FastCGI-запроса на бэкенд тело
       запроса могло передаваться неправильно.

    *) Исправление: в логгировании в syslog.


Изменения в nginx 1.7.5                                           16.09.2014

    *) Безопасность: при использовании общего для нескольких блоков server
       разделяемого кэша SSL-сессий или общего ключа для шифрования TLS
       session tickets было возможно повторно использовать SSL-сессию в
       контексте другого блока server (CVE-2014-3616).
       Спасибо Antoine Delignat-Lavaud.

    *) Изменение: директиву stub_status теперь можно указывать без
       параметров.

    *) Добавление: параметр always директивы add_header.

    *) Добавление: директивы proxy_next_upstream_tries,
       proxy_next_upstream_timeout, fastcgi_next_upstream_tries,
       fastcgi_next_upstream_timeout, memcached_next_upstream_tries,
       memcached_next_upstream_timeout, scgi_next_upstream_tries,
       scgi_next_upstream_timeout, uwsgi_next_upstream_tries и
       uwsgi_next_upstream_timeout.

    *) Исправление: в параметре if директивы access_log.

    *) Исправление: в модуле ngx_http_perl_module.
       Спасибо Piotr Sikora.

    *) Исправление: директива listen почтового прокси-сервера не позволяла
       указать более двух параметров.

    *) Исправление: директива sub_filter не работала с заменяемой строкой из
       одного символа.

    *) Исправление: запросы могли зависать, если использовался resolver и в
       процессе обращения к DNS-серверу происходил таймаут.

    *) Исправление: в модуле ngx_http_spdy_module при использовании
       совместно с AIO.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если с помощью директивы set изменялись переменные "$http_...",
       "$sent_http_..." или "$upstream_http_...".

    *) Исправление: в обработке ошибок выделения памяти.
       Спасибо Markus Linnala и Feng Gu.


Изменения в nginx 1.7.4                                           05.08.2014

    *) Безопасность: pipelined-команды не отбрасывались после команды
       STARTTLS в SMTP прокси-сервере (CVE-2014-3556); ошибка появилась в
       1.5.6.
       Спасибо Chris Boulton.

    *) Изменение: экранирование символов в URI теперь использует
       шестнадцатеричные цифры в верхнем регистре.
       Спасибо Piotr Sikora.

    *) Добавление: теперь nginx можно собрать с BoringSSL и LibreSSL.
       Спасибо Piotr Sikora.

    *) Исправление: запросы могли зависать, если использовался resolver и
       DNS-сервер возвращал некорректный ответ; ошибка появилась в 1.5.8.

    *) Исправление: в модуле ngx_http_spdy_module.
       Спасибо Piotr Sikora.

    *) Исправление: переменная $uri могла содержать мусор при возврате
       ошибок с кодом 400.
       Спасибо Сергею Боброву.

    *) Исправление: в обработке ошибок в директиве proxy_store и в модуле
       ngx_http_dav_module.
       Спасибо Feng Gu.

    *) Исправление: при логгировании ошибок в syslog мог происходить
       segmentation fault; ошибка появилась в 1.7.1.

    *) Исправление: переменные $geoip_latitude, $geoip_longitude,
       $geoip_dma_code и $geoip_area_code могли не работать.
       Спасибо Yichun Zhang.

    *) Исправление: в обработке ошибок выделения памяти.
       Спасибо Tatsuhiko Kubo и Piotr Sikora.


Изменения в nginx 1.7.3                                           08.07.2014

    *) Добавление: weak entity tags теперь не удаляются при изменениях
       ответа, а strong entity tags преобразуются в weak.

    *) Добавление: ревалидация элементов кэша теперь, если это возможно,
       использует заголовок If-None-Match.

    *) Добавление: директива ssl_password_file.

    *) Исправление: при возврате ответа из кэша заголовок запроса
       If-None-Match игнорировался, если в ответе не было заголовка
       Last-Modified.

    *) Исправление: сообщения "peer closed connection in SSL handshake" при
       соединении с бэкендами логгировались на уровне info вместо error.

    *) Исправление: в модуле ngx_http_dav_module в nginx/Windows.

    *) Исправление: SPDY-соединения могли неожиданно закрываться, если
       использовалось кэширование.


Изменения в nginx 1.7.2                                           17.06.2014

    *) Добавление: директива hash в блоке upstream.

    *) Добавление: дефрагментация свободных блоков разделяемой памяти.
       Спасибо Wandenberg Peixoto и Yichun Zhang.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалось значение access_log по умолчанию; ошибка
       появилась в 1.7.0.
       Спасибо Piotr Sikora.

    *) Исправление: завершающий слэш ошибочно удалялся из последнего
       параметра директивы try_files.

    *) Исправление: nginx мог не собираться на OS X.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.7.1                                           27.05.2014

    *) Добавление: переменные "$upstream_cookie_...".

    *) Добавление: переменная $ssl_client_fingerprint.

    *) Добавление: директивы error_log и access_log теперь поддерживают
       логгирование в syslog.

    *) Добавление: почтовый прокси-сервер теперь логгирует порт клиента при
       соединении.

    *) Исправление: утечки памяти при использовании директивы
       "ssl_stapling".
       Спасибо Filipe da Silva.

    *) Исправление: директива alias внутри location'а, заданного регулярным
       выражением, работала неправильно, если использовались директивы if
       или limit_except.

    *) Исправление: директива charset не ставила кодировку для сжатых
       ответов бэкендов.

    *) Исправление: директива proxy_pass без URI могла использовать
       оригинальный запрос после установки переменной $args.
       Спасибо Yichun Zhang.

    *) Исправление: в работе параметра none директивы smtp_auth; ошибка
       появилась в 1.5.6.
       Спасибо Святославу Никольскому.

    *) Исправление: при совместном использовании sub_filter и SSI ответы
       могли передаваться неверно.

    *) Исправление: nginx не собирался с параметром --with-file-aio на
       Linux/aarch64.


Изменения в nginx 1.7.0                                           24.04.2014

    *) Добавление: проверка SSL-сертификатов бэкендов.

    *) Добавление: поддержка SNI при работе с бэкендами по SSL.

    *) Добавление: переменная $ssl_server_name.

    *) Добавление: параметр if директивы access_log.


Изменения в nginx 1.5.13                                          08.04.2014

    *) Изменение: улучшена обработка хэш-таблиц; в директивах
       variables_hash_max_size и types_hash_bucket_size значения по
       умолчанию изменены на 1024 и 64 соответственно.

    *) Добавление: модуль ngx_http_mp4_module теперь понимает аргумент end.

    *) Добавление: поддержка byte ranges модулем ngx_http_mp4_module и при
       сохранении ответов в кэш.

    *) Исправление: теперь nginx не пишет в лог сообщения "ngx_slab_alloc()
       failed: no memory" при использовании разделяемой памяти в
       ssl_session_cache и в модуле ngx_http_limit_req_module.

    *) Исправление: директива underscores_in_headers не разрешала
       подчёркивание в первом символе заголовка.
       Спасибо Piotr Sikora.

    *) Исправление: cache manager мог нагружать процессор при выходе в
       nginx/Windows.

    *) Исправление: при использовании ssl_session_cache с параметром shared
       рабочий процесс nginx/Windows завершался аварийно.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.5.12                                          18.03.2014

    *) Безопасность: при обработке специально созданного запроса модулем
       ngx_http_spdy_module могло происходить переполнение буфера в рабочем
       процессе, что потенциально могло приводить к выполнению произвольного
       кода (CVE-2014-0133).
       Спасибо Lucas Molas из Programa STIC, Fundación Dr. Manuel Sadosky,
       Buenos Aires, Argentina.

    *) Добавление: параметр proxy_protocol в директивах listen и
       real_ip_header, переменная $proxy_protocol_addr.

    *) Исправление: в директиве fastcgi_next_upstream.
       Спасибо Lucas Molas.


Изменения в nginx 1.5.11                                          04.03.2014

    *) Безопасность: при обработке специально созданного запроса модулем
       ngx_http_spdy_module на 32-битных платформах могла повреждаться
       память рабочего процесса, что потенциально могло приводить к
       выполнению произвольного кода (CVE-2014-0088); ошибка появилась в
       1.5.10.
       Спасибо Lucas Molas из Programa STIC, Fundación Dr. Manuel Sadosky,
       Buenos Aires, Argentina.

    *) Добавление: переменная $ssl_session_reused.

    *) Исправление: директива client_max_body_size могла не работать при
       чтении тела запроса с использованием chunked transfer encoding;
       ошибка появилась в 1.3.9.
       Спасибо Lucas Molas.

    *) Исправление: при проксировании WebSocket-соединений в рабочем
       процессе мог произойти segmentation fault.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_spdy_module на 32-битных
       платформах; ошибка появилась в 1.5.10.

    *) Исправление: значение переменной $upstream_status могло быть
       неверным, если использовались директивы proxy_cache_use_stale или
       proxy_cache_revalidate.
       Спасибо Piotr Sikora.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если ошибки с кодом 400 с помощью директивы error_page
       перенаправлялись в именованный location.

    *) Исправление: nginx/Windows не собирался с Visual Studio 2013.


Изменения в nginx 1.5.10                                          04.02.2014

    *) Добавление: модуль ngx_http_spdy_module теперь использует протокол
       SPDY 3.1.
       Спасибо Automattic и MaxCDN за спонсирование разработки.

    *) Добавление: модуль ngx_http_mp4_module теперь пропускает дорожки,
       имеющие меньшую длину, чем запрошенная перемотка.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если переменная $ssl_session_id использовалась при логгировании;
       ошибка появилась в 1.5.9.

    *) Исправление: переменные $date_local и $date_gmt использовали неверный
       формат вне модуля ngx_http_ssi_filter_module.

    *) Исправление: клиентские соединения могли сразу закрываться, если
       использовался отложенный accept; ошибка появилась в 1.3.15.

    *) Исправление: сообщения "getsockopt(TCP_FASTOPEN) ... failed"
       записывались в лог в процессе обновления исполняемого файла на Linux;
       ошибка появилась в 1.5.8.
       Спасибо Piotr Sikora.


Изменения в nginx 1.5.9                                           22.01.2014

    *) Изменение: теперь в заголовке X-Accel-Redirect nginx ожидает
       закодированный URI.

    *) Добавление: директива ssl_buffer_size.

    *) Добавление: директиву limit_rate теперь можно использовать для
       ограничения скорости передачи ответов клиенту в SPDY-соединениях.

    *) Добавление: директива spdy_chunk_size.

    *) Добавление: директива ssl_session_tickets.
       Спасибо Dirkjan Bussink.

    *) Исправление: переменная $ssl_session_id содержала всю сессию в
       сериализованном виде вместо её идентификатора.
       Спасибо Ivan Ristić.

    *) Исправление: nginx неправильно обрабатывал закодированный символ "?"
       в команде SSI include.

    *) Исправление: модуль ngx_http_dav_module не раскодировал целевой URI
       при обработке методов COPY и MOVE.

    *) Исправление: resolver не понимал доменные имена с точкой в конце.
       Спасибо Yichun Zhang.

    *) Исправление: при проксировании в логах могли появляться сообщения
       "zero size buf in output"; ошибка появилась в 1.3.9.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_spdy_module.

    *) Исправление: при использовании методов обработки соединений select,
       poll и /dev/poll проксируемые WebSocket-соединения могли зависать
       сразу после открытия.

    *) Исправление: директива xclient почтового прокси-сервера некорректно
       передавала IPv6-адреса.


Изменения в nginx 1.5.8                                           17.12.2013

    *) Добавление: теперь resolver поддерживает IPv6.

    *) Добавление: директива listen поддерживает параметр fastopen.
       Спасибо Mathew Rodley.

    *) Добавление: поддержка SSL в модуле ngx_http_uwsgi_module.
       Спасибо Roberto De Ioris.

    *) Добавление: скрипты подсветки синтаксиса для vim добавлены в contrib.
       Спасибо Evan Miller.

    *) Исправление: при чтении тела запроса с использованием chunked
       transfer encoding по SSL-соединению мог произойти таймаут.

    *) Исправление: директива master_process работала неправильно в
       nginx/Windows.

    *) Исправление: параметр setfib директивы listen мог не работать.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.5.7                                           19.11.2013

    *) Безопасность: символ, следующий за незакодированным пробелом в строке
       запроса, обрабатывался неправильно (CVE-2013-4547); ошибка появилась
       в 0.8.41.
       Спасибо Ivan Fratric из Google Security Team.

    *) Изменение: уровень логгирования ошибок auth_basic об отсутствии
       пароля понижен с уровня error до info.

    *) Добавление: директивы proxy_cache_revalidate,
       fastcgi_cache_revalidate, scgi_cache_revalidate и
       uwsgi_cache_revalidate.

    *) Добавление: директива ssl_session_ticket_key.
       Спасибо Piotr Sikora.

    *) Исправление: директива "add_header Cache-Control ''" добавляла строку
       заголовка ответа "Cache-Control" с пустым значением.

    *) Исправление: директива "satisfy any" могла вернуть ошибку 403 вместо
       401 при использовании директив auth_request и auth_basic.
       Спасибо Jan Marc Hoffmann.

    *) Исправление: параметры accept_filter и deferred директивы listen
       игнорировались для listen-сокетов, создаваемых в процессе обновления
       исполняемого файла.
       Спасибо Piotr Sikora.

    *) Исправление: часть данных, полученных от бэкенда при
       небуферизированном проксировании, могла не отправляться клиенту
       сразу, если использовались директивы gzip или gunzip.
       Спасибо Yichun Zhang.

    *) Исправление: в обработке ошибок в модуле
       ngx_http_gunzip_filter_module.

    *) Исправление: ответы могли зависать, если использовался модуль
       ngx_http_spdy_module и директива auth_request.

    *) Исправление: утечки памяти в nginx/Windows.


Изменения в nginx 1.5.6                                           01.10.2013

    *) Добавление: директива fastcgi_buffering.

    *) Добавление: директивы proxy_ssl_protocols и proxy_ssl_ciphers.
       Спасибо Piotr Sikora.

    *) Добавление: оптимизация SSL handshake при использовании длинных
       цепочек сертификатов.

    *) Добавление: почтовый прокси-сервер поддерживает SMTP pipelining.

    *) Исправление: в модуле ngx_http_auth_basic_module при использовании
       метода шифрования паролей "$apr1$".
       Спасибо Markus Linnala.

    *) Исправление: на MacOSX, Cygwin и nginx/Windows для обработки запроса
       мог использоваться неверный location, если для задания location'ов
       использовались символы разных регистров.

    *) Исправление: автоматическое перенаправление с добавлением
       завершающего слэша для проксированных location'ов могло не работать.

    *) Исправление: в почтовом прокси-сервере.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.5.5                                           17.09.2013

    *) Изменение: теперь nginx по умолчанию использует HTTP/1.0, если точно
       определить протокол не удалось.

    *) Добавление: директива disable_symlinks теперь использует O_PATH на
       Linux.

    *) Добавление: для определения того, что клиент закрыл соединение, при
       использовании метода epoll теперь используются события EPOLLRDHUP.

    *) Исправление: в директиве valid_referers при использовании параметра
       server_names.

    *) Исправление: переменная $request_time не работала в nginx/Windows.

    *) Исправление: в директиве image_filter.
       Спасибо Lanshun Zhou.

    *) Исправление: совместимость с OpenSSL 1.0.1f.
       Спасибо Piotr Sikora.


Изменения в nginx 1.5.4                                           27.08.2013

    *) Изменение: MIME-тип для расширения js изменён на
       "application/javascript"; значение по умолчанию директивы
       charset_types изменено соответственно.

    *) Изменение: теперь директива image_filter с параметром size возвращает
       ответ с MIME-типом "application/json".

    *) Добавление: модуль ngx_http_auth_request_module.

    *) Исправление: на старте или во время переконфигурации мог произойти
       segmentation fault, если использовалась директива try_files с пустым
       параметром.

    *) Исправление: утечки памяти при использовании в директивах root и
       auth_basic_user_file относительных путей, заданных с помощью
       переменных.

    *) Исправление: директива valid_referers неправильно выполняла
       регулярные выражения, если заголовок Referer начинался с "https://".
       Спасибо Liangbin Li.

    *) Исправление: ответы могли зависать, если использовались подзапросы и
       при обработке подзапроса происходила ошибка во время SSL handshake с
       бэкендом.
       Спасибо Aviram Cohen.

    *) Исправление: в модуле ngx_http_autoindex_module.

    *) Исправление: в модуле ngx_http_spdy_module.


Изменения в nginx 1.5.3                                           30.07.2013

    *) Изменение во внутреннем API: теперь при небуферизированной работе с
       бэкендами u->length по умолчанию устанавливается в -1.

    *) Изменение: теперь при получении неполного ответа от бэкенда nginx
       отправляет полученную часть ответа, после чего закрывает соединение с
       клиентом.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_spdy_module и директива
       client_body_in_file_only.

    *) Исправление: параметр so_keepalive директивы listen мог работать
       некорректно на DragonFlyBSD.
       Спасибо Sepherosa Ziehau.

    *) Исправление: в модуле ngx_http_xslt_filter_module.

    *) Исправление: в модуле ngx_http_sub_filter_module.


Изменения в nginx 1.5.2                                           02.07.2013

    *) Добавление: теперь можно использовать несколько директив error_log.

    *) Исправление: метод $r->header_in() встроенного перла не возвращал
       значения строк "Cookie" и "X-Forwarded-For" из заголовка запроса;
       ошибка появилась в 1.3.14.

    *) Исправление: в модуле ngx_http_spdy_module.
       Спасибо Jim Radford.

    *) Исправление: nginx не собирался на Linux при использовании x32 ABI.
       Спасибо Сергею Иванцову.


Изменения в nginx 1.5.1                                           04.06.2013

    *) Добавление: директивы ssi_last_modified, sub_filter_last_modified и
       xslt_last_modified.
       Спасибо Алексею Колпакову.

    *) Добавление: параметр http_403 в директивах proxy_next_upstream,
       fastcgi_next_upstream, scgi_next_upstream и uwsgi_next_upstream.

    *) Добавление: директивы allow и deny теперь поддерживают unix domain
       сокеты.

    *) Исправление: nginx не собирался с модулем ngx_mail_ssl_module, но без
       модуля ngx_http_ssl_module; ошибка появилась в 1.3.14.

    *) Исправление: в директиве proxy_set_body.
       Спасибо Lanshun Zhou.

    *) Исправление: в директиве lingering_time.
       Спасибо Lanshun Zhou.

    *) Исправление: параметр fail_timeout директивы server в блоке upstream
       мог не работать, если использовался параметр max_fails; ошибка
       появилась в 1.3.0.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива ssl_stapling.
       Спасибо Piotr Sikora.

    *) Исправление: в почтовом прокси-сервере.
       Спасибо Filipe Da Silva.

    *) Исправление: nginx/Windows мог перестать принимать соединения, если
       использовалось несколько рабочих процессов.


Изменения в nginx 1.5.0                                           07.05.2013

    *) Безопасность: при обработке специально созданного запроса мог
       перезаписываться стек рабочего процесса, что могло приводить к
       выполнению произвольного кода (CVE-2013-2028); ошибка появилась в
       1.3.9.
       Спасибо Greg MacManus, iSIGHT Partners Labs.


Изменения в nginx 1.4.0                                           24.04.2013

    *) Исправление: nginx не собирался с модулем ngx_http_perl_module, если
       использовался параметр --with-openssl; ошибка появилась в 1.3.16.

    *) Исправление: в работе с телом запроса из модуля ngx_http_perl_module;
       ошибка появилась в 1.3.9.


Изменения в nginx 1.3.16                                          16.04.2013

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовались подзапросы; ошибка появилась в 1.3.9.

    *) Исправление: директива tcp_nodelay вызывала ошибку при проксировании
       WebSocket-соединений в unix domain сокет.

    *) Исправление: переменная $upstream_response_length возвращала значение
       "0", если не использовалась буферизация.
       Спасибо Piotr Sikora.

    *) Исправление: в методах обработки соединений eventport и /dev/poll.


Изменения в nginx 1.3.15                                          26.03.2013

    *) Изменение: открытие и закрытие соединения без отправки в нём
       каких-либо данных больше не записывается в access_log с кодом ошибки
       400.

    *) Добавление: модуль ngx_http_spdy_module.
       Спасибо Automattic за спонсирование разработки.

    *) Добавление: директивы limit_req_status и limit_conn_status.
       Спасибо Nick Marden.

    *) Добавление: директива image_filter_interlace.
       Спасибо Ивану Боброву.

    *) Добавление: переменная $connections_waiting в модуле
       ngx_http_stub_status_module.

    *) Добавление: теперь почтовый прокси-сервер поддерживает IPv6-бэкенды.

    *) Исправление: при повторной отправке запроса на бэкенд тело запроса
       могло передаваться неправильно; ошибка появилась в 1.3.9.
       Спасибо Piotr Sikora.

    *) Исправление: в директиве client_body_in_file_only; ошибка появилась в
       1.3.9.

    *) Исправление: ответы могли зависать, если использовались подзапросы и
       при обработке подзапроса происходила DNS-ошибка.
       Спасибо Lanshun Zhou.

    *) Исправление: в процедуре учёта использования бэкендов.


Изменения в nginx 1.3.14                                          05.03.2013

    *) Добавление: переменные $connections_active, $connections_reading и
       $connections_writing в модуле ngx_http_stub_status_module.

    *) Добавление: поддержка WebSocket-соединений в модулях
       ngx_http_uwsgi_module и ngx_http_scgi_module.

    *) Исправление: в обработке виртуальных серверов при использовании SNI.

    *) Исправление: при использовании директивы "ssl_session_cache shared"
       новые сессии могли не сохраняться, если заканчивалось место в
       разделяемой памяти.
       Спасибо Piotr Sikora.

    *) Исправление: несколько заголовков X-Forwarded-For обрабатывались
       неправильно.
       Спасибо Neal Poole за спонсирование разработки.

    *) Исправление: в модуле ngx_http_mp4_module.
       Спасибо Gernot Vormayr.


Изменения в nginx 1.3.13                                          19.02.2013

    *) Изменение: теперь для сборки по умолчанию используется компилятор с
       именем "cc".

    *) Добавление: поддержка проксирования WebSocket-соединений.
       Спасибо Apcera и CloudBees за спонсирование разработки.

    *) Добавление: директива auth_basic_user_file поддерживает шифрование
       паролей методом "{SHA}".
       Спасибо Louis Opter.


Изменения в nginx 1.3.12                                          05.02.2013

    *) Добавление: директивы proxy_bind, fastcgi_bind, memcached_bind,
       scgi_bind и uwsgi_bind поддерживают переменные.

    *) Добавление: переменные $pipe, $request_length, $time_iso8601 и
       $time_local теперь можно использовать не только в директиве
       log_format.
       Спасибо Kiril Kalchev.

    *) Добавление: поддержка IPv6 в модуле ngx_http_geoip_module.
       Спасибо Gregor Kališnik.

    *) Исправление: директива proxy_method работала неверно, если была
       указана на уровне http.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался resolver и метод poll.

    *) Исправление: nginx мог нагружать процессор во время SSL handshake с
       бэкендом при использовании методов обработки соединений select, poll
       и /dev/poll.

    *) Исправление: ошибка "[crit] SSL_write() failed (SSL:)".

    *) Исправление: в директиве client_body_in_file_only; ошибка появилась в
       1.3.9.

    *) Исправление: в директиве fastcgi_keep_conn.


Изменения в nginx 1.3.11                                          10.01.2013

    *) Исправление: при записи в лог мог происходить segmentation fault;
       ошибка появилась в 1.3.10.

    *) Исправление: директива proxy_pass не работала с IP-адресами без
       явного указания порта; ошибка появилась в 1.3.10.

    *) Исправление: на старте или во время переконфигурации происходил
       segmentation fault, если директива keepalive была указана несколько
       раз в одном блоке upstream.

    *) Исправление: параметр default директивы geo не определял значение по
       умолчанию для IPv6-адресов.


Изменения в nginx 1.3.10                                          25.12.2012

    *) Изменение: для указанных в конфигурационном файле доменных имён
       теперь используются не только IPv4, но и IPv6 адреса.

    *) Изменение: теперь при использовании директивы include с маской на
       Unix-системах включаемые файлы сортируются в алфавитном порядке.

    *) Изменение: директива add_header добавляет строки в ответы с кодом
       201.

    *) Добавление: директива geo теперь поддерживает IPv6 адреса в формате
       CIDR.

    *) Добавление: параметры flush и gzip в директиве access_log.

    *) Добавление: директива auth_basic поддерживает переменные.

    *) Исправление: nginx в некоторых случаях не собирался с модулем
       ngx_http_perl_module.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_xslt_module.

    *) Исправление: nginx мог не собираться на MacOSX.
       Спасибо Piotr Sikora.

    *) Исправление: при использовании директивы limit_rate с большими
       значениями скорости на 32-битных системах ответ мог возвращаться не
       целиком.
       Спасибо Алексею Антропову.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива if.
       Спасибо Piotr Sikora.

    *) Исправление: ответ "100 Continue" выдавался вместе с ответом "413
       Request Entity Too Large".

    *) Исправление: директивы image_filter, image_filter_jpeg_quality и
       image_filter_sharpen могли наследоваться некорректно.
       Спасибо Ивану Боброву.

    *) Исправление: при использовании директивы auth_basic под Linux могли
       возникать ошибки "crypt_r() failed".

    *) Исправление: в обработке backup-серверов.
       Спасибо Thomas Chen.

    *) Исправление: при проксировании HEAD-запросов мог возвращаться
       некорректный ответ, если использовалась директива gzip.


Изменения в nginx 1.3.9                                           27.11.2012

    *) Добавление: поддержка chunked transfer encoding при получении тела
       запроса.

    *) Добавление: переменные $request_time и $msec теперь можно
       использовать не только в директиве log_format.

    *) Исправление: cache manager и cache loader могли не запускаться, если
       использовалось более 512 listen-сокетов.

    *) Исправление: в модуле ngx_http_dav_module.


Изменения в nginx 1.3.8                                           30.10.2012

    *) Добавление: параметр optional_no_ca директивы ssl_verify_client.
       Спасибо Михаилу Казанцеву и Eric O'Connor.

    *) Добавление: переменные $bytes_sent, $connection и
       $connection_requests теперь можно использовать не только в директиве
       log_format.
       Спасибо Benjamin Grössing.

    *) Добавление: параметр auto директивы worker_processes.

    *) Исправление: сообщения "cache file ... has md5 collision".

    *) Исправление: в модуле ngx_http_gunzip_filter_module.

    *) Исправление: в директиве ssl_stapling.


Изменения в nginx 1.3.7                                           02.10.2012

    *) Добавление: поддержка OCSP stapling.
       Спасибо Comodo, DigiCert и GlobalSign за спонсирование разработки.

    *) Добавление: директива ssl_trusted_certificate.

    *) Добавление: теперь resolver случайным образом меняет порядок
       возвращаемых закэшированных адресов.
       Спасибо Антону Жулину.

    *) Исправление: совместимость с OpenSSL 0.9.7.


Изменения в nginx 1.3.6                                           12.09.2012

    *) Добавление: модуль ngx_http_gunzip_filter_module.

    *) Добавление: директива memcached_gzip_flag.

    *) Добавление: параметр always директивы gzip_static.

    *) Исправление: в директиве "limit_req"; ошибка появилась в 1.1.14.
       Спасибо Charles Chen.

    *) Исправление: nginx не собирался gcc 4.7 с оптимизацией -O2 если
       использовался параметр --with-ipv6.


Изменения в nginx 1.3.5                                           21.08.2012

    *) Изменение: модуль ngx_http_mp4_module больше не отфильтровывает
       дорожки в форматах, отличных от H.264 и AAC.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если в директиве map в качестве значений использовались переменные.

    *) Исправление: в рабочем процессе мог произойти segmentation fault при
       использовании директивы geo с параметром ranges, но без параметра
       default; ошибка появилась в 0.8.43.
       Спасибо Zhen Chen и Weibin Yao.

    *) Исправление: в обработке параметра командной строки -p.

    *) Исправление: в почтовом прокси-сервере.

    *) Исправление: незначительных потенциальных ошибок.
       Спасибо Coverity.

    *) Исправление: nginx/Windows не собирался с Visual Studio 2005 Express.
       Спасибо HAYASHI Kentaro.


Изменения в nginx 1.3.4                                           31.07.2012

    *) Изменение: теперь на слушающих IPv6-сокетах параметр ipv6only включён
       по умолчанию.

    *) Добавление: поддержка компилятора Clang.

    *) Исправление: могли создаваться лишние слушающие сокеты.
       Спасибо Роману Одайскому.

    *) Исправление: nginx/Windows мог нагружать процессор, если при запуске
       рабочего процесса происходила ошибка.
       Спасибо Ricardo Villalobos Guevara.

    *) Исправление: директивы proxy_pass_header, fastcgi_pass_header,
       scgi_pass_header, uwsgi_pass_header, proxy_hide_header,
       fastcgi_hide_header, scgi_hide_header и uwsgi_hide_header могли
       наследоваться некорректно.


Изменения в nginx 1.3.3                                           10.07.2012

    *) Добавление: поддержка entity tags и директива etag.

    *) Исправление: при использовании директивы map с параметром hostnames
       не игнорировалась конечная точка в исходном значении.

    *) Исправление: для обработки запроса мог использоваться неверный
       location, если переход в именованный location происходил после
       изменения URI с помощью директивы rewrite.


Изменения в nginx 1.3.2                                           26.06.2012

    *) Изменение: параметр single директивы keepalive теперь игнорируется.

    *) Изменение: сжатие SSL теперь отключено в том числе при использовании
       OpenSSL старее 1.0.0.

    *) Добавление: директиву "ip_hash" теперь можно использовать для
       балансировки IPv6 клиентов.

    *) Добавление: переменную $status теперь можно использовать не только в
       директиве log_format.

    *) Исправление: при завершении рабочего процесса мог произойти
       segmentation fault, если использовалась директива resolver.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался модуль ngx_http_mp4_module.

    *) Исправление: в модуле ngx_http_mp4_module.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовались конфликтующие имена серверов с масками.

    *) Исправление: на платформе ARM nginx мог аварийно завершаться по
       сигналу SIGBUS.

    *) Исправление: во время переконфигурации на HP-UX в лог записывался
       alert "sendmsg() failed (9: Bad file number)".


Изменения в nginx 1.3.1                                           05.06.2012

    *) Безопасность: теперь nginx/Windows игнорирует точку в конце
       компонента URI и не разрешает URI, содержащие последовательность
       ":$".
       Спасибо Владимиру Кочеткову, Positive Research Center.

    *) Добавление: директивы proxy_pass, fastcgi_pass, scgi_pass, uwsgi_pass
       и директива server в блоке upstream теперь поддерживают IPv6-адреса.

    *) Добавление: в директиве resolver теперь можно указывать порт и
       задавать IPv6-адреса DNS-серверов.

    *) Добавление: директива least_conn в блоке upstream.

    *) Добавление: при использовании директивы ip_hash теперь можно задавать
       веса серверов.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива image_filter; ошибка появилась в 1.3.0.

    *) Исправление: nginx не собирался с модулем ngx_cpp_test_module; ошибка
       появилась в 1.1.12.

    *) Исправление: доступ к переменным из SSI и встроенного перла мог не
       работать после переконфигурации.
       Спасибо Yichun Zhang.

    *) Исправление: в модуле ngx_http_xslt_filter_module.
       Спасибо Kuramoto Eiji.

    *) Исправление: утечки памяти при использовании переменной $geoip_org.
       Спасибо Денису Латыпову.

    *) Исправление: в директивах proxy_cookie_domain и proxy_cookie_path.


Изменения в nginx 1.3.0                                           15.05.2012

    *) Добавление: директива debug_connection теперь поддерживает
       IPv6-адреса и параметр "unix:".

    *) Добавление: директива set_real_ip_from и параметр proxy директивы geo
       теперь поддерживают IPv6-адреса.

    *) Добавление: директивы real_ip_recursive, geoip_proxy и
       geoip_proxy_recursive.

    *) Добавление: параметр proxy_recursive директивы geo.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива resolver.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовались директивы fastcgi_pass, scgi_pass или uwsgi_pass
       и бэкенд возвращал некорректный ответ.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива rewrite и в новых аргументах запроса в
       строке замены использовались переменные.

    *) Исправление: nginx мог нагружать процессор, если было достигнуто
       ограничение на количество открытых файлов.

    *) Исправление: при использовании директивы proxy_next_upstream с
       параметром http_404 nginx мог бесконечно перебирать бэкенды, если в
       блоке upstream был хотя бы один сервер с флагом backup.

    *) Исправление: при использовании директивы ip_hash установка параметра
       down директивы server могла приводить к ненужному перераспределению
       клиентов между бэкендами.

    *) Исправление: утечки сокетов.
       Спасибо Yichun Zhang.

    *) Исправление: в модуле ngx_http_fastcgi_module.


Изменения в nginx 1.2.0                                           23.04.2012

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива try_files; ошибка появилась в 1.1.19.

    *) Исправление: ответ мог быть передан не полностью, если использовалось
       больше IOV_MAX буферов.

    *) Исправление: в работе параметра crop директивы image_filter.
       Спасибо Maxim Bublis.


Изменения в nginx 1.1.19                                          12.04.2012

    *) Безопасность: при обработке специально созданного mp4 файла модулем
       ngx_http_mp4_module могли перезаписываться области памяти рабочего
       процесса, что могло приводить к выполнению произвольного кода
       (CVE-2012-2089).
       Спасибо Matthew Daley.

    *) Исправление: nginx/Windows мог завершаться аварийно.
       Спасибо Vincent Lee.

    *) Исправление: nginx нагружал процессор, если все серверы в upstream'е
       были помечены флагом backup.

    *) Исправление: директивы allow и deny могли наследоваться некорректно,
       если в них использовались IPv6 адреса.

    *) Исправление: директивы modern_browser и ancient_browser могли
       наследоваться некорректно.

    *) Исправление: таймауты могли работать некорректно на Solaris/SPARC.

    *) Исправление: в модуле ngx_http_mp4_module.


Изменения в nginx 1.1.18                                          28.03.2012

    *) Изменение: теперь keepalive соединения не запрещены для Safari по
       умолчанию.

    *) Добавление: переменная $connection_requests.

    *) Добавление: переменные $tcpinfo_rtt, $tcpinfo_rttvar,
       $tcpinfo_snd_cwnd и $tcpinfo_rcv_space.

    *) Добавление: директива worker_cpu_affinity теперь работает на FreeBSD.

    *) Добавление: директивы xslt_param и xslt_string_param.
       Спасибо Samuel Behan.

    *) Исправление: в configure.
       Спасибо Piotr Sikora.

    *) Исправление: в модуле ngx_http_xslt_filter_module.

    *) Исправление: nginx не собирался на Debian GNU/Hurd.


Изменения в nginx 1.1.17                                          15.03.2012

    *) Безопасность: содержимое ранее освобождённой памяти могло быть
       отправлено клиенту, если бэкенд возвращал специально созданный ответ.
       Спасибо Matthew Daley.

    *) Исправление: при использовании встроенного перла из SSI.
       Спасибо Matthew Daley.

    *) Исправление: в модуле ngx_http_uwsgi_module.


Изменения в nginx 1.1.16                                          29.02.2012

    *) Изменение: ограничение на количество одновременных подзапросов
       поднято до 200.

    *) Добавление: параметр from в директиве disable_symlinks.

    *) Добавление: директивы return и error_page теперь могут использоваться
       для возврата перенаправлений с кодом 307.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива resolver и на глобальном уровне не была
       задана директива error_log.
       Спасибо Роману Арутюняну.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовались директивы "proxy_http_version 1.1" или
       "fastcgi_keep_conn on".

    *) Исправление: утечек памяти.
       Спасибо Lanshun Zhou.

    *) Исправление: в директиве disable_symlinks.

    *) Исправление: при использовании ZFS размер кэша на диске мог считаться
       некорректно; ошибка появилась в 1.0.1.

    *) Исправление: nginx не собирался компилятором icc 12.1.

    *) Исправление: nginx не собирался gcc на Solaris; ошибка появилась в
       1.1.15.


Изменения в nginx 1.1.15                                          15.02.2012

    *) Добавление: директива disable_symlinks.

    *) Добавление: директивы proxy_cookie_domain и proxy_cookie_path.

    *) Исправление: nginx мог некорректно сообщать об ошибке "upstream
       prematurely closed connection" вместо "upstream sent too big header".
       Спасибо Feibo Li.

    *) Исправление: nginx не собирался с модулем ngx_http_perl_module, если
       использовался параметр --with-openssl.

    *) Исправление: количество внутренних перенаправлений в именованные
       location'ы не ограничивалось.

    *) Исправление: вызов $r->flush() несколько раз подряд мог приводить к
       ошибкам в модуле ngx_http_gzip_filter_module.

    *) Исправление: при использовании директивы proxy_store с
       SSI-подзапросами временные файлы могли не удаляться.

    *) Исправление: в некоторых случаях некэшируемые переменные (такие, как
       $args) возвращали старое пустое закэшированное значение.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если одновременно создавалось слишком много SSI-подзапросов; ошибка
       появилась в 0.7.25.


Изменения в nginx 1.1.14                                          30.01.2012

    *) Добавление: теперь можно указать несколько ограничений limit_req
       одновременно.

    *) Исправление: в обработке ошибок при соединении с бэкендом.
       Спасибо Piotr Sikora.

    *) Исправление: в обработке ошибок при использовании AIO на FreeBSD.

    *) Исправление: в инициализации библиотеки OpenSSL.

    *) Исправление: директивы proxy_redirect могли наследоваться
       некорректно.

    *) Исправление: утечки памяти при переконфигурации, если использовалась
       директива pcre_jit.


Изменения в nginx 1.1.13                                          16.01.2012

    *) Добавление: параметры TLSv1.1 и TLSv1.2 в директиве ssl_protocols.

    *) Исправление: параметры директивы limit_req наследовались некорректно;
       ошибка появилась в 1.1.12.

    *) Исправление: директива proxy_redirect некорректно обрабатывала
       заголовок Refresh при использовании регулярных выражений.

    *) Исправление: директива proxy_cache_use_stale с параметром error не
       возвращала ответ из кэша, если все бэкенды были признаны
       неработающими.

    *) Исправление: директива worker_cpu_affinity могла не работать.

    *) Исправление: nginx не собирался на Solaris; ошибка появилась в
       1.1.12.

    *) Исправление: в модуле ngx_http_mp4_module.


Изменения в nginx 1.1.12                                          26.12.2011

    *) Изменение: после перенаправления запроса с помощью директивы
       error_page директива proxy_pass без URI теперь использует изменённый
       URI.
       Спасибо Lanshun Zhou.

    *) Добавление: директивы proxy/fastcgi/scgi/uwsgi_cache_lock,
       proxy/fastcgi/scgi/uwsgi_cache_lock_timeout.

    *) Добавление: директива pcre_jit.

    *) Добавление: SSI команда if поддерживает выделения в регулярных
       выражениях.

    *) Исправление: SSI команда if не работала внутри команды block.

    *) Исправление: директивы limit_conn_log_level и limit_req_log_level
       могли не работать.

    *) Исправление: директива limit_rate не позволяла передавать на полной
       скорости, даже если был указан очень большой лимит.

    *) Исправление: директива sendfile_max_chunk не работала, если
       использовалась директива limit_rate.

    *) Исправление: если в директиве proxy_pass использовались переменные и
       не был указан URI, всегда использовался URI исходного запроса.

    *) Исправление: после перенаправления запроса с помощью директивы
       try_files директива proxy_pass без URI могла использовать URI
       исходного запроса.
       Спасибо Lanshun Zhou.

    *) Исправление: в модуле ngx_http_scgi_module.

    *) Исправление: в модуле ngx_http_mp4_module.

    *) Исправление: nginx не собирался на Solaris; ошибка появилась в 1.1.9.


Изменения в nginx 1.1.11                                          12.12.2011

    *) Добавление: параметр so_keepalive в директиве listen.
       Спасибо Всеволоду Стахову.

    *) Добавление: параметр if_not_empty в директивах
       fastcgi/scgi/uwsgi_param.

    *) Добавление: переменная $https.

    *) Добавление: директива proxy_redirect поддерживает переменные в первом
       параметре.

    *) Добавление: директива proxy_redirect поддерживает регулярные
       выражения.

    *) Исправление: переменная $sent_http_cache_control могла содержать
       неверное значение при использовании директивы expires.
       Спасибо Yichun Zhang.

    *) Исправление: директива read_ahead могла не работать при использовании
       совместно с try_files и open_file_cache.

    *) Исправление: если в параметре inactive директивы proxy_cache_path
       было указано малое время, в рабочем процессе мог произойти
       segmentation fault.

    *) Исправление: ответы из кэша могли зависать.


Изменения в nginx 1.1.10                                          30.11.2011

    *) Исправление: при использовании AIO на Linux в рабочем процессе
       происходил segmentation fault; ошибка появилась в 1.1.9.


Изменения в nginx 1.1.9                                           28.11.2011

    *) Изменение: теперь двойные кавычки экранируется при выводе
       SSI-командой echo.
       Спасибо Зауру Абасмирзоеву.

    *) Добавление: параметр valid в директиве resolver. По умолчанию теперь
       используется TTL, возвращённый DNS-сервером.
       Спасибо Кириллу Коринскому.

    *) Исправление: nginx мог перестать отвечать, если рабочий процесс
       завершался аварийно.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалось SNI; ошибка появилась в 1.1.2.

    *) Исправление: в директиве keepalive_disable; ошибка появилась в 1.1.8.
       Спасибо Александру Усову.

    *) Исправление: сигнал SIGWINCH переставал работать после первого
       обновления исполняемого файла; ошибка появилась в 1.1.1.

    *) Исправление: теперь ответы бэкендов, длина которых не соответствует
       заголовку Content-Length, не кэширутся.

    *) Исправление: в директиве scgi_param при использовании составных
       параметров.

    *) Исправление: в методе epoll.
       Спасибо Yichun Zhang.

    *) Исправление: в модуле ngx_http_flv_module.
       Спасибо Piotr Sikora.

    *) Исправление: в модуле ngx_http_mp4_module.

    *) Исправление: теперь nginx понимает IPv6-адреса в строке запроса и в
       заголовке Host.

    *) Исправление: директивы add_header и expires не работали для ответов с
       кодом 206, если запрос проксировался.

    *) Исправление: nginx не собирался на FreeBSD 10.

    *) Исправление: nginx не собирался на AIX.


Изменения в nginx 1.1.8                                           14.11.2011

    *) Изменение: модуль ngx_http_limit_zone_module переименован в
       ngx_http_limit_conn_module.

    *) Изменение: директива limit_zone заменена директивой limit_conn_zone с
       новым синтаксисом.

    *) Добавление: поддержка ограничения по нескольким limit_conn на одном
       уровне.

    *) Добавление: директива image_filter_sharpen.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если resolver получил большой DNS-ответ.
       Спасибо Ben Hawkes.

    *) Исправление: в вычислении ключа для кэширования, если использовалась
       внутренняя реализация MD5; ошибка появилась в 1.0.4.

    *) Исправление: строки "If-Modified-Since", "If-Range" и им подобные в
       заголовке запроса клиента могли передаваться бэкенду при кэшировании;
       или не передаваться при выключенном кэшировании, если кэширование
       было включено в другой части конфигурации.

    *) Исправление: модуль ngx_http_mp4_module выдавал неверную строку
       "Content-Length" в заголовке ответа, использовался аргумент start.
       Спасибо Piotr Sikora.


Изменения в nginx 1.1.7                                           31.10.2011

    *) Добавление: поддержка нескольких DNS серверов в директиве "resolver".
       Спасибо Кириллу Коринскому.

    *) Исправление: на старте или во время переконфигурации происходил
       segmentation fault, если директива ssl использовалась на уровне http
       и не был указан ssl_certificate.

    *) Исправление: уменьшено потребление памяти при проксировании больших
       файлов, если они буферизировались на диск.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовалась директива "proxy_http_version 1.1".

    *) Исправление: в директиве "expires @time".


Изменения в nginx 1.1.6                                           17.10.2011

    *) Изменение во внутреннем API: теперь при внутреннем редиректе в
       именованный location контексты модулей очищаются.
       По запросу Yichun Zhang.

    *) Изменение: теперь если сервер, описанный в блоке upstream, был
       признан неработающим, то после истечения fail_timeout на него будет
       отправлен только один запрос; сервер будет считаться работающим, если
       успешно ответит на этот запрос.

    *) Изменение: теперь символы 0x7F-0xFF в access_log записываются в виде
       \xXX.

    *) Добавление: директивы "proxy/fastcgi/scgi/uwsgi_ignore_headers"
       теперь поддерживают значения X-Accel-Limit-Rate, X-Accel-Buffering и
       X-Accel-Charset.

    *) Добавление: уменьшение потребления памяти при использовании SSL.

    *) Исправление: некоторые UTF-8 символы обрабатывались неправильно.
       Спасибо Алексею Куцу.

    *) Исправление: директивы модуля ngx_http_rewrite_module, заданные на
       уровне server, применялись повторно, если для запроса не находилось
       ни одного location'а.

    *) Исправление: при использовании "aio sendfile" могла происходить
       утечка сокетов.

    *) Исправление: при использовании файлового AIO соединения с быстрыми
       клиентами могли быть закрыты по истечению send_timeout.

    *) Исправление: в модуле ngx_http_autoindex_module.

    *) Исправление: модуль ngx_http_mp4_module не поддерживал перемотку на
       32-битных платформах.


Изменения в nginx 1.1.5                                           05.10.2011

    *) Добавление: директивы uwsgi_buffering и scgi_buffering.
       Спасибо Peter Smit.

    *) Исправление: при использовании proxy_cache_bypass могли быть
       закэшированы некэшируемые ответы.
       Спасибо John Ferlito.

    *) Исправление: в модуле ngx_http_proxy_module при работе с бэкендами по
       HTTP/1.1.

    *) Исправление: закэшированные ответы с пустым телом возвращались
       некорректно; ошибка появилась в 0.8.31.

    *) Исправление: ответы с кодом 201 модуля ngx_http_dav_module были
       некорректны; ошибка появилась в 0.8.32.

    *) Исправление: в директиве return.

    *) Исправление: при использовании директивы "ssl_session_cache builtin"
       происходил segmentation fault; ошибка появилась в 1.1.1.


Изменения в nginx 1.1.4                                           20.09.2011

    *) Добавление: модуль ngx_http_upstream_keepalive.

    *) Добавление: директива proxy_http_version.

    *) Добавление: директива fastcgi_keep_conn.

    *) Добавление: директива worker_aio_requests.

    *) Исправление: если nginx был собран с файловым AIO, он не мог
       запускаться на Linux без поддержки AIO.

    *) Исправление: в обработке ошибок при работе с Linux AIO.
       Спасибо Hagai Avrahami.

    *) Исправление: уменьшено потребление памяти для долгоживущих запросов.

    *) Исправление: модуль ngx_http_mp4_module не поддерживал 64-битный
       MP4-атом co64.


Изменения в nginx 1.1.3                                           14.09.2011

    *) Добавление: модуль ngx_http_mp4_module.

    *) Исправление: в Linux AIO, используемым совместно с open_file_cache.

    *) Исправление: open_file_cache не обновлял информацию о файле, если
       файл был изменён не атомарно.

    *) Исправление: nginx не собирался на MacOSX 10.7.


Изменения в nginx 1.1.2                                           05.09.2011

    *) Изменение: теперь, если суммарный размер всех диапазонов больше
       размера исходного ответа, то nginx возвращает только исходный ответ,
       не обрабатывая диапазоны.

    *) Добавление: директива max_ranges.

    *) Исправление: директивы ssl_verify_client, ssl_verify_depth и
       ssl_prefer_server_cipher могли работать некорректно, если
       использовался SNI.

    *) Исправление: в директивах proxy/fastcgi/scgi/
       uwsgi_ignore_client_abort.


Изменения в nginx 1.1.1                                           22.08.2011

    *) Изменение: теперь загрузчик кэша за каждую итерацию либо обрабатывает
       число файлов, указанное в параметре load_files, либо работает не
       дольше времени, указанного в параметре loader_threshold.

    *) Изменение: SIGWINCH сигнал теперь работает только в режиме демона.

    *) Добавление: теперь разделяемые зоны и кэши используют семафоры POSIX
       на Solaris.
       Спасибо Денису Иванову.

    *) Добавление: теперь на NetBSD поддерживаются accept фильтры.

    *) Исправление: nginx не собирался на Linux 3.0.

    *) Исправление: в некоторых случаях nginx не использовал сжатие; ошибка
       появилась в 1.1.0.

    *) Исправление: обработка тела запроса могла быть неверной, если клиент
       использовал pipelining.

    *) Исправление: в директиве request_body_in_single_buf.

    *) Исправление: в директивах proxy_set_body и proxy_pass_request_body
       при использовании SSL-соединения с бэкендом.

    *) Исправление: nginx нагружал процессор, если все серверы в upstream'е
       были помечены флагом down.

    *) Исправление: при переконфигурации мог произойти segmentation fault,
       если в предыдущей конфигурации был определён, но не использовался
       ssl_session_cache.

    *) Исправление: при использовании большого количества backup-серверов в
       рабочем процессе мог произойти segmentation fault.

    *) Исправление: при использовании директив fastcgi/scgi/uwsgi_param со
       значениями, начинающимися со строки "HTTP_", в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 0.8.40.


Изменения в nginx 1.1.0                                           01.08.2011

    *) Добавление: уменьшение времени работы загрузчика кэша.

    *) Добавление: параметры loader_files, loader_sleep и loader_threshold
       директив proxy/fastcgi/scgi/uwsgi_cache_path.

    *) Добавление: уменьшение времени загрузки конфигураций с большим
       количеством HTTPS серверов.

    *) Добавление: теперь nginx поддерживает шифры с обменом ECDHE-ключами.
       Спасибо Adrian Kotelba.

    *) Добавление: директива lingering_close.
       Спасибо Максиму Дунину.

    *) Исправление: закрытия соединения для pipelined-запросов.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не запрещал сжатие при получении значения
       "gzip;q=0" в строке "Accept-Encoding" в заголовке запроса клиента.

    *) Исправление: таймаута при небуферизированном проксировании.
       Спасибо Максиму Дунину.

    *) Исправление: утечки памяти при использовании переменных в директиве
       proxy_pass при работе с бэкендом по HTTPS.
       Спасибо Максиму Дунину.

    *) Исправление: в проверке параметра директивы proxy_pass, заданного
       переменными.
       Спасибо Lanshun Zhou.

    *) Исправление: SSL не работал на QNX.
       Спасибо Максиму Дунину.

    *) Исправление: SSL модули не собирались gcc 4.6 без параметра
       --with-debug.


Изменения в nginx 1.0.5                                           19.07.2011

    *) Изменение: теперь по умолчанию используются следующие шифры SSL:
       "HIGH:!aNULL:!MD5".
       Спасибо Rob Stradling.

    *) Добавление: директивы referer_hash_max_size и
       referer_hash_bucket_size.
       Спасибо Witold Filipczyk.

    *) Добавление: переменная $uid_reset.

    *) Исправление: при использовании кэширования в рабочем процессе мог
       произойти segmentation fault.
       Спасибо Lanshun Zhou.

    *) Исправление: при использовании кэширования рабочие процессы могли
       зациклиться во время переконфигурации; ошибка появилась в 0.8.48.
       Спасибо Максиму Дунину.

    *) Исправление: сообщения "stalled cache updating".
       Спасибо Максиму Дунину.


Изменения в nginx 1.0.4                                           01.06.2011

    *) Изменение: теперь в регулярных выражениях в директиве map можно
       задать чувствительность к регистру с помощью префиксов "~" и "~*".

    *) Добавление: теперь разделяемые зоны и кэши используют семафоры POSIX
       на Linux.
       Спасибо Денису Латыпову.

    *) Исправление: сообщения "stalled cache updating".

    *) Исправление: nginx не собирался с параметром
       --without-http_auth_basic_module; ошибка появилась в 1.0.3.


Изменения в nginx 1.0.3                                           25.05.2011

    *) Добавление: директива auth_basic_user_file поддерживает шифрование
       пароля методами "$apr1", "{PLAIN}" и "{SSHA}".
       Спасибо Максиму Дунину.

    *) Добавление: директива geoip_org и переменная $geoip_org.
       Спасибо Александру Ускову, Arnaud Granal и Денису Латыпову.

    *) Добавление: модули ngx_http_geo_module и ngx_http_geoip_module
       поддерживают адреса IPv4, отображённые на IPv6 адреса.

    *) Исправление: при проверке адреса IPv4, отображённого на адрес IPv6, в
       рабочем процессе происходил segmentation fault, если директивы access
       или deny были определены только для адресов IPv6; ошибка появилась в
       0.8.22.

    *) Исправление: закэшированный ответ мог быть испорчен, если значения
       директив proxy/fastcgi/scgi/uwsgi_cache_bypass и proxy/fastcgi/scgi/
       uwsgi_no_cache были разными; ошибка появилась в 0.8.46.


Изменения в nginx 1.0.2                                           10.05.2011

    *) Добавление: теперь разделяемые зоны и кэши используют семафоры POSIX.

    *) Исправление: в работе параметра rotate директивы image_filter.
       Спасибо Adam Bocim.

    *) Исправление: nginx не собирался на Solaris; ошибка появилась в 1.0.1.


Изменения в nginx 1.0.1                                           03.05.2011

    *) Изменение: теперь директива split_clients использует алгоритм
       MurmurHash2 из-за лучшего распределения.
       Спасибо Олегу Мамонтову.

    *) Изменение: теперь длинные строки, начинающиеся с нуля, не считаются
       ложными значениями.
       Спасибо Максиму Дунину.

    *) Изменение: теперь по умолчанию nginx использует значение 511 для
       listen backlog на Linux.

    *) Добавление: переменные $upstream_... можно использовать в SSI и
       перловом модулях.

    *) Исправление: теперь nginx лучше ограничивает размер кэша на диске.
       Спасибо Олегу Мамонтову.

    *) Исправление: при парсинге неправильного IPv4 адреса мог произойти
       segmentation fault; ошибка появилась в 0.8.22.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не собирался gcc 4.6 без параметра --with-debug.

    *) Исправление: nginx не собирался на Solaris 9 и более ранних; ошибка
       появилась в 0.9.3.
       Спасибо Dagobert Michelsen.

    *) Исправление: переменная $request_time имела неверные значения, если
       использовались подзапросы; ошибка появилась в 0.8.47.
       Спасибо Игорю А. Валькову.


Изменения в nginx 1.0.0                                           12.04.2011

    *) Исправление: cache manager мог нагружать процессор после
       переконфигурации.
       Спасибо Максиму Дунину.

    *) Исправление: директива "image_filter crop" неправильно работала в
       сочетании с "image_filter rotate 180".

    *) Исправление: директива "satisfy any" запрещала выдачу
       пользовательской страницы для 401 кода.


Изменения в nginx 0.9.7                                           04.04.2011

    *) Добавление: теперь соединения в состоянии keepalive могут быть
       закрыты преждевременно, если у воркера нет свободных соединений.
       Спасибо Максиму Дунину.

    *) Добавление: параметр rotate директивы image_filter.
       Спасибо Adam Bocim.

    *) Исправление: ситуации, когда бэкенд в директивах fastcgi_pass,
       scgi_pass или uwsgi_pass задан выражением и ссылается на описанный
       upstream.


Изменения в nginx 0.9.6                                           21.03.2011

    *) Добавление: директива map поддерживает регулярные выражения в
       качестве значения первого параметра.

    *) Добавление: переменная $time_iso8601 для access_log.
       Спасибо Michael Lustfield.


Изменения в nginx 0.9.5                                           21.02.2011

    *) Изменение: теперь по умолчанию nginx использует значение -1 для
       listen backlog на Linux.
       Спасибо Андрею Нигматулину.

    *) Добавление: параметр utf8 в директивах geoip_country и geoip_city.
       Спасибо Денису Латыпову.

    *) Исправление: исправление в умолчательной директиве proxy_redirect,
       если в директиве proxy_pass не был описан URI.
       Спасибо Максиму Дунину.

    *) Исправление: директива error_page не работала с нестандартными кодами
       ошибок; ошибка появилась в 0.8.53.
       Спасибо Максиму Дунину.


Изменения в nginx 0.9.4                                           21.01.2011

    *) Добавление: директива server_name поддерживает переменную $hostname.

    *) Добавление: 494 код для ошибки "Request Header Too Large".


Изменения в nginx 0.9.3                                           13.12.2010

    *) Исправление: если для пары IPv6-адрес:порт описан только один сервер,
       то выделения в регулярных выражениях в директиве server_name не
       работали.

    *) Исправление: nginx не собирался под Solaris; ошибка появилась в
       0.9.0.


Изменения в nginx 0.9.2                                           06.12.2010

    *) Добавление: поддержка строки "If-Unmodified-Since" в заголовке
       запроса клиента.

    *) Изменение: использование accept(), если accept4() не реализован;
       ошибка появилась в 0.9.0.

    *) Исправление: nginx не собирался под Cygwin; ошибка появилась в 0.9.0.

    *) Исправление: уязвимости в OpenSSL CVE-2010-4180.
       Спасибо Максиму Дунину.


Изменения в nginx 0.9.1                                           30.11.2010

    *) Исправление: директивы вида "return CODE message" не работали; ошибка
       появилась в 0.9.0.


Изменения в nginx 0.9.0                                           29.11.2010

    *) Добавление: директива keepalive_disable.

    *) Добавление: директива map поддерживает переменные в качестве значения
       определяемой переменной.

    *) Добавление: директива map поддерживает пустые строки в качестве
       значения первого параметра.

    *) Добавление: директива map поддерживает выражения в первом параметре.

    *) Добавление: страница руководства nginx(8).
       Спасибо Сергею Осокину.

    *) Добавление: поддержка accept4() в Linux.
       Спасибо Simon Liu.

    *) Изменение: устранение предупреждения линкера о "sys_errlist" и
       "sys_nerr" под Linux; предупреждение появилось в 0.8.35.

    *) Исправление: при использовании директивы auth_basic в рабочем
       процессе мог произойти segmentation fault.
       Спасибо Михаилу Лалетину.

    *) Исправление: совместимость с модулем ngx_http_eval_module; ошибка
       появилась в 0.8.42.


Изменения в nginx 0.8.53                                          18.10.2010

    *) Добавление: теперь директива error_page позволяет менять код статуса
       у редиректа.

    *) Добавление: директива gzip_disable поддерживает специальную маску
       degradation.

    *) Исправление: при использовании файлового AIO могла происходить утечка
       сокетов.
       Спасибо Максиму Дунину.

    *) Исправление: если в первом сервере не была описана директива listen и
       нигде явно не описан сервер по умолчанию, то сервером по умолчанию
       становился следующий сервер с директивой listen; ошибка появилась в
       0.8.21.


Изменения в nginx 0.8.52                                          28.09.2010

    *) Исправление: nginx использовал режим SSL для listen сокета, если для
       него был установлен любой listen-параметр; ошибка появилась в 0.8.51.


Изменения в nginx 0.8.51                                          27.09.2010

    *) Изменение: директива secure_link_expires упразднена.

    *) Изменение: уровень логгирования ошибок resolver'а понижен с уровня
       alert на error.

    *) Добавление: теперь параметр "ssl" listen-сокета можно устанавливать
       несколько раз.


Изменения в nginx 0.8.50                                          02.09.2010

    *) Добавление: директивы secure_link, secure_link_md5 и
       secure_link_expires модуля ngx_http_secure_link_module.

    *) Добавление: ключ -q.
       Спасибо Геннадию Махомеду.

    *) Исправление: при использовании кэширования рабочие процессы и могли
       зациклиться во время переконфигурации; ошибка появилась в 0.8.48.

    *) Исправление: в директиве gzip_disable.
       Спасибо Derrick Petzold.

    *) Исправление: nginx/Windows не мог посылать сигналы stop, quit,
       reopen, reload процессу, запущенному в другой сессии.


Изменения в nginx 0.8.49                                          09.08.2010

    *) Добавление: директива image_filter_jpeg_quality поддерживает
       переменные.

    *) Исправление: при использовании переменной $geoip_region_name в
       рабочем процессе мог произойти segmentation fault; ошибка появилась в
       0.8.48.

    *) Исправление: ошибки, перехваченные error_page, кэшировались только до
       следующего запроса; ошибка появилась в 0.8.48.


Изменения в nginx 0.8.48                                          03.08.2010

    *) Изменение: теперь по умолчанию директива server_name имеет значение
       пустое имя "".
       Спасибо Геннадию Махомеду.

    *) Изменение: теперь по умолчанию директива server_name_in_redirect
       имеет значение off.

    *) Добавление: переменные $geoip_dma_code, $geoip_area_code и
       $geoip_region_name.
       Спасибо Christine McGonagle.

    *) Исправление: директивы proxy_pass, fastcgi_pass, uwsgi_pass и
       scgi_pass не наследовались в блоки limit_except.

    *) Исправление: директивы proxy_cache_min_uses, fastcgi_cache_min_uses
       uwsgi_cache_min_uses и scgi_cache_min_uses не работали; ошибка
       появилась в 0.8.46.

    *) Исправление: директива fastcgi_split_path_info неверно использовала
       выделения, если в выделения попадала только часть URI.
       Спасибо Юрию Тарадаю и Frank Enderle.

    *) Исправление: директива rewrite не экранировала символ ";" при
       копировании из URI в аргументы.
       Спасибо Daisuke Murase.

    *) Исправление: модуль ngx_http_image_filter_module закрывал соединение,
       если изображение было больше размера image_filter_buffer.


Изменения в nginx 0.8.47                                          28.07.2010

    *) Исправление: переменная $request_time имела неверные значения для
       подзапросов.

    *) Исправление: ошибки, перехваченные error_page, не кэшировались.

    *) Исправление: если использовался параметр max_size, то cache manager
       мог зациклиться; ошибка появилась в 0.8.46.


Изменения в nginx 0.8.46                                          19.07.2010

    *) Изменение: директивы proxy_no_cache, fastcgi_no_cache, uwsgi_no_cache
       и scgi_no_cache теперь влияют только на сохранение закэшированного
       ответа.

    *) Добавление: директивы proxy_cache_bypass, fastcgi_cache_bypass,
       uwsgi_cache_bypass и scgi_cache_bypass.

    *) Исправление: nginx не освобождал память в keys_zone кэшей в случае
       ошибки работы с бэкендом: память освобождалась только по истечении
       времени неактивности или при недостатке памяти.


Изменения в nginx 0.8.45                                          13.07.2010

    *) Добавление: улучшения в модуле ngx_http_xslt_filter.
       Спасибо Laurence Rowe.

    *) Исправление: ответ SSI модуля мог передаваться не полностью после
       команды include с параметром wait="yes"; ошибка появилась в 0.7.25.
       Спасибо Максиму Дунину.

    *) Исправление: директива listen не поддерживала параметр setfib=0.


Изменения в nginx 0.8.44                                          05.07.2010

    *) Изменение: теперь nginx по умолчанию не кэширует ответы бэкендов, в
       заголовке которых есть строка "Set-Cookie".

    *) Добавление: директива listen поддерживает параметр setfib.
       Спасибо Андрею Филонову.

    *) Исправление: директива sub_filter могла изменять регистр букв при
       частичном совпадении.

    *) Исправление: совместимость с HP/UX.

    *) Исправление: совместимость с компилятором AIX xlC_r.

    *) Исправление: nginx считал большие пакеты SSLv2 как обычные текстовые
       запросы.
       Спасибо Miroslaw Jaworski.


Изменения в nginx 0.8.43                                          30.06.2010

    *) Добавление: ускорение загрузки больших баз geo-диапазонов.

    *) Исправление: перенаправление ошибки в "location /zero {return 204;}"
       без изменения кода ответа оставляло тело ошибки; ошибка появилась в
       0.8.42.

    *) Исправление: nginx мог закрывать IPv6 listen сокет во время
       переконфигурации.
       Спасибо Максиму Дунину.

    *) Исправление: переменную $uid_set можно использовать на любой стадии
       обработки запроса.


Изменения в nginx 0.8.42                                          21.06.2010

    *) Изменение: теперь nginx проверяет location'ы, заданные регулярными
       выражениями, если запрос полностью совпал с location'ом, заданным
       строкой префикса. Предыдущее поведение появилось в 0.7.1.

    *) Добавление: модуль ngx_http_scgi_module.
       Спасибо Manlio Perillo.

    *) Добавление: в директиве return можно добавлять текст ответа.


Изменения в nginx 0.8.41                                          15.06.2010

    *) Безопасность: рабочий процесс nginx/Windows мог завершаться аварийно
       при запросе файла с неверной кодировкой UTF-8.

    *) Изменение: теперь nginx разрешает использовать пробелы в строке
       запроса.

    *) Исправление: директива proxy_redirect неправильно изменяла строку
       "Refresh" в заголовке ответа бэкенда.
       Спасибо Андрею Андрееву и Максиму Согину.

    *) Исправление: nginx не поддерживал путь без имени хоста в строке
       "Destination" в заголовке запроса.


Изменения в nginx 0.8.40                                          07.06.2010

    *) Безопасность: теперь nginx/Windows игнорирует имя потока файла по
       умолчанию.
       Спасибо Jose Antonio Vazquez Gonzalez.

    *) Добавление: модуль ngx_http_uwsgi_module.
       Спасибо Roberto De Ioris.

    *) Добавление: директива fastcgi_param со значением, начинающимся со
       строки "HTTP_", изменяет строку заголовка в запросе клиента.

    *) Исправление: строки "If-Modified-Since", "If-Range" и им подобные в
       заголовке запроса клиента передавались FastCGI-серверу при
       кэшировании.

    *) Исправление: listen unix domain сокет нельзя было изменить во время
       переконфигурации.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.39                                          31.05.2010

    *) Исправление: наследуемая директива alias неправильно работала во
       вложенном location'е.

    *) Исправление: в комбинации директив alias с переменными и try_files;

    *) Исправление: listen unix domain и IPv6 сокеты не наследовались во
       время обновления без перерыва.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.38                                          24.05.2010

    *) Добавление: директивы proxy_no_cache и fastcgi_no_cache.

    *) Добавление: теперь при использовании переменной $scheme в директиве
       rewrite автоматически делается редирект.
       Спасибо Piotr Sikora.

    *) Исправление: теперь задержки в директиве limit_req соответствует
       описанному алгоритму.
       Спасибо Максиму Дунину.

    *) Исправление: переменную $uid_got нельзя было использовать в SSI и
       перловом модулях.


Изменения в nginx 0.8.37                                          17.05.2010

    *) Добавление: модуль ngx_http_split_clients_module.

    *) Добавление: директива map поддерживает ключи больше 255 символов.

    *) Исправление: nginx игнорировал значения "private" и "no-store" в
       строке "Cache-Control" в заголовке ответа бэкенда.

    *) Исправление: параметр stub в SSI-директиве include не использовался,
       если пустой ответ имел код 200.

    *) Исправление: если проксированный или FastCGI запрос внутренне
       перенаправлялся в другой проксированный или FastCGI location, то в
       рабочем процессе мог произойти segmentation fault; ошибка появилась в
       0.8.33.
       Спасибо Yichun Zhang.

    *) Исправление: соединения IMAP к серверу Zimbra могло зависнуть до
       таймаута.
       Спасибо Alan Batie.


Изменения в nginx 0.8.36                                          22.04.2010

    *) Исправление: модуль ngx_http_dav_module неправильно обрабатывал
       методы DELETE, COPY и MOVE для симлинков.

    *) Исправление: модуль SSI в подзапросах использовал закэшированные в
       основном запросе значения переменных $query_string, $arg_... и им
       подобных.

    *) Исправление: значение переменной повторно экранировалось после
       каждого вывода SSI-команды echo; ошибка появилась в 0.6.14.

    *) Исправление: рабочий процесс зависал при запросе файла FIFO.
       Спасибо Vicente Aguilar и Максиму Дунину.

    *) Исправление: совместимость с OpenSSL-1.0.0 на 64-битном Linux.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не собирался с параметром --without-http-cache;
       ошибка появилась в 0.8.35.


Изменения в nginx 0.8.35                                          01.04.2010

    *) Изменение: теперь charset-фильтр работает до SSI-фильтра.

    *) Добавление: директива chunked_transfer_encoding.

    *) Исправление: символ "&" при копировании в аргументы в правилах
       rewrite не экранировался.

    *) Исправление: nginx мог завершаться аварийно во время обработки
       сигнала или при использовании директивы timer_resolution на
       платформах, не поддерживающих методы kqueue или eventport.
       Спасибо George Xie и Максиму Дунину.

    *) Исправление: если временные файлы и постоянное место хранения
       располагались на разных файловых системах, то у постоянных файлов
       время изменения было неверным.
       Спасибо Максиму Дунину.

    *) Исправление: модуль ngx_http_memcached_module мог выдавать ошибку
       "memcached sent invalid trailer".
       Спасибо Максиму Дунину.

    *) Исправление: nginx не мог собрать библиотеку zlib-1.2.4 из исходных
       текстов.
       Спасибо Максиму Дунину.

    *) Исправление: в рабочем процессе происходил segmentation fault, если
       перед ответом FastCGI-сервера было много вывода в stderr; ошибка
       появилась в 0.8.34.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.34                                          03.03.2010

    *) Исправление: nginx не поддерживал все шифры, используемые в
       клиентских сертификатах.
       Спасибо Иннокентию Еникееву.

    *) Исправление: nginx неправильно кэшировал FastCGI-ответы, если перед
       ответом было много вывода в stderr.

    *) Исправление: nginx не поддерживал HTTPS-рефереры.

    *) Исправление: nginx/Windows мог не находить файлы, если путь в
       конфигурации был задан в другом регистре; ошибка появилась в 0.8.33.

    *) Исправление: переменная $date_local выдавала неверное время, если
       использовался формат "%s".
       Спасибо Максиму Дунину.

    *) Исправление: если ssl_session_cache не был установлен или установлен
       в none, то при проверке клиентского сертификаты могла происходить
       ошибка "session id context uninitialized"; ошибка появилась в 0.7.1.

    *) Исправление: geo-диапазон возвращал значение по умолчанию, если
       диапазон включал в себя одну и более сетей размером /16 и не
       начинался на границе сети размером /16.

    *) Исправление: блок, используемый в параметре stub в SSI-директиве
       include, выводился с MIME-типом "text/plain".

    *) Исправление: $r->sleep() не работал; ошибка появилась в 0.8.11.


Изменения в nginx 0.8.33                                          01.02.2010

    *) Безопасность: теперь nginx/Windows игнорирует пробелы в конце URI.
       Спасибо Dan Crowley, Core Security Technologies.

    *) Безопасность: теперь nginx/Windows игнорирует короткие имена файлов.
       Спасибо Dan Crowley, Core Security Technologies.

    *) Изменение: теперь keepalive соединения после запросов POST не
       запрещаются для MSIE 7.0+.
       Спасибо Adam Lounds.

    *) Изменение: теперь keepalive соединения запрещены для Safari.
       Спасибо Joshua Sierles.

    *) Исправление: если проксированный или FastCGI запрос внутренне
       перенаправлялся в другой проксированный или FastCGI location, то
       переменная $upstream_response_time могла иметь ненормально большое
       значение; ошибка появилась в 0.8.7.

    *) Исправление: в рабочем процессе мог произойти segmentation fault при
       отбрасывания тела запроса; ошибка появилась в 0.8.11.


Изменения в nginx 0.8.32                                          11.01.2010

    *) Исправление: ошибки при использовании кодировки UTF-8 в
       ngx_http_autoindex_module.
       Спасибо Максиму Дунину.

    *) Исправление: именованные выделения в регулярных выражениях работали
       только для двух переменных.
       Спасибо Максиму Дунину.

    *) Исправление: теперь в строке заголовка запроса "Host" используется
       имя "localhost", если в директиве auth_http указан unix domain сокет.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не поддерживал передачу chunk'ами для 201-ых
       ответов.
       Спасибо Julian Reich.

    *) Исправление: если директива "expires modified" выставляла дату в
       прошлом, то в строке заголовка ответа "Cache-Control" выдавалось
       отрицательное число.
       Спасибо Алексею Капранову.


Изменения в nginx 0.8.31                                          23.12.2009

    *) Добавление: теперь директива error_page может перенаправлять ответы
       со статусом 301 и 302.

    *) Добавление: переменные $geoip_city_continent_code, $geoip_latitude и
       $geoip_longitude.
       Спасибо Arvind Sundararajan.

    *) Добавление: модуль ngx_http_image_filter_module теперь всегда удаляет
       EXIF и другие данные, если они занимают больше 5% в JPEG-файле.

    *) Исправление: nginx закрывал соединение при запросе закэшированного
       ответа с пустым телом.
       Спасибо Piotr Sikora.

    *) Исправление: nginx мог не собираться gcc 4.x при использовании
       оптимизации -O2 и выше.
       Спасибо Максиму Дунину и Денису Латыпову.

    *) Исправление: регулярные выражения в location всегда тестировались с
       учётом регистра; ошибка появилась в 0.8.25.

    *) Исправление: nginx кэшировал 304 ответ, если в заголовке
       проксируемого запроса была строка "If-None-Match".
       Спасибо Tim Dettrick и David Kostal.

    *) Исправление: nginx/Windows пытался дважды удалить временный файл при
       перезаписи уже существующего файла.


Изменения в nginx 0.8.30                                          15.12.2009

    *) Изменение: теперь по умолчанию размер буфера директивы
       large_client_header_buffers равен 8K.
       Спасибо Andrew Cholakian.

    *) Добавление: файл conf/fastcgi.conf для простых конфигураций FastCGI.

    *) Исправление: nginx/Windows пытался дважды переименовать временный
       файл при перезаписи уже существующего файла.

    *) Исправление: ошибки double free or corruption, возникающей, если имя
       хоста не было найдено; ошибка появилась в 0.8.22.
       Спасибо Константину Свисту.

    *) Исправление: в использовании libatomic на некоторых платформах.
       Спасибо W-Mark Kubacki.


Изменения в nginx 0.8.29                                          30.11.2009

    *) Изменение: теперь для проксируемых ответов HTTP/0.9 в лог пишется код
       ответа "009".

    *) Добавление: директивы addition_types, charset_types, gzip_types,
       ssi_types, sub_filter_types и xslt_types поддерживают параметр "*".

    *) Добавление: использование встроенных атомарных операций GCC 4.1+.
       Спасибо W-Mark Kubacki.

    *) Добавление: параметр --with-libatomic[=DIR] в configure.
       Спасибо W-Mark Kubacki.

    *) Исправление: listen unix domain сокет имели ограниченные права
       доступа.

    *) Исправление: закэшированные ответы ответов HTTP/0.9 неправильно
       обрабатывались.

    *) Исправление: именованные выделения в регулярных выражениях, заданные
       как "?P<...>", не работали в директиве server_name.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.28                                          23.11.2009

    *) Исправление: nginx не собирался с параметром --without-pcre; ошибка
       появилась в 0.8.25.


Изменения в nginx 0.8.27                                          17.11.2009

    *) Исправление: регулярные выражения не работали в nginx/Windows; ошибка
       появилась в 0.8.25.


Изменения в nginx 0.8.26                                          16.11.2009

    *) Исправление: ошибки при использовании выделений в директиве rewrite;
       ошибка появилась в 0.8.25.

    *) Исправление: nginx не собирался без параметра --with-debug; ошибка
       появилась в 0.8.25.


Изменения в nginx 0.8.25                                          16.11.2009

    *) Изменение: теперь в лог ошибок не пишется сообщение, если переменная
       не найдена с помощью метода $r->variable().

    *) Добавление: модуль ngx_http_degradation_module.

    *) Добавление: именованные выделения в регулярных выражениях.

    *) Добавление: теперь при использовании переменных в директиве
       proxy_pass не требуется задавать URI.

    *) Добавление: теперь директива msie_padding работает и для Chrome.

    *) Исправление: в рабочем процессе происходил segmentation fault при
       недостатке памяти; ошибка появилась в 0.8.18.

    *) Исправление: nginx передавал сжатые ответы клиентам, не
       поддерживающим сжатие, при настройках gzip_static on и gzip_vary off;
       ошибка появилась в 0.8.16.


Изменения в nginx 0.8.24                                          11.11.2009

    *) Исправление: nginx всегда добавлял строку "Content-Encoding: gzip" в
       заголовок 304-ых ответов модуля ngx_http_gzip_static_module.

    *) Исправление: nginx не собирался без параметра --with-debug; ошибка
       появилась в 0.8.23.

    *) Исправление: параметр "unix:" в директиве set_real_ip_from
       неправильно наследовался с предыдущего уровня.

    *) Исправление: в resolver'е при определении пустого имени.


Изменения в nginx 0.8.23                                          11.11.2009

    *) Безопасность: теперь SSL/TLS renegotiation запрещён.
       Спасибо Максиму Дунину.

    *) Исправление: listen unix domain сокет не наследовался во время
       обновления без перерыва.

    *) Исправление: параметр "unix:" в директиве set_real_ip_from не работал
       без ещё одной директивы с любым IP-адресом.

    *) Исправление: segmentation fault и зацикливания в resolver'е.

    *) Исправление: в resolver'е.
       Спасибо Артёму Бохану.


Изменения в nginx 0.8.22                                          03.11.2009

    *) Добавление: директивы proxy_bind, fastcgi_bind и memcached_bind.

    *) Добавление: директивы access и deny поддерживают IPv6.

    *) Добавление: директива set_real_ip_from поддерживает IPv6 адреса в
       заголовках запроса.

    *) Добавление: параметр "unix:" в директиве set_real_ip_from.

    *) Исправление: nginx не удалял unix domain сокет после тестирования
       конфигурации.

    *) Исправление: nginx удалял unix domain сокет во время обновления без
       перерыва.

    *) Исправление: оператор "!-x" не работал.
       Спасибо Максиму Дунину.

    *) Исправление: в рабочем процессе мог произойти segmentation fault при
       использовании limit_rate в HTTPS сервере.
       Спасибо Максиму Дунину.

    *) Исправление: при записи в лог переменной $limit_rate в рабочем
       процессе происходил segmentation fault.
       Спасибо Максиму Дунину.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если внутри блока server не было директивы listen; ошибка появилась в
       0.8.21.


Изменения в nginx 0.8.21                                          26.10.2009

    *) Добавление: теперь ключ -V показывает статус поддержки TLS SNI.

    *) Добавление: директива listen модуля HTTP поддерживает unix domain
       сокеты.
       Спасибо Hongli Lai.

    *) Добавление: параметр "default_server" в директиве listen.

    *) Добавление: теперь параметр "default" не обязателен для установки
       параметров listen-сокета.

    *) Исправление: nginx не поддерживал даты в 2038 году на 32-битных
       платформах;

    *) Исправление: утечки сокетов; ошибка появилась в 0.8.11.


Изменения в nginx 0.8.20                                          14.10.2009

    *) Изменение: теперь по умолчанию используются следующие шифры SSL:
       "HIGH:!ADH:!MD5".

    *) Исправление: модуль ngx_http_autoindex_module не показывал последний
       слэш для линков на каталоги; ошибка появилась в 0.7.15.

    *) Исправление: nginx не закрывал лог, заданный параметром конфигурации
       --error-log-path; ошибка появилась в 0.7.53.

    *) Исправление: nginx не считал запятую разделителем в строке
       "Cache-Control" в заголовке ответа бэкенда.

    *) Исправление: nginx/Windows мог не создать временный файл, файл в кэше
       или файл с помощью директив proxy/fastcgi_store, если рабочий процесс
       не имел достаточно прав для работы с каталогами верхнего уровня.

    *) Исправление: строки "Set-Cookie" и "P3P" в заголовке ответа
       FastCGI-сервера не скрывались при кэшировании, если не использовались
       директивы fastcgi_hide_header с любыми параметрами.

    *) Исправление: nginx неверно считал размер кэша на диске.


Изменения в nginx 0.8.19                                          06.10.2009

    *) Изменение: теперь протокол SSLv2 по умолчанию запрещён.

    *) Изменение: теперь по умолчанию используются следующие шифры SSL:
       "ALL:!ADH:RC4+RSA:+HIGH:+MEDIUM".

    *) Исправление: директива limit_req не работала; ошибка появилась в
       0.8.18.


Изменения в nginx 0.8.18                                          06.10.2009

    *) Добавление: директива read_ahead.

    *) Добавление: теперь можно использовать несколько директив
       perl_modules.

    *) Добавление: директивы limit_req_log_level и limit_conn_log_level.

    *) Исправление: теперь директива limit_req соответствует алгоритму leaky
       bucket.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не работал на Linux/sparc.
       Спасибо Marcus Ramberg.

    *) Исправление: nginx слал символ '\0' в строке "Location" в заголовке в
       ответе на запрос MKCOL.
       Спасибо Xie Zhenye.

    *) Исправление: вместо кода ответа 499 в лог записывался код 0; ошибка
       появилась в 0.8.11.

    *) Исправление: утечки сокетов; ошибка появилась в 0.8.11.


Изменения в nginx 0.8.17                                          28.09.2009

    *) Безопасность: теперь символы "/../" запрещены в строке "Destination"
       в заголовке запроса.

    *) Изменение: теперь значение переменной $host всегда в нижнем регистре.

    *) Добавление: переменная $ssl_session_id.

    *) Исправление: утечки сокетов; ошибка появилась в 0.8.11.


Изменения в nginx 0.8.16                                          22.09.2009

    *) Добавление: директива image_filter_transparency.

    *) Исправление: директива "addition_types" была неверно названа
       "addtion_types".

    *) Исправление: порчи кэша resolver'а.
       Спасибо Matthew Dempsky.

    *) Исправление: утечки памяти в resolver'е.
       Спасибо Matthew Dempsky.

    *) Исправление: неверная строка запроса в переменной $request
       записывалась в access_log только при использовании error_log на
       уровне info или debug.

    *) Исправление: в поддержке альфа-канала PNG в модуле
       ngx_http_image_filter_module.

    *) Исправление: nginx всегда добавлял строку "Vary: Accept-Encoding" в
       заголовок ответа, если обе директивы gzip_static и gzip_vary были
       включены.

    *) Исправление: в поддержке кодировки UTF-8 директивой try_files в
       nginx/Windows.

    *) Исправление: ошибки при использовании post_action; ошибка появилась в
       0.8.11.
       Спасибо Игорю Артемьеву.


Изменения в nginx 0.8.15                                          14.09.2009

    *) Безопасность: при обработке специально созданного запроса в рабочем
       процессе мог произойти segmentation fault.
       Спасибо Chris Ries.

    *) Исправление: если были описаны имена .domain.tld, .sub.domain.tld и
       .domain-some.tld, то имя .sub.domain.tld попадало под маску
       .domain.tld.

    *) Исправление: в поддержке прозрачности в модуле
       ngx_http_image_filter_module.

    *) Исправление: в файловом AIO.

    *) Исправление: ошибки при использовании X-Accel-Redirect; ошибка
       появилась в 0.8.11.

    *) Исправление: ошибки при использовании встроенного перла; ошибка
       появилась в 0.8.11.


Изменения в nginx 0.8.14                                          07.09.2009

    *) Исправление: устаревший закэшированный запрос мог залипнуть в
       состоянии "UPDATING".

    *) Исправление: при использовании error_log на уровне info или debug в
       рабочем процессе мог произойти segmentation fault.
       Спасибо Сергею Боченкову.

    *) Исправление: ошибки при использовании встроенного перла; ошибка
       появилась в 0.8.11.

    *) Исправление: директива error_page не перенаправляла ошибку 413;
       ошибка появилась в 0.6.10.


Изменения в nginx 0.8.13                                          31.08.2009

    *) Исправление: в директиве "aio sendfile"; ошибка появилась в 0.8.12.

    *) Исправление: nginx не собирался без параметра --with-file-aio на
       FreeBSD; ошибка появилась в 0.8.12.


Изменения в nginx 0.8.12                                          31.08.2009

    *) Добавление: параметр sendfile в директиве aio во FreeBSD.

    *) Исправление: ошибки при использовании try_files; ошибка появилась в
       0.8.11.

    *) Исправление: ошибки при использовании memcached; ошибка появилась в
       0.8.11.


Изменения в nginx 0.8.11                                          28.08.2009

    *) Изменение: теперь директива "gzip_disable msie6" не запрещает сжатие
       для MSIE 6.0 SV1.

    *) Добавление: поддержка файлового AIO во FreeBSD и Linux.

    *) Добавление: директива directio_alignment.


Изменения в nginx 0.8.10                                          24.08.2009

    *) Исправление: утечек памяти при использовании базы GeoIP City.

    *) Исправление: ошибки при копировании временных файлов в постоянное
       место хранения; ошибка появилась в 0.8.9.


Изменения в nginx 0.8.9                                           17.08.2009

    *) Добавление: теперь стартовый загрузчик кэша работает в отдельном
       процесс; это должно улучшить обработку больших кэшей.

    *) Добавление: теперь временные файлы и постоянное место хранения могут
       располагаться на разных файловых системах.


Изменения в nginx 0.8.8                                           10.08.2009

    *) Исправление: в обработке заголовков ответа, разделённых в
       FastCGI-записях.

    *) Исправление: если запрос обрабатывался в двух проксированных или
       FastCGI location'ах и в первом из них использовалось кэширование, то
       в рабочем процессе происходил segmentation fault; ошибка появилась в
       0.8.7.


Изменения в nginx 0.8.7                                           27.07.2009

    *) Изменение: минимальная поддерживаемая версия OpenSSL - 0.9.7.

    *) Изменение: параметр ask директивы ssl_verify_client изменён на
       параметр optional и теперь он проверяет клиентский сертификат, если
       он был предложен.
       Спасибо Brice Figureau.

    *) Добавление: переменная $ssl_client_verify.
       Спасибо Brice Figureau.

    *) Добавление: директива ssl_crl.
       Спасибо Brice Figureau.

    *) Добавление: параметр proxy директивы geo.

    *) Добавление: директива image_filter поддерживает переменные для
       задания размеров.

    *) Исправление: использование переменной $ssl_client_cert портило
       память; ошибка появилась в 0.7.7.
       Спасибо Сергею Журавлёву.

    *) Исправление: директивы proxy_pass_header и fastcgi_pass_header" не
       передавали клиенту строки "X-Accel-Redirect", "X-Accel-Limit-Rate",
       "X-Accel-Buffering" и "X-Accel-Charset" из заголовка ответа бэкенда.
       Спасибо Максиму Дунину.

    *) Исправление: в обработке строк "Last-Modified" и "Accept-Ranges" в
       заголовке ответа бэкенда; ошибка появилась в 0.7.44.
       Спасибо Максиму Дунину.

    *) Исправление: ошибки "[alert] zero size buf" при получении пустых
       ответы в подзапросах; ошибка появилась в 0.8.5.


Изменения в nginx 0.8.6                                           20.07.2009

    *) Добавление: модуль ngx_http_geoip_module.

    *) Исправление: XSLT-фильтр мог выдавать ошибку "not well formed XML
       document" для правильного документа.
       Спасибо Kuramoto Eiji.

    *) Исправление: в MacOSX, Cygwin и nginx/Windows при проверке
       location'ов, заданных регулярным выражением, теперь всегда делается
       сравнение без учёта регистра символов.

    *) Исправление: теперь nginx/Windows игнорирует точки в конце URI.
       Спасибо Hugo Leisink.

    *) Исправление: имя файла указанного в --conf-path игнорировалось при
       установке; ошибка появилась в 0.6.6.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.5                                           13.07.2009

    *) Исправление: теперь nginx разрешает подчёркивания в методе запроса.

    *) Исправление: при использовании HTTP Basic-аутентификации на Windows
       для неверных имени/пароля возвращалась 500-ая ошибка.

    *) Исправление: ответы модуля ngx_http_perl_module не работали в
       подзапросах.

    *) Исправление: в модуле ngx_http_limit_req_module.
       Спасибо Максиму Дунину.


Изменения в nginx 0.8.4                                           22.06.2009

    *) Исправление: nginx не собирался с параметром --without-http-cache;
       ошибка появилась в 0.8.3.


Изменения в nginx 0.8.3                                           19.06.2009

    *) Добавление: переменная $upstream_cache_status.

    *) Исправление: nginx не собирался на MacOSX 10.6.

    *) Исправление: nginx не собирался с параметром --without-http-cache;
       ошибка появилась в 0.8.2.

    *) Исправление: если использовался перехват 401 ошибки от бэкенда и
       бэкенд не возвращал строку "WWW-Authenticate" в заголовке ответа, то
       в рабочем процессе происходил segmentation fault.
       Спасибо Евгению Мычло.


Изменения в nginx 0.8.2                                           15.06.2009

    *) Исправление: во взаимодействии open_file_cache и proxy/fastcgi кэша
       на старте.

    *) Исправление: open_file_cache мог кэшировать открытые файлы очень
       долго; ошибка появилась в 0.7.4.


Изменения в nginx 0.8.1                                           08.06.2009

    *) Добавление: параметр updating в директивах proxy_cache_use_stale и
       fastcgi_cache_use_stale.

    *) Исправление: строки "If-Modified-Since", "If-Range" и им подобные в
       заголовке запроса клиента передавались бэкенду при кэшировании, если
       не использовалась директива proxy_set_header с любыми параметрами.

    *) Исправление: строки "Set-Cookie" и "P3P" в заголовке ответа бэкенда
       не скрывались при кэшировании, если не использовались директивы
       proxy_hide_header/fastcgi_hide_header с любыми параметрами.

    *) Исправление: модуль ngx_http_image_filter_module не понимал формат
       GIF87a.
       Спасибо Денису Ильиных.

    *) Исправление: nginx не собирался на Solaris 10 и более ранних; ошибка
       появилась в 0.7.56.


Изменения в nginx 0.8.0                                           02.06.2009

    *) Добавление: директива keepalive_requests.

    *) Добавление: директива limit_rate_after.
       Спасибо Ivan Debnar.

    *) Исправление: XSLT-фильтр не работал в подзапросах.

    *) Исправление: обработке относительных путей в nginx/Windows.

    *) Исправление: в proxy_store, fastcgi_store, proxy_cache и
       fastcgi_cache в nginx/Windows.

    *) Исправление: в обработке ошибок выделения памяти.
       Спасибо Максиму Дунину и Кириллу Коринскому.


Изменения в nginx 0.7.59                                          25.05.2009

    *) Добавление: директивы proxy_cache_methods и fastcgi_cache_methods.

    *) Исправление: утечки сокетов; ошибка появилась в 0.7.25.
       Спасибо Максиму Дунину.

    *) Исправление: при использовании переменной $request_body в рабочем
       процессе происходил segmentation fault, если в запросе не было тела;
       ошибка появилась в 0.7.58.

    *) Исправление: SSL-модули могли не собираться на Solaris и Linux;
       ошибка появилась в 0.7.56.

    *) Исправление: ответы модуля ngx_http_xslt_filter_module не
       обрабатывались SSI-, charset- и gzip-фильтрами.

    *) Исправление: директива charset не ставила кодировку для ответов
       модуля ngx_http_gzip_static_module.


Изменения в nginx 0.7.58                                          18.05.2009

    *) Добавление: директива listen почтового прокси-сервера поддерживает
       IPv6.

    *) Добавление: директива image_filter_jpeg_quality.

    *) Добавление: директива client_body_in_single_buffer.

    *) Добавление: переменная $request_body.

    *) Исправление: в модуле ngx_http_autoindex_module в ссылках на имена
       файлов, содержащих символ ":".

    *) Исправление: процедура "make upgrade" не работала; ошибка появилась в
       0.7.53.
       Спасибо Денису Латыпову.


Изменения в nginx 0.7.57                                          12.05.2009

    *) Исправление: при перенаправлении ошибок модуля
       ngx_http_image_filter_module в именованный location в рабочем
       процессе происходил floating-point fault; ошибка появилась в 0.7.56.


Изменения в nginx 0.7.56                                          11.05.2009

    *) Добавление: nginx/Windows поддерживает IPv6 в директиве listen модуля
       HTTP.

    *) Исправление: в модуле ngx_http_image_filter_module.


Изменения в nginx 0.7.55                                          06.05.2009

    *) Исправление: параметры http_XXX в директивах proxy_cache_use_stale и
       fastcgi_cache_use_stale не работали.

    *) Исправление: fastcgi кэш не кэшировал ответы, состоящие только из
       заголовка.

    *) Исправление: ошибки "select() failed (9: Bad file descriptor)" в
       nginx/Unix и "select() failed (10038: ...)" в nginx/Windows.

    *) Исправление: при использовании директивы debug_connection в рабочем
       процессе мог произойти segmentation fault; ошибка появилась в 0.7.54.

    *) Исправление: в сборке модуля ngx_http_image_filter_module.

    *) Исправление: файлы больше 2G не передавались с использованием
       $r->sendfile.
       Спасибо Максиму Дунину.


Изменения в nginx 0.7.54                                          01.05.2009

    *) Добавление: модуль ngx_http_image_filter_module.

    *) Добавление: директивы proxy_ignore_headers и fastcgi_ignore_headers.

    *) Исправление: при использовании переменных "open_file_cache_errors on"
       в рабочем процессе мог произойти segmentation fault; ошибка появилась
       в 0.7.53.

    *) Исправление: директива "port_in_redirect off" не работала; ошибка
       появилась в 0.7.39.

    *) Исправление: улучшение обработки ошибок метода select.

    *) Исправление: ошибки "select() failed (10022: ...)" в nginx/Windows.

    *) Исправление: в текстовых сообщениях об ошибках в nginx/Windows;
       ошибка появилась в 0.7.53.


Изменения в nginx 0.7.53                                          27.04.2009

    *) Изменение: теперь лог, указанный в --error-log-path, создаётся с
       самого начала работы.

    *) Добавление: теперь ошибки и предупреждения при старте записываются в
       error_log и выводятся на stderr.

    *) Добавление: при сборке с пустым параметром --prefix= nginx использует
       как префикс каталог, в котором он был запущен.

    *) Добавление: ключ -p.

    *) Добавление: ключ -s на Unix-платформах.

    *) Добавление: ключи -? и -h.
       Спасибо Jerome Loyet.

    *) Добавление: теперь ключи можно задавать в сжатой форме.

    *) Исправление: nginx/Windows не работал, если файл конфигурации был
       задан ключом -c.

    *) Исправление: при использовании директив proxy_store, fastcgi_store,
       proxy_cache или fastcgi_cache временные файлы могли не удаляться.
       Спасибо Максиму Дунину.

    *) Исправление: в заголовке Auth-Method запроса серверу аутентификации
       почтового прокси-сервера передавалось неверное значение; ошибка
       появилась в 0.7.34.
       Спасибо Simon Lecaille.

    *) Исправление: при логгировании на Linux не писались текстовые описания
       системных ошибок; ошибка появилась в 0.7.45.

    *) Исправление: директива fastcgi_cache_min_uses не работала.
       Спасибо Андрею Воробьёву.


Изменения в nginx 0.7.52                                          20.04.2009

    *) Добавление: первая бинарная версия под Windows.

    *) Исправление: корректная обработка метода HEAD при кэшировании.

    *) Исправление: корректная обработка строк "If-Modified-Since",
       "If-Range" и им подобных в заголовке запроса клиента при кэшировании.

    *) Исправление: теперь строки "Set-Cookie" и "P3P" скрываются в
       заголовке ответа для закэшированных ответов.

    *) Исправление: если nginx был собран с модулем ngx_http_perl_module и
       perl поддерживал потоки, то при выходе основного процесса могла
       выдаваться ошибка "panic: MUTEX_LOCK".

    *) Исправление: nginx не собирался с параметром --without-http-cache;
       ошибка появилась в 0.7.48.

    *) Исправление: nginx не собирался на платформах, отличных от i386,
       amd64, sparc и ppc; ошибка появилась в 0.7.42.


Изменения в nginx 0.7.51                                          12.04.2009

    *) Добавление: директива try_files поддерживает код ответа в последнем
       параметре.

    *) Добавление: теперь в директиве return можно использовать любой код
       ответа.

    *) Исправление: директива error_page делала внешний редирект без строки
       запроса; ошибка появилась в 0.7.44.

    *) Исправление: если сервера слушали на нескольких явно описанных
       адресах, то виртуальные сервера могли не работать; ошибка появилась в
       0.7.39.


Изменения в nginx 0.7.50                                          06.04.2009

    *) Исправление: переменные $arg_... не работали; ошибка появилась в
       0.7.49.


Изменения в nginx 0.7.49                                          06.04.2009

    *) Исправление: при использовании переменных $arg_... в рабочем процессе
       мог произойти segmentation fault; ошибка появилась в 0.7.48.


Изменения в nginx 0.7.48                                          06.04.2009

    *) Добавление: директива proxy_cache_key.

    *) Исправление: теперь nginx учитывает при кэшировании строки
       "X-Accel-Expires", "Expires" и "Cache-Control" в заголовке ответа
       бэкенда.

    *) Исправление: теперь nginx кэширует только ответы на запросы GET.

    *) Исправление: директива fastcgi_cache_key не наследовалась.

    *) Исправление: переменные $arg_... не работали с SSI-подзапросами.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не собирался с библиотекой uclibc.
       Спасибо Timothy Redaelli.

    *) Исправление: nginx не собирался на OpenBSD; ошибка появилась
       в 0.7.46.


Изменения в nginx 0.7.47                                          01.04.2009

    *) Исправление: nginx не собирался на FreeBSD 6 и более ранних версиях;
       ошибка появилась в 0.7.46.

    *) Исправление: nginx не собирался на MacOSX; ошибка появилась в 0.7.46.

    *) Исправление: если использовался параметр max_size, то cache manager
       мог удалить весь кэш; ошибка появилась в 0.7.46.

    *) Изменение: в рабочем процессе мог произойти segmentation fault, если
       директивы proxy_cache/fastcgi_cache и proxy_cache_valid/
       fastcgi_cache_valid не были заданы на одном уровне; ошибка появилась
       в 0.7.46.

    *) Исправление: в рабочем процессе мог произойти segmentation fault при
       перенаправлении запроса проксированному или FastCGI-серверу с помощью
       error_page или try_files; ошибка появилась в 0.7.44.


Изменения в nginx 0.7.46                                          30.03.2009

    *) Исправление: архив предыдущего релиза был неверным.


Изменения в nginx 0.7.45                                          30.03.2009

    *) Изменение: теперь директивы proxy_cache и proxy_cache_valid можно
       задавать на разных уровнях.

    *) Изменение: параметр clean_time в директиве proxy_cache_path удалён.

    *) Добавление: параметр max_size в директиве proxy_cache_path.

    *) Добавление: предварительная поддержка кэширования в модуле
       ngx_http_fastcgi_module.

    *) Добавление: теперь при ошибках выделения в разделяемой памяти в логе
       указываются названия директивы и зоны.

    *) Исправление: директива "add_header last-modified ''" не удаляла в
       заголовке ответа строку "Last-Modified"; ошибка появилась в 0.7.44.

    *) Исправление: в директиве auth_basic_user_file не работал
       относительный путь, заданный строкой без переменных; ошибка появилась
       в 0.7.44.
       Спасибо Jerome Loyet.

    *) Исправление: в директиве alias, заданной переменными без ссылок на
       выделения в регулярных выражениях; ошибка появилась в 0.7.42.


Изменения в nginx 0.7.44                                          23.03.2009

    *) Добавление: предварительная поддержка кэширования в модуле
       ngx_http_proxy_module.

    *) Добавление: параметр --with-pcre в configure.

    *) Добавление: теперь директива try_files может быть использована на
       уровне server.

    *) Исправление: директива try_files неправильно обрабатывала строку
       запроса в последнем параметре.

    *) Исправление: директива try_files могла неверно тестировать каталоги.

    *) Исправление: если для пары адрес:порт описан только один сервер, то
       выделения в регулярных выражениях в директиве server_name не
       работали.


Изменения в nginx 0.7.43                                          18.03.2009

    *) Исправление: запрос обрабатывался неверно, если директива root
       использовала переменные; ошибка появилась в 0.7.42.

    *) Исправление: если сервер слушал на адресах типа "*", то значение
       переменной $server_addr было "0.0.0.0"; ошибка появилась в 0.7.36.


Изменения в nginx 0.7.42                                          16.03.2009

    *) Изменение: ошибка "Invalid argument", возвращаемая
       setsockopt(TCP_NODELAY) на Solaris, теперь игнорируется.

    *) Изменение: при отсутствии файла, указанного в директиве
       auth_basic_user_file, теперь возвращается ошибка 403 вместо 500.

    *) Добавление: директива auth_basic_user_file поддерживает переменные.
       Спасибо Кириллу Коринскому.

    *) Добавление: директива listen поддерживает параметр ipv6only.
       Спасибо Zhang Hua.

    *) Исправление: в директиве alias со ссылками на выделения в регулярных
       выражениях; ошибка появилась в 0.7.40.

    *) Исправление: совместимость с Tru64 UNIX.
       Спасибо Dustin Marquess.

    *) Исправление: nginx не собирался без библиотеки PCRE; ошибка появилась
       в 0.7.41.


Изменения в nginx 0.7.41                                          11.03.2009

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если в server_name или location были выделения в регулярных
       выражениях; ошибка появилась в 0.7.40.
       Спасибо Владимиру Сопоту.


Изменения в nginx 0.7.40                                          09.03.2009

    *) Добавление: директива location поддерживает выделения в регулярных
       выражениях.

    *) Добавление: директиву alias с ссылками на выделения в регулярных
       выражениях можно использовать внутри location'а, заданного регулярным
       выражением с выделениями.

    *) Добавление: директива server_name поддерживает выделения в регулярных
       выражениях.

    *) Изменение: модуль ngx_http_autoindex_module не показывал последний
       слэш для каталогов на файловой системе XFS; ошибка появилась в
       0.7.15.
       Спасибо Дмитрию Кузьменко.


Изменения в nginx 0.7.39                                          02.03.2009

    *) Исправление: при включённом сжатии большие ответы с использованием
       SSI могли зависать; ошибка появилась в 0.7.28.
       Спасибо Артёму Бохану.

    *) Исправление: при использовании коротких статических вариантов в
       директиве try_files в рабочем процессе мог произойти segmentation
       fault.


Изменения в nginx 0.7.38                                          23.02.2009

    *) Добавление: логгирование ошибок аутентификации.

    *) Исправление: имя/пароль, заданные в auth_basic_user_file,
       игнорировались после нечётного числа пустых строк.
       Спасибо Александру Загребину.

    *) Исправление: при использовании длинного пути в unix domain сокете в
       главном процессе происходил segmentation fault; ошибка появилась в
       0.7.36.


Изменения в nginx 0.7.37                                          21.02.2009

    *) Исправление: директивы, использующие upstream'ы, не работали; ошибка
       появилась в 0.7.36.


Изменения в nginx 0.7.36                                          21.02.2009

    *) Добавление: предварительная поддержка IPv6; директива listen модуля
       HTTP поддерживает IPv6.

    *) Исправление: переменная $ancient_browser не работала для браузеров,
       заданных директивами modern_browser.


Изменения в nginx 0.7.35                                          16.02.2009

    *) Исправление: директива ssl_engine не использовала SSL-акселератор для
       асимметричных шифров.
       Спасибо Marcin Gozdalik.

    *) Исправление: директива try_files выставляла MIME-type, исходя из
       расширения первоначального запроса.

    *) Исправление: в директивах server_name, valid_referers и map
       неправильно обрабатывались имена вида "*domain.tld", если
       использовались маски вида ".domain.tld" и ".subdomain.domain.tld";
       ошибка появилась в 0.7.9.


Изменения в nginx 0.7.34                                          10.02.2009

    *) Добавление: параметр off в директиве if_modified_since.

    *) Добавление: теперь после команды XCLIENT nginx посылает команду
       HELO/EHLO.
       Спасибо Максиму Дунину.

    *) Добавление: поддержка Microsoft-специфичного режима
       "AUTH LOGIN with User Name" в почтовом прокси-сервере.
       Спасибо Максиму Дунину.

    *) Исправление: в директиве rewrite, возвращающей редирект, старые
       аргументы присоединялись к новым через символ "?" вместо "&";
       ошибка появилась в 0.1.18.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не собирался на AIX.


Изменения в nginx 0.7.33                                          02.02.2009

    *) Исправление: если на запрос с телом возвращался редирект, то ответ
       мог быть двойным при использовании методов epoll или rtsig.
       Спасибо Eden Li.

    *) Исправление: для некоторых типов редиректов в переменной
       $sent_http_location было пустое значение.

    *) Исправление: при использовании директивы resolver в SMTP
       прокси-сервере в рабочем процессе мог произойти segmentation fault.


Изменения в nginx 0.7.32                                          26.01.2009

    *) Добавление: теперь в директиве try_files можно явно указать проверку
       каталога.

    *) Исправление: fastcgi_store не всегда сохранял файлы.

    *) Исправление: в гео-диапазонах.

    *) Исправление: ошибки выделения больших блоков в разделяемой памяти,
       если nginx был собран без отладки.
       Спасибо Андрею Квасову.


Изменения в nginx 0.7.31                                          19.01.2009

    *) Изменение: теперь директива try_files проверяет только файлы,
       игнорируя каталоги.

    *) Добавление: директива fastcgi_split_path_info.

    *) Исправления в поддержке строки "Expect" в заголовке запроса.

    *) Исправления в гео-диапазонах.

    *) Исправление: при отсутствии ответа ngx_http_memcached_module
       возвращал в теле ответа строку "END" вместо 404-ой страницы по
       умолчанию; ошибка появилась в 0.7.18.
       Спасибо Максиму Дунину.

    *) Исправление: при проксировании SMTP nginx выдавал сообщение
       "250 2.0.0 OK" вместо "235 2.0.0 OK"; ошибка появилась в 0.7.22.
       Спасибо Максиму Дунину.


Изменения в nginx 0.7.30                                          24.12.2008

    *) Исправление: в рабочем процессе происходил segmentation fault, если в
       директивах fastcgi_pass или proxy_pass использовались переменные и
       имя хоста должно было резолвиться; ошибка появилась в 0.7.29.


Изменения в nginx 0.7.29                                          24.12.2008

    *) Исправление: директивы fastcgi_pass и proxy_pass не поддерживали
       переменные при использовании unix domain сокетов.

    *) Исправления в обработке подзапросов; ошибки появились в 0.7.25.

    *) Исправление: ответ "100 Continue" выдавался для запросов версии
       HTTP/1.0;
       Спасибо Максиму Дунину.

    *) Исправление: в выделении памяти в модуле ngx_http_gzip_filter_module
       под Cygwin.


Изменения в nginx 0.7.28                                          22.12.2008

    *) Изменение: в выделении памяти в модуле ngx_http_gzip_filter_module.

    *) Изменение: значения по умолчанию для директивы gzip_buffers изменены
       с 4 4k/8k на 32 4k или 16 8k.


Изменения в nginx 0.7.27                                          15.12.2008

    *) Добавление: директива try_files.

    *) Добавление: директива fastcgi_pass поддерживает переменные.

    *) Добавление: теперь директива geo может брать адрес из переменной.
       Спасибо Андрею Нигматулину.

    *) Добавление: теперь модификатор location'а можно указывать без пробела
       перед названием.

    *) Добавление: переменная $upstream_response_length.

    *) Исправление: теперь директива add_header не добавляет пустое
       значение.

    *) Исправление: при запросе файла нулевой длины nginx закрывал
       соединение, ничего не передав; ошибка появилась в 0.7.25.

    *) Исправление: метод MOVE не мог перемещать файл в несуществующий
       каталог.

    *) Исправление: если в сервере не был описан ни один именованный
       location, но такой location использовался в директиве error_page, то
       в рабочем процессе происходил segmentation fault.
       Спасибо Сергею Боченкову.


Изменения в nginx 0.7.26                                          08.12.2008

    *) Исправление: в обработке подзапросов; ошибка появилась в 0.7.25.


Изменения в nginx 0.7.25                                          08.12.2008

    *) Изменение: в обработке подзапросов.

    *) Изменение: теперь разрешаются POST'ы без строки "Content-Length" в
       заголовке запроса.

    *) Исправление: теперь директивы limit_req и limit_conn указывают
       причину запрета запроса.

    *) Исправление: в параметре delete директивы geo.


Изменения в nginx 0.7.24                                          01.12.2008

    *) Добавление: директива if_modified_since.

    *) Исправление: nginx не обрабатывал ответ FastCGI-сервера, если перед
       ответом сервер передавал много сообщений в stderr.

    *) Исправление: переменные "$cookie_..." не работали в SSI and в
       перловом модуле.


Изменения в nginx 0.7.23                                          27.11.2008

    *) Добавление: параметры delete и ranges в директиве geo.

    *) Добавление: ускорение загрузки geo-базы с большим числом значений.

    *) Добавление: уменьшение памяти, необходимой для загрузки geo-базы.


Изменения в nginx 0.7.22                                          20.11.2008

    *) Добавление: параметр none в директиве smtp_auth.
       Спасибо Максиму Дунину.

    *) Добавление: переменные "$cookie_...".

    *) Исправление: директива directio не работала с файловой системой XFS.

    *) Исправление: resolver не понимал большие DNS-ответы.
       Спасибо Zyb.


Изменения в nginx 0.7.21                                          11.11.2008

    *) Изменения в модуле ngx_http_limit_req_module.

    *) Добавление: поддержка EXSLT в модуле ngx_http_xslt_module.
       Спасибо Денису Латыпову.

    *) Изменение: совместимость с glibc 2.3.
       Спасибо Eric Benson и Максиму Дунину.

    *) Исправление: nginx не запускался на MacOSX 10.4 и более ранних;
       ошибка появилась в 0.7.6.


Изменения в nginx 0.7.20                                          10.11.2008

    *) Изменения в модуле ngx_http_gzip_filter_module.

    *) Добавление: модуль ngx_http_limit_req_module.

    *) Исправление: на платформах sparc и ppc рабочие процессы могли
       выходить по сигналу SIGBUS; ошибка появилась в 0.7.3.
       Спасибо Максиму Дунину.

    *) Исправление: директивы вида "proxy_pass http://host/some:uri" не
       работали; ошибка появилась в 0.7.12.

    *) Исправление: при использовании HTTPS запросы могли завершаться с
       ошибкой "bad write retry".

    *) Исправление: модуль ngx_http_secure_link_module не работал внутри
       location'ов с именами меньше 3 символов.

    *) Исправление: переменная $server_addr могла не иметь значения.


Изменения в nginx 0.7.19                                          13.10.2008

    *) Исправление: обновление номера версии.


Изменения в nginx 0.7.18                                          13.10.2008

    *) Изменение: директива underscores_in_headers; теперь nginx по
       умолчанию не разрешает подчёркивания в именах строк в заголовке
       запроса клиента.

    *) Добавление: модуль ngx_http_secure_link_module.

    *) Добавление: директива real_ip_header поддерживает любой заголовок.

    *) Добавление: директива log_subrequest.

    *) Добавление: переменная $realpath_root.

    *) Добавление: параметры http_502 и http_504 в директиве
       proxy_next_upstream.

    *) Исправление: параметр http_503 в директивах proxy_next_upstream или
       fastcgi_next_upstream не работал.

    *) Исправление: nginx мог выдавать строку "Transfer-Encoding: chunked"
       для запросов HEAD.

    *) Исправление: теперь accept-лимит зависит от числа worker_connections.


Изменения в nginx 0.7.17                                          15.09.2008

    *) Добавление: директива directio теперь работает на Linux.

    *) Добавление: переменная $pid.

    *) Исправление: оптимизация directio, появившаяся в 0.7.15, не работала
       при использовании open_file_cache.

    *) Исправление: access_log с переменными не работал на Linux; ошибка
       появилась в 0.7.7.

    *) Исправление: модуль ngx_http_charset_module не понимал название
       кодировки в кавычках, полученное от бэкенда.


Изменения в nginx 0.7.16                                          08.09.2008

    *) Исправление: nginx не собирался на 64-битных платформах; ошибка
       появилась в 0.7.15.


Изменения в nginx 0.7.15                                          08.09.2008

    *) Добавление: модуль ngx_http_random_index_module.

    *) Добавление: директива directio оптимизирована для запросов файлов,
       начинающихся с произвольной позиции.

    *) Добавление: директива directio при необходимости запрещает
       использование sendfile.

    *) Добавление: теперь nginx разрешает подчёркивания в именах строк в
       заголовке запроса клиента.


Изменения в nginx 0.7.14                                          01.09.2008

    *) Изменение: теперь директивы ssl_certificate и ssl_certificate_key не
       имеют значений по умолчанию.

    *) Добавление: директива listen поддерживает параметр ssl.

    *) Добавление: теперь при переконфигурации nginx учитывает изменение
       временной зоны на FreeBSD и Linux.

    *) Исправление: параметры директивы listen, такие как backlog, rcvbuf и
       прочие, не устанавливались, если сервером по умолчанию был не первый
       сервер.

    *) Исправление: при использовании в качестве аргументов части URI,
       выделенного с помощью директивы rewrite, эти аргументы не
       экранировались.

    *) Исправление: улучшения тестирования правильности конфигурационного
       файла.


Изменения в nginx 0.7.13                                          26.08.2008

    *) Исправление: nginx не собирался на Linux и Solaris; ошибка появилась
       в 0.7.12.


Изменения в nginx 0.7.12                                          26.08.2008

    *) Добавление: директива server_name поддерживает пустое имя "".

    *) Добавление: директива gzip_disable поддерживает специальную маску
       msie6.

    *) Исправление: при использовании параметра max_fails=0 в upstream'е с
       несколькими серверами рабочий процесс выходил по сигналу SIGFPE.
       Спасибо Максиму Дунину.

    *) Исправление: при перенаправлении запроса с помощью директивы
       error_page терялось тело запроса.

    *) Исправление: при перенаправлении запроса с методом HEAD с помощью
       директивы error_page возвращался полный ответ.

    *) Исправление: метод $r->header_in() не возвращал значения строк
       "Host", "User-Agent", и "Connection" из заголовка запроса; ошибка
       появилась в 0.7.0.


Изменения в nginx 0.7.11                                          18.08.2008

    *) Изменение: теперь ngx_http_charset_module по умолчанию не работает
       MIME-типом text/css.

    *) Добавление: теперь nginx возвращает код 405 для метода POST при
       запросе статического файла, только если файл существует.

    *) Добавление: директива proxy_ssl_session_reuse.

    *) Исправление: после перенаправления запроса с помощью
       "X-Accel-Redirect" директива proxy_pass без URI могла использовать
       оригинальный запрос.

    *) Исправление: если у каталога были права доступа только на поиск
       файлов и первый индексный файл отсутствовал, то nginx возвращал
       ошибку 500.

    *) Исправление: ошибок во вложенных location'ах; ошибки появились в
       0.7.1.


Изменения в nginx 0.7.10                                          13.08.2008

    *) Исправление: ошибок в директивах addition_types, charset_types,
       gzip_types, ssi_types, sub_filter_types и xslt_types; ошибки
       появились в 0.7.9.

    *) Исправление: рекурсивной error_page для 500 ошибки.

    *) Исправление: теперь модуль ngx_http_realip_module устанавливает адрес
       не для всего keepalive соединения, а для каждого запроса по этому
       соединению.


Изменения в nginx 0.7.9                                           12.08.2008

    *) Изменение: теперь ngx_http_charset_module по умолчанию работает со
       следующими MIME-типами: text/html, text/css, text/xml, text/plain,
       text/vnd.wap.wml, application/x-javascript и application/rss+xml.

    *) Добавление: директивы charset_types и addition_types.

    *) Добавление: теперь директивы gzip_types, ssi_types и sub_filter_types
       используют хэш.

    *) Добавление: модуль ngx_cpp_test_module.

    *) Добавление: директива expires поддерживает суточное время.

    *) Добавление: улучшения и исправления в модуле ngx_http_xslt_module.
       Спасибо Денису Латыпову и Максиму Дунину.

    *) Исправление: директива log_not_found не работала при поиске индексных
       файлов.

    *) Исправление: HTTPS-соединения могли зависнуть, если использовались
       методы kqueue, epoll, rtsig или eventport; ошибка появилась в 0.7.7.

    *) Исправление: если в директивах server_name, valid_referers и map
       использовалась маска вида "*.domain.tld" и при этом полное имя вида
       "domain.tld" не было описано, то это имя попадало под маску; ошибка
       появилась в 0.3.18.


Изменения в nginx 0.7.8                                           04.08.2008

    *) Добавление: модуль ngx_http_xslt_module.

    *) Добавление: переменные "$arg_...".

    *) Добавление: поддержка directio в Solaris.
       Спасибо Ivan Debnar.

    *) Исправление: теперь, если FastCGI-сервер присылает строку "Location"
       в заголовке ответа без строки статуса, то nginx использует код
       статуса 302.
       Спасибо Максиму Дунину.


Изменения в nginx 0.7.7                                           30.07.2008

    *) Изменение: теперь ошибка EAGAIN при вызове connect() не считается
       временной.

    *) Изменение: значением переменной $ssl_client_cert теперь является
       сертификат, перед каждой строкой которого, кроме первой, вставляется
       символ табуляции; неизменённый сертификат доступен через переменную
       $ssl_client_raw_cert.

    *) Добавление: параметр ask директивы ssl_verify_client.

    *) Добавление: улучшения в обработке byte-range.
       Спасибо Максиму Дунину.

    *) Добавление: директива directio.
       Спасибо Jiang Hong.

    *) Добавление: поддержка sendfile() в MacOSX 10.5.

    *) Исправление: в MacOSX и Cygwin при проверке location'ов теперь
       делается сравнение без учёта регистра символов; однако, сравнение
       ограничено только однобайтными locale'ями.

    *) Исправление: соединения почтового прокси-сервера зависали в режиме
       SSL, если использовались методы select, poll или /dev/poll.

    *) Исправление: ошибки при использовании кодировки UTF-8 в
       ngx_http_autoindex_module.


Изменения в nginx 0.7.6                                           07.07.2008

    *) Исправление: теперь при использовании переменных в директиве
       access_log всегда проверяется существовании root'а для запроса.

    *) Исправление: модуль ngx_http_flv_module не поддерживал несколько
       значений в аргументах запроса.


Изменения в nginx 0.7.5                                           01.07.2008

    *) Исправления в поддержке переменных в директиве access_log; ошибки
       появились в 0.7.4.

    *) Исправление: nginx не собирался с параметром
       --without-http_gzip_module; ошибка появилась в 0.7.3.
       Спасибо Кириллу Коринскому.

    *) Исправление: при совместном использовании sub_filter и SSI ответы
       могли передаваться неверно.


Изменения в nginx 0.7.4                                           30.06.2008

    *) Добавление: директива access_log поддерживает переменные.

    *) Добавление: директива open_log_file_cache.

    *) Добавление: ключ -g.

    *) Добавление: поддержка строки "Expect" в заголовке запроса.

    *) Исправление: большие включения в SSI могли передавались не полностью.


Изменения в nginx 0.7.3                                           23.06.2008

    *) Изменение: MIME-тип для расширения rss изменён на
       "application/rss+xml".

    *) Изменение: теперь директива "gzip_vary on" выдаёт строку
       "Vary: Accept-Encoding" в заголовке ответа и для несжатых ответов.

    *) Добавление: теперь при использовании протокола "https://" в директиве
       rewrite автоматически делается редирект.

    *) Исправление: директива proxy_pass не работала с протоколом HTTPS;
       ошибка появилась в 0.6.9.


Изменения в nginx 0.7.2                                           16.06.2008

    *) Добавление: теперь nginx поддерживает шифры с обменом EDH-ключами.

    *) Добавление: директива ssl_dhparam.

    *) Добавление: переменная $ssl_client_cert.
       Спасибо Manlio Perillo.

    *) Исправление: после изменения URI с помощью директивы rewrite nginx не
       искал новый location; ошибка появилась в 0.7.1.
       Спасибо Максиму Дунину.

    *) Исправление: nginx не собирался без библиотеки PCRE; ошибка появилась
       в 0.7.1.

    *) Исправление: при редиректе запроса к каталогу с добавлением слэша
       nginx не добавлял аргументы из оригинального запроса.


Изменения в nginx 0.7.1                                           26.05.2008

    *) Изменение: теперь поиск location'а делается с помощью дерева.

    *) Изменение: директива optimize_server_names упразднена в связи с
       появлением директивы server_name_in_redirect.

    *) Изменение: некоторые давно устаревшие директивы больше не
       поддерживаются.

    *) Изменение: параметр "none" в директиве ssl_session_cache; теперь этот
       параметр используется по умолчанию.
       Спасибо Rob Mueller.

    *) Исправление: рабочие процессы могли не реагировать на сигналы
       переконфигурации и ротации логов.

    *) Исправление: nginx не собирался на последних Fedora 9 Linux.
       Спасибо Roxis.


Изменения в nginx 0.7.0                                           19.05.2008

    *) Изменение: теперь символы 0x00-0x1F, '"' и '\' в access_log
       записываются в виде \xXX.
       Спасибо Максиму Дунину.

    *) Изменение: теперь nginx разрешает несколько строк "Host" в заголовке
       запроса.

    *) Добавление: директива expires поддерживает флаг modified.

    *) Добавление: переменные $uid_got и $uid_set можно использовать на
       любой стадии обработки запроса.

    *) Добавление: переменная $hostname.
       Спасибо Андрею Нигматулину.

    *) Добавление: поддержка DESTDIR.
       Спасибо Todd A. Fisher и Andras Voroskoi.

    *) Исправление: при использовании keepalive на Linux в рабочем процессе
       мог произойти segmentation fault.


Изменения в nginx 0.6.31                                          12.05.2008

    *) Исправление: nginx не обрабатывал ответ FastCGI-сервера, если строка
       заголовка ответа была в конце записи FastCGI; ошибка появилась в
       0.6.2.
       Спасибо Сергею Серову.

    *) Исправление: при удалении файла и использовании директивы
       open_file_cache_errors off в рабочем процессе мог произойти
       segmentation fault.


Изменения в nginx 0.6.30                                          29.04.2008

    *) Изменение: теперь, если маске, заданной в директиве include, не
       соответствует ни один файл, то nginx не выдаёт ошибку.

    *) Добавление: теперь время в директивах можно задавать без пробела,
       например, "1h50m".

    *) Исправление: утечек памяти, если директива ssl_verify_client имела
       значение on.
       Спасибо Chavelle Vincent.

    *) Исправление: директива sub_filter могла вставлять заменяемый текст в
       вывод.

    *) Исправление: директива error_page не воспринимала параметры в
       перенаправляемом URI.

    *) Исправление: теперь при сборке с Cygwin nginx всегда открывает файлы
       в бинарном режиме.

    *) Исправление: nginx не собирался под OpenBSD; ошибка появилась в
       0.6.15.


Изменения в nginx 0.6.29                                          18.03.2008

    *) Добавление: модуль ngx_google_perftools_module.

    *) Исправление: модуль ngx_http_perl_module не собирался на 64-битных
       платформах; ошибка появилась в 0.6.27.


Изменения в nginx 0.6.28                                          13.03.2008

    *) Исправление: метод rtsig не собирался; ошибка появилась в 0.6.27.


Изменения в nginx 0.6.27                                          12.03.2008

    *) Изменение: теперь на Linux 2.6.18+ по умолчанию не собирается метод
       rtsig.

    *) Изменение: теперь при перенаправлении запроса в именованный location
       с помощью директивы error_page метод запроса не изменяется.

    *) Добавление: директивы resolver и resolver_timeout в SMTP
       прокси-сервере.

    *) Добавление: директива post_action поддерживает именованные
       location'ы.

    *) Исправление: при перенаправлении запроса из location'а c обработчиком
       proxy, FastCGI или memcached в именованный location со статическим
       обработчиком в рабочем процессе происходил segmentation fault.

    *) Исправление: браузеры не повторяли SSL handshake, если при первом
       handshake не оказалось правильного клиентского сертификата.
       Спасибо Александру Инюхину.

    *) Исправление: при перенаправлении ошибок 495-497 с помощью директивы
       error_page без изменения кода ошибки nginx пытался выделить очень
       много памяти.

    *) Исправление: утечки памяти в долгоживущих небуфферизированных
       соединениях.

    *) Исправление: утечки памяти в resolver'е.

    *) Исправление: при перенаправлении запроса из location'а c обработчиком
       proxy в другой location с обработчиком proxy в рабочем процессе
       происходил segmentation fault.

    *) Исправление: ошибки в кэшировании переменных $proxy_host и
       $proxy_port.
       Спасибо Сергею Боченкову.

    *) Исправление: директива proxy_pass с переменными использовала порт,
       описанной в другой директиве proxy_pass без переменных, но с таким же
       именем хоста.
       Спасибо Сергею Боченкову.

    *) Исправление: во время переконфигурации на некоторых 64-битном
       платформах в лог записывался alert "sendmsg() failed (9: Bad file
       descriptor)".

    *) Исправление: при повторном использовании в SSI пустого block'а в
       качестве заглушки в рабочем процессе происходил segmentation fault.

    *) Исправление: ошибки при копировании части URI, содержащего
       экранированные символы, в аргументы.


Изменения в nginx 0.6.26                                          11.02.2008

    *) Исправление: директивы proxy_store и fastcgi_store не проверяли длину
       ответа.

    *) Исправление: при использовании большого значения в директиве expires
       в рабочем процессе происходил segmentation fault.
       Спасибо Joaquin Cuenca Abela.

    *) Исправление: nginx неверно определял длину строки кэша на Pentium 4.
       Спасибо Геннадию Махомеду.

    *) Исправление: в проксированных подзапросах и подзапросах к
       FastCGI-серверу вместо метода GET использовался оригинальный метод
       клиента.

    *) Исправление: утечки сокетов в режиме HTTPS при использовании
       отложенного accept'а.
       Спасибо Ben Maurer.

    *) Исправление: nginx выдавал ошибочное сообщение "SSL_shutdown() failed
       (SSL: )"; ошибка появилась в 0.6.23.

    *) Исправление: при использовании HTTPS запросы могли завершаться с
       ошибкой "bad write retry"; ошибка появилась в 0.6.23.


Изменения в nginx 0.6.25                                          08.01.2008

    *) Изменение: вместо специального параметра "*" в директиве server_name
       теперь используется директива server_name_in_redirect.

    *) Изменение: в качестве основного имени в директиве server_name теперь
       можно использовать имена с масками и регулярными выражениями.

    *) Изменение: директива satisfy_any заменена директивой satisfy.

    *) Изменение: после переконфигурации старые рабочие процесс могли сильно
       нагружать процессор при запуске под Linux OpenVZ.

    *) Добавление: директива min_delete_depth.

    *) Исправление: методы COPY и MOVE не работали с одиночными файлами.

    *) Исправление: модуль ngx_http_gzip_static_module не позволял работать
       модулю ngx_http_dav_module; ошибка появилась в 0.6.23.

    *) Исправление: утечки сокетов в режиме HTTPS при использовании
       отложенного accept'а.
       Спасибо Ben Maurer.

    *) Исправление: nginx не собирался без библиотеки PCRE; ошибка появилась
       в 0.6.23.


Изменения в nginx 0.6.24                                          27.12.2007

    *) Исправление: при использовании HTTPS в рабочем процессе мог произойти
       segmentation fault; ошибка появилась в 0.6.23.


Изменения в nginx 0.6.23                                          27.12.2007

    *) Изменение: параметр "off" в директиве ssl_session_cache; теперь этот
       параметр используется по умолчанию.

    *) Изменение: директива open_file_cache_retest переименована в
       open_file_cache_valid.

    *) Добавление: директива open_file_cache_min_uses.

    *) Добавление: модуль ngx_http_gzip_static_module.

    *) Добавление: директива gzip_disable.

    *) Добавление: директиву memcached_pass можно использовать внутри блока
       if.

    *) Исправление: если внутри одного location'а использовались директивы
       "memcached_pass" и "if", то в рабочем процессе происходил
       segmentation fault.

    *) Исправление: если при использовании директивы satisfy_any on" были
       заданы директивы не всех модулей доступа, то заданные директивы не
       проверялись.

    *) Исправление: параметры, заданные регулярным выражением в директиве
       valid_referers, не наследовалась с предыдущего уровня.

    *) Исправление: директива post_action не работала, если запрос
       завершался с кодом 499.

    *) Исправление: оптимизация использования 16K буфера для SSL-соединения.
       Спасибо Ben Maurer.

    *) Исправление: STARTTLS в режиме SMTP не работал.
       Спасибо Олегу Мотиенко.

    *) Исправление: при использовании HTTPS запросы могли завершаться с
       ошибкой "bad write retry"; ошибка появилась в 0.5.13.


Изменения в nginx 0.6.22                                          19.12.2007

    *) Изменение: теперь все методы модуля ngx_http_perl_module возвращают
       значения, скопированные в память, выделенную perl'ом.

    *) Исправление: если nginx был собран с модулем ngx_http_perl_module,
       использовался perl до версии 5.8.6 и perl поддерживал потоки, то во
       время переконфигурации основной процесс аварийно выходил; ошибка
       появилась в 0.5.9.
       Спасибо Борису Жмурову.

    *) Исправление: в методы модуля ngx_http_perl_module могли передаваться
       неверные результаты выделения в регулярных выражениях.

    *) Исправление: если метод $r->has_request_body() вызывался для запроса,
       у которого небольшое тело запроса было уже полностью получено, то в
       рабочем процессе происходил segmentation fault.

    *) Исправление: large_client_header_buffers не освобождались перед
       переходом в состояние keep-alive.
       Спасибо Олександру Штепе.

    *) Исправление: в переменной $upstream_addr не записывался последний
       адрес; ошибка появилась в 0.6.18.

    *) Исправление: директива fastcgi_catch_stderr не возвращала ошибку;
       теперь она возвращает ошибку 502, которую можно направить на
       следующий сервер с помощью "fastcgi_next_upstream invalid_header".

    *) Исправление: при использовании директивы fastcgi_catch_stderr в
       основном процессе происходил segmentation fault; ошибка появилась в
       0.6.10.
       Спасибо Manlio Perillo.


Изменения в nginx 0.6.21                                          03.12.2007

    *) Изменение: если в значениях переменных директивы proxy_pass
       используются только IP-адреса, то указывать resolver не нужно.

    *) Исправление: при использовании директивы proxy_pass c URI-частью в
       рабочем процессе мог произойти segmentation fault; ошибка появилась в
       0.6.19.

    *) Исправление: если resolver использовался на платформах, не
       поддерживающих метод kqueue, то nginx выдавал alert "name is out of
       response".
       Спасибо Андрею Нигматулину.

    *) Исправление: При использовании переменной $server_protocol в
       FastCGI-параметрах и запросе, длина которого была близка к значению
       директивы client_header_buffer_size, nginx выдавал alert "fastcgi:
       the request record is too big".

    *) Исправление: при обычном запросе версии HTTP/0.9 к HTTPS серверу
       nginx возвращал обычный ответ.


Изменения в nginx 0.6.20                                          28.11.2007

    *) Исправление: при использовании директивы proxy_pass c URI-частью в
       рабочем процессе мог произойти segmentation fault; ошибка появилась в
       0.6.19.


Изменения в nginx 0.6.19                                          27.11.2007

    *) Исправление: версия 0.6.18 не собиралась.


Изменения в nginx 0.6.18                                          27.11.2007

    *) Изменение: теперь модуль ngx_http_userid_module в поле куки с номером
       процесса добавляет микросекунды на время старта.

    *) Изменение: в error_log теперь записывается полная строка запроса
       вместо только URI.

    *) Добавление: директива proxy_pass поддерживает переменные.

    *) Добавление: директивы resolver и resolver_timeout.

    *) Добавление: теперь директива "add_header last-modified ''" удаляет в
       заголовке ответа строку "Last-Modified".

    *) Исправление: директива limit_rate не позволяла передавать на полной
       скорости, даже если был указан очень большой лимит.


Изменения в nginx 0.6.17                                          15.11.2007

    *) Добавление: поддержка строки "If-Range" в заголовке запроса.
       Спасибо Александру Инюхину.

    *) Исправление: при использовании директивы msie_refresh повторно
       экранировались уже экранированные символы; ошибка появилась в 0.6.4.

    *) Исправление: директива autoindex не работала при использовании "alias
       /".

    *) Исправление: при использовании подзапросов в рабочем процессе мог
       произойти segmentation fault.

    *) Исправление: при использовании SSL и gzip большие ответы могли
       передаваться не полностью.

    *) Исправление: если ответ проксированного сервера был версии HTTP/0.9,
       то переменная $status была равна 0.


Изменения в nginx 0.6.16                                          29.10.2007

    *) Изменение: теперь на Linux используется uname(2) вместо procfs.
       Спасибо Илье Новикову.

    *) Исправление: если в директиве error_page использовался символ "?", то
       он экранировался при проксировании запроса; ошибка появилась в
       0.6.11.

    *) Исправление: совместимость с mget.


Изменения в nginx 0.6.15                                          22.10.2007

    *) Добавление: совместимость с Cygwin.
       Спасибо Владимиру Кутакову.

    *) Добавление: директива merge_slashes.

    *) Добавление: директива gzip_vary.

    *) Добавление: директива server_tokens.

    *) Исправление: nginx не раскодировал URI в команде SSI include.

    *) Исправление: при использовании переменной в директивах charset или
       source_charset на старте или во время переконфигурации происходил
       segmentation fault,

    *) Исправление: nginx возвращал ошибку 400 на запросы вида
       "GET http://www.domain.com HTTP/1.0".
       Спасибо James Oakley.

    *) Исправление: после перенаправления запроса с телом запроса с помощью
       директивы error_page nginx пытался снова прочитать тело запроса;
       ошибка появилась в 0.6.7.

    *) Исправление: в рабочем процессе происходил segmentation fault, если у
       сервера, обрабатывающему запрос, не был явно определён server_name;
       ошибка появилась в 0.6.7.


Изменения в nginx 0.6.14                                          15.10.2007

    *) Изменение: теперь по умолчанию команда SSI echo использует
       кодирование entity.

    *) Добавление: параметр encoding в команде SSI echo.

    *) Добавление: директиву access_log можно использовать внутри блока
       limit_except.

    *) Исправление: если все сервера апстрима оказывались недоступными, то
       до восстановления работоспособности у всех серверов вес становился
       равным одному; ошибка появилась в 0.6.6.

    *) Исправление: при использовании переменных $date_local и $date_gmt вне
       модуля ngx_http_ssi_filter_module в рабочем процессе происходил
       segmentation fault.

    *) Исправление: при использовании включённом отладочном логе в рабочем
       процессе мог произойти segmentation fault.
       Спасибо Андрею Нигматулину.

    *) Исправление: ngx_http_memcached_module не устанавливал
       $upstream_response_time.
       Спасибо Максиму Дунину.

    *) Исправление: рабочий процесс мог зациклиться при использовании
       memcached.

    *) Исправление: nginx распознавал параметры "close" и "keep-alive" в
       строке "Connection" в заголовке запроса только, если они были в
       нижнем регистре; ошибка появилась в 0.6.11.

    *) Исправление: sub_filter не работал с пустой строкой замены.

    *) Исправление: в парсинге sub_filter.


Изменения в nginx 0.6.13                                          24.09.2007

    *) Исправление: nginx не закрывал файл каталога для запроса HEAD, если
       использовался autoindex
       Спасибо Arkadiusz Patyk.


Изменения в nginx 0.6.12                                          21.09.2007

    *) Изменение: почтовый прокси-сервер разделён на три модуля: pop3, imap
       и smtp.

    *) Добавление: параметры конфигурации --without-mail_pop3_module,
       --without-mail_imap_module и --without-mail_smtp_module.

    *) Добавление: директивы smtp_greeting_delay и smtp_client_buffer модуля
       ngx_mail_smtp_module.

    *) Исправление: wildcard в конце имени сервера не работали; ошибка
       появилась в 0.6.9.

    *) Исправление: при использовании разделяемой библиотеки PCRE,
       расположенной в нестандартном месте, nginx не запускался на Solaris.

    *) Исправление: директивы proxy_hide_header и fastcgi_hide_header не
       скрывали строки заголовка ответа с именем больше 32 символов.
       Спасибо Manlio Perillo.


Изменения в nginx 0.6.11                                          11.09.2007

    *) Исправление: счётчик активных соединений всегда рос при использовании
       почтового прокси-сервера.

    *) Исправление: если бэкенд возвращал только заголовок ответа при
       небуферизированном проксировании, то nginx закрывал соединение с
       бэкендом по таймауту.

    *) Исправление: nginx не поддерживал несколько строк "Connection" в
       заголовке запроса.

    *) Исправление: если в сервере апстрима был задан max_fails, то после
       первой же неудачной попытки вес сервера навсегда становился равным
       одному; ошибка появилась в 0.6.6.


Изменения в nginx 0.6.10                                          03.09.2007

    *) Добавление: директивы open_file_cache, open_file_cache_retest и
       open_file_cache_errors.

    *) Исправление: утечки сокетов; ошибка появилась в 0.6.7.

    *) Исправление: В строку заголовка ответа "Content-Type", указанную в
       методе $r->send_http_header(), не добавлялась кодировка, указанная в
       директиве charset.

    *) Исправление: при использовании метода /dev/poll в рабочем процессе
       мог произойти segmentation fault.


Изменения в nginx 0.6.9                                           28.08.2007

    *) Исправление: рабочий процесс мог зациклиться при использовании
       протокола HTTPS; ошибка появилась в 0.6.7.

    *) Исправление: если сервер слушал на двух адресах или портах, то nginx
       не запускался при использовании wildcard в конце имени сервера.

    *) Исправление: директива ip_hash могла неверно помечать сервера как
       нерабочие.

    *) Исправление: nginx не собирался на amd64; ошибка появилась в 0.6.8.


Изменения в nginx 0.6.8                                           20.08.2007

    *) Изменение: теперь nginx пытается установить директивы
       worker_priority, worker_rlimit_nofile, worker_rlimit_core,
       worker_rlimit_sigpending без привилегий root'а.

    *) Изменение: теперь nginx экранирует символы пробела и "%" при передаче
       запроса серверу аутентификации почтового прокси-сервера.

    *) Изменение: теперь nginx экранирует символ "%" в переменной
       $memcached_key.

    *) Исправление: при указании относительного пути к конфигурационному
       файлу в качестве параметра ключа -c nginx определял путь относительно
       конфигурационного префикса; ошибка появилась в 0.6.6.

    *) Исправление: nginx не работал на FreeBSD/sparc64.


Изменения в nginx 0.6.7                                           15.08.2007

    *) Изменение: теперь пути, указанные в директивах include,
       auth_basic_user_file, perl_modules, ssl_certificate,
       ssl_certificate_key и ssl_client_certificate, определяются
       относительно каталога конфигурационного файла nginx.conf, а не
       относительно префикса.

    *) Изменение: параметр --sysconfdir=PATH в configure упразднён.

    *) Изменение: для обновления на лету версий 0.1.x создан специальный
       сценарий make upgrade1.

    *) Добавление: директивы server_name и valid_referers поддерживают
       регулярные выражения.

    *) Добавление: директива server в блоке upstream поддерживает параметр
       backup.

    *) Добавление: модуль ngx_http_perl_module поддерживает метод
       $r->discard_request_body.

    *) Добавление: директива "add_header Last-Modified ..." меняет строку
       "Last-Modified" в заголовке ответа.

    *) Исправление: если на запрос с телом возвращался ответ с кодом HTTP
       отличным от 200, и после этого запроса соединение переходило в
       состояние keep-alive, то на следующий запрос nginx возвращал 400.

    *) Исправление: если в директиве auth_http был задан неправильный адрес,
       то в рабочем процессе происходил segmentation fault.

    *) Исправление: теперь по умолчанию nginx использует значение 511 для
       listen backlog на всех платформах, кроме FreeBSD.
       Спасибо Jiang Hong.

    *) Исправление: рабочий процесс мог зациклиться, если server в блоке
       upstream был помечен как down; ошибка появилась в 0.6.6.

    *) Исправление: sendfilev() в Solaris теперь не используется при
       передаче тела запроса FastCGI-серверу через unix domain сокет.


Изменения в nginx 0.6.6                                           30.07.2007

    *) Добавление: параметр --sysconfdir=PATH в configure.

    *) Добавление: именованные location'ы.

    *) Добавление: переменную $args можно устанавливать с помощью set.

    *) Добавление: переменная $is_args.

    *) Исправление: равномерное распределение запросов к апстримам с
       большими весами.

    *) Исправление: если клиент в почтовом прокси-сервере закрывал
       соединение, то nginx мог не закрывать соединение с бэкендом.

    *) Исправление: при использовании одного хоста в качестве бэкендов для
       протоколов HTTP и HTTPS без явного указания портов, nginx использовал
       только один порт - 80 или 443.

    *) Исправление: nginx не собирался на Solaris/amd64 Sun Studio 11 и
       более ранними версиями; ошибка появилась в 0.6.4.


Изменения в nginx 0.6.5                                           23.07.2007

    *) Добавление: переменная $nginx_version.
       Спасибо Николаю Гречуху.

    *) Добавление: почтовый прокси-сервер поддерживает AUTHENTICATE в режиме
       IMAP.
       Спасибо Максиму Дунину.

    *) Добавление: почтовый прокси-сервер поддерживает STARTTLS в режиме
       SMTP.
       Спасибо Максиму Дунину.

    *) Исправление: теперь nginx экранирует пробел в переменной
       $memcached_key.

    *) Исправление: nginx неправильно собирался Sun Studio на Solaris/amd64.
       Спасибо Jiang Hong.

    *) Исправление: незначительных потенциальных ошибок.
       Спасибо Coverity's Scan.


Изменения в nginx 0.6.4                                           17.07.2007

    *) Безопасность: при использовании директивы msie_refresh был возможен
       XSS.
       Спасибо Максиму Богуку.

    *) Изменение: директивы proxy_store и fastcgi_store изменены.

    *) Добавление: директивы proxy_store_access и fastcgi_store_access.

    *) Исправление: nginx не работал на Solaris/sparc64, если был собран Sun
       Studio.
       Спасибо Андрею Нигматулину.

    *) Изменение: обход ошибки в Sun Studio 12.
       Спасибо Jiang Hong.


Изменения в nginx 0.6.3                                           12.07.2007

    *) Добавление: директивы proxy_store и fastcgi_store.

    *) Исправление: при использовании директивы auth_http_header в рабочем
       процессе мог произойти segmentation fault.
       Спасибо Максиму Дунину.

    *) Исправление: если использовался метод аутентификации CRAM-MD5, но он
       не был разрешён, то в рабочем процессе происходил segmentation fault.

    *) Исправление: при использовании протокола HTTPS в директиве proxy_pass
       в рабочем процессе мог произойти segmentation fault.

    *) Исправление: в рабочем процессе мог произойти segmentation fault,
       если использовался метод eventport.

    *) Исправление: директивы proxy_ignore_client_abort и
       fastcgi_ignore_client_abort не работали; ошибка появилась в 0.5.13.


Изменения в nginx 0.6.2                                           09.07.2007

    *) Исправление: если заголовок ответа был разделён в FastCGI-записях, то
       nginx передавал клиенту мусор в таких заголовках.


Изменения в nginx 0.6.1                                           17.06.2007

    *) Исправление: в парсинге SSI.

    *) Исправление: при использовании удалённого подзапроса в SSI
       последующий подзапрос локального файла мог отдаваться клиенту в
       неверном порядке.

    *) Исправление: большие включения в SSI, сохранённые во временные файлы,
       передавались не полностью.

    *) Исправление: значение perl'овой переменной $$ модуля
       ngx_http_perl_module было равно номеру главного процесса.


Изменения в nginx 0.6.0                                           14.06.2007

    *) Добавление: директивы "server_name", "map", and "valid_referers"
       поддерживают маски вида "www.example.*".


Изменения в nginx 0.5.25                                          11.06.2007

    *) Исправление: nginx не собирался с параметром
       --without-http_rewrite_module; ошибка появилась в 0.5.24.


Изменения в nginx 0.5.24                                          06.06.2007

    *) Безопасность: директива ssl_verify_client не работала, если запрос
       выполнялся по протоколу HTTP/0.9.

    *) Исправление: при использовании сжатия часть ответа могла передаваться
       несжатой; ошибка появилась в 0.5.23.


Изменения в nginx 0.5.23                                          04.06.2007

    *) Добавление: модуль ngx_http_ssl_module поддерживает расширение TLS
       Server Name Indication.

    *) Добавление: директива fastcgi_catch_stderr.
       Спасибо Николаю Гречуху, проект OWOX.

    *) Исправление: на Линуксе в основном процессе происходил segmentation
       fault, если два виртуальных сервера должны bind()ится к
       пересекающимся портам.

    *) Исправление: если nginx был собран с модулем ngx_http_perl_module и
       perl поддерживал потоки, то во время второй переконфигурации
       выдавались ошибки "panic: MUTEX_LOCK" и "perl_parse() failed".

    *) Исправление: в использовании протокола HTTPS в директиве proxy_pass.


Изменения в nginx 0.5.22                                          29.05.2007

    *) Исправление: большое тело запроса могло не передаваться бэкенду;
       ошибка появилась в 0.5.21.


Изменения в nginx 0.5.21                                          28.05.2007

    *) Исправление: если внутри сервера описано больше примерно десяти
       location'ов, то location'ы, заданные с помощью регулярного выражения,
       могли выполняться не в том, порядке, в каком они описаны.

    *) Исправление: на 64-битной платформе рабочий процесс мог зациклиться,
       если 33-тий по счёту или последующий бэкенд упал.
       Спасибо Антону Поварову.

    *) Исправление: при использовании библиотеки PCRE на Solaris/sparc64 мог
       произойти bus error.
       Спасибо Андрею Нигматулину.

    *) Исправление: в использовании протокола HTTPS в директиве proxy_pass.


Изменения в nginx 0.5.20                                          07.05.2007

    *) Добавление: директива sendfile_max_chunk.

    *) Добавление: переменные "$http_...", "$sent_http_..." и
       "$upstream_http_..." можно менять директивой set.

    *) Исправление: при использовании SSI-команды 'if expr="$var = /"' в
       рабочем процессе мог произойти segmentation fault.

    *) Исправление: завершающая строка multipart range ответа передавалась
       неверно.
       Спасибо Evan Miller.

    *) Исправление: nginx не работал на Solaris/sparc64, если был собран Sun
       Studio.
       Спасибо Андрею Нигматулину.

    *) Исправление: модуль ngx_http_perl_module не собирался make в Solaris.
       Спасибо Андрею Нигматулину.


Изменения в nginx 0.5.19                                          24.04.2007

    *) Изменение: значение переменной $request_time теперь записывается с
       точностью до миллисекунд.

    *) Изменение: метод $r->rflush в модуле ngx_http_perl_module
       переименован в $r->flush.

    *) Добавление: переменная $upstream_addr.

    *) Добавление: директивы proxy_headers_hash_max_size и
       proxy_headers_hash_bucket_size.
       Спасибо Володымыру Костырко.

    *) Исправление: при использовании sendfile и limit_rate на 64-битных
       платформах нельзя было передавать файлы больше 2G.

    *) Исправление: при использовании sendfile на 64-битном Linux нельзя
       было передавать файлы больше 2G.


Изменения в nginx 0.5.18                                          19.04.2007

    *) Добавление: модуль ngx_http_sub_filter_module.

    *) Добавление: переменные "$upstream_http_...".

    *) Добавление: теперь переменные $upstream_status и
       $upstream_response_time содержат данные о всех обращениях к
       апстримам, сделанным до X-Accel-Redirect.

    *) Исправление: если nginx был собран с модулем ngx_http_perl_module и
       perl не поддерживал multiplicity, то после первой переконфигурации и
       после получения любого сигнала в основном процессе происходил
       segmentation fault; ошибка появилась в 0.5.9.

    *) Исправление: если perl не поддерживал multiplicity, то после
       переконфигурации перловый код не работал; ошибка появилась в 0.3.38.


Изменения в nginx 0.5.17                                          02.04.2007

    *) Изменение: теперь nginx для метода TRACE всегда возвращает код 405.

    *) Добавление: теперь nginx поддерживает директиву include внутри блока
       types.

    *) Исправление: использование переменной $document_root в директиве root
       и alias запрещено: оно вызывало рекурсивное переполнение стека.

    *) Исправление: в использовании протокола HTTPS в директиве proxy_pass.

    *) Исправление: в некоторых случаях некэшируемые переменные (такие, как
       $uri) возвращали старое закэшированное значение.


Изменения в nginx 0.5.16                                          26.03.2007

    *) Исправление: в качестве ключа для хэша в директиве ip_hash не
       использовалась сеть класса С.
       Спасибо Павлу Ярковому.

    *) Исправление: если в строке "Content-Type" в заголовке ответа бэкенда
       был указан charset и строка завершалась символом ";", то в рабочем
       процессе мог произойти segmentation fault; ошибка появилась в 0.3.50.

    *) Исправление: ошибки "[alert] zero size buf" при работе с
       FastCGI-сервером, если тело запроса, записанное во временный файл,
       было кратно 32K.

    *) Исправление: nginx не собирался на Solaris без параметра
       --with-debug; ошибка появилась в 0.5.15.


Изменения в nginx 0.5.15                                          19.03.2007

    *) Добавление: почтовый прокси-сервер поддерживает аутентифицированное
       SMTP-проксирование и директивы smtp_auth, smtp_capabilities и
       xclient.
       Спасибо Антону Южанинову и Максиму Дунину.

    *) Добавление: теперь keep-alive соединения закрываются сразу же по
       получении сигнала переконфигурации.

    *) Изменение: директивы imap и auth переименованы соответственно в mail
       и pop3_auth.

    *) Исправление: если использовался метод аутентификации CRAM-MD5 и не
       был разрешён метод APOP, то в рабочем процессе происходил
       segmentation fault.

    *) Исправление: при использовании директивы starttls only в протоколе
       POP3 nginx разрешал аутентификацию без перехода в режим SSL.

    *) Исправление: рабочие процессы не выходили после переконфигурации и не
       переоткрывали логи, если использовался метод eventport.

    *) Исправление: при использовании директивы ip_hash рабочий процесс мог
       зациклиться.

    *) Исправление: теперь nginx не пишет в лог некоторые alert'ы, если
       используются методы eventport или /dev/poll.


Изменения в nginx 0.5.14                                          23.02.2007

    *) Исправление: nginx игнорировал лишние закрывающие скобки "}" в конце
       конфигурационного файла.


Изменения в nginx 0.5.13                                          19.02.2007

    *) Добавление: методы COPY и MOVE.

    *) Исправление: модуль ngx_http_realip_module устанавливал мусор для
       запросов, переданных по keep-alive соединению.

    *) Исправление: nginx не работал на 64-битном big-endian Linux.
       Спасибо Андрею Нигматулину.

    *) Исправление: при получении слишком длинной команды IMAP/POP3-прокси
       теперь сразу закрывает соединение, а не по таймауту.

    *) Исправление: если при использовании метода epoll клиент закрывал
       преждевременно соединение со своей стороны, то nginx закрывал это
       соединение только по истечении таймаута на передачу.

    *) Исправление: nginx не собирался на платформах, отличных от i386,
       amd64, sparc и ppc; ошибка появилась в 0.5.8.


Изменения в nginx 0.5.12                                          12.02.2007

    *) Исправление: nginx не собирался на платформах, отличных от i386,
       amd64, sparc и ppc; ошибка появилась в 0.5.8.

    *) Исправление: при использовании временных файлов в время работы с
       FastCGI-сервером в рабочем процессе мог произойти segmentation fault;
       ошибка появилась в 0.5.8.

    *) Исправление: если переменная $fastcgi_script_name записывалась в лог,
       то в рабочем процессе мог произойти segmentation fault.

    *) Исправление: ngx_http_perl_module не собирался на Solaris.


Изменения в nginx 0.5.11                                          05.02.2007

    *) Добавление: теперь configure определяет библиотеку PCRE в MacPorts.
       Спасибо Chris McGrath.

    *) Исправление: ответ был неверным, если запрашивалось несколько
       диапазонов; ошибка появилась в 0.5.6.

    *) Исправление: директива create_full_put_path не могла создавать
       промежуточные каталоги, если не была установлена директива
       dav_access.
       Спасибо Evan Miller.

    *) Исправление: вместо кодов ошибок "400" и "408" в access_log мог
       записываться код "0".

    *) Исправление: при сборке с оптимизацией -O2 в рабочем процессе мог
       произойти segmentation fault.


Изменения в nginx 0.5.10                                          26.01.2007

    *) Исправление: во время обновления исполняемого файла новый процесс не
       наследовал слушающие сокеты; ошибка появилась в 0.5.9.

    *) Исправление: при сборке с оптимизацией -O2 в рабочем процессе мог
       произойти segmentation fault; ошибка появилась в 0.5.1.


Изменения в nginx 0.5.9                                           25.01.2007

    *) Изменение: модуль ngx_http_memcached_module теперь в качестве ключа
       использует значение переменной $memcached_key.

    *) Добавление: переменная $memcached_key.

    *) Добавление: параметр clean в директиве client_body_in_file_only.

    *) Добавление: директива env.

    *) Добавление: директива sendfile работает внутри блока if.

    *) Добавление: теперь при ошибке записи в access_log nginx записывает
       сообщение в error_log, но не чаще одного раза в минуту.

    *) Исправление: директива "access_log off" не всегда запрещала запись в
       лог.


Изменения в nginx 0.5.8                                           19.01.2007

    *) Исправление: если использовалась директива
       "client_body_in_file_only on" и тело запроса было небольшое, то мог
       произойти segmentation fault.

    *) Исправление: происходил segmentation fault, если использовались
       директивы "client_body_in_file_only on" и
       "proxy_pass_request_body off" или "fastcgi_pass_request_body off", и
       делался переход к следующему бэкенду.

    *) Исправление: если при использовании директивы "proxy_buffering off"
       соединение с клиентом было неактивно, то оно закрывалось по таймауту,
       заданному директивой send_timeout; ошибка появилась в 0.4.7.

    *) Исправление: если при использовании метода epoll клиент закрывал
       преждевременно соединение со своей стороны, то nginx закрывал это
       соединение только по истечении таймаута на передачу.

    *) Исправление: ошибки "[alert] zero size buf" при работе с
       FastCGI-сервером.

    *) Исправление ошибок в директиве limit_zone.


Изменения в nginx 0.5.7                                           15.01.2007

    *) Добавление: оптимизация использования памяти в ssl_session_cache.

    *) Исправление ошибок в директивах ssl_session_cache и limit_zone.

    *) Исправление: на старте или во время переконфигурации происходил
       segmentation fault, если директивы ssl_session_cache или limit_zone
       использовались на 64-битных платформах.

    *) Исправление: при использовании директив add_before_body или
       add_after_body происходил segmentation fault, если в заголовке ответа
       нет строки "Content-Type".

    *) Исправление: библиотека OpenSSL всегда собиралась с поддержкой
       потоков.
       Спасибо Дену Иванову.

    *) Исправление: совместимость библиотеки PCRE-6.5+ и компилятора icc.


Изменения в nginx 0.5.6                                           09.01.2007

    *) Изменение: теперь модуль ngx_http_index_module игнорирует все методы,
       кроме GET, HEAD и POST.

    *) Добавление: модуль ngx_http_limit_zone_module.

    *) Добавление: переменная $binary_remote_addr.

    *) Добавление: директивы ssl_session_cache модулей ngx_http_ssl_module и
       ngx_imap_ssl_module.

    *) Добавление: метод DELETE поддерживает рекурсивное удаление.

    *) Исправление: при использовании $r->sendfile() byte-ranges
       передавались неверно.


Изменения в nginx 0.5.5                                           24.12.2006

    *) Изменение: ключ -v больше не выводит информацию о компиляторе.

    *) Добавление: ключ -V.

    *) Добавление: директива worker_rlimit_core поддерживает указание
       размера в K, M и G.

    *) Исправление: модуль nginx.pm теперь может устанавливаться
       непривилегированным пользователем.

    *) Исправление: при использовании методов $r->request_body или
       $r->request_body_file мог произойти segmentation fault.

    *) Исправление: ошибок, специфичных для платформы ppc.


Изменения в nginx 0.5.4                                           15.12.2006

    *) Добавление: директиву perl можно использовать внутри блока
       limit_except.

    *) Исправление: модуль ngx_http_dav_module требовал строку "Date" в
       заголовке запроса для метода DELETE.

    *) Исправление: при использовании одного параметра в директиве
       dav_access nginx мог сообщить об ошибке в конфигурации.

    *) Исправление: при использовании переменной $host мог произойти
       segmentation fault; ошибка появилась в 0.4.14.


Изменения в nginx 0.5.3                                           13.12.2006

    *) Добавление: модуль ngx_http_perl_module поддерживает методы
       $r->status, $r->log_error и $r->sleep.

    *) Добавление: метод $r->variable поддерживает переменные, неописанные в
       конфигурации nginx'а.

    *) Исправление: метод $r->has_request_body не работал.


Изменения в nginx 0.5.2                                           11.12.2006

    *) Исправление: если в директивах proxy_pass использовалось имя,
       указанное в upstream, то nginx пытался найти IP-адрес этого имени;
       ошибка появилась в 0.5.1.


Изменения в nginx 0.5.1                                           11.12.2006

    *) Исправление: директива post_action могла не работать после неудачного
       завершения запроса.

    *) Изменение: обход ошибки в Eudora для Mac; ошибка появилась в 0.4.11.
       Спасибо Bron Gondwana.

    *) Исправление: при указании в директиве fastcgi_pass имени описанного
       upstream'а выдавалось сообщение "no port in upstream"; ошибка
       появилась в 0.5.0.

    *) Исправление: если в директивах proxy_pass и fastcgi_pass
       использовались одинаковых имена серверов, но с разными портами, то
       эти директивы использовали первый описанный порт; ошибка появилась в
       0.5.0.

    *) Исправление: если в директивах proxy_pass и fastcgi_pass
       использовались unix domain сокеты, то эти директивы использовали
       первый описанный сокет; ошибка появилась в 0.5.0.

    *) Исправление: ngx_http_auth_basic_module игнорировал пользователя,
       если он был указан в последней строке файла паролей и после пароля не
       было перевода строки, возврата каретки или символа ":".

    *) Исправление: переменная $upstream_response_time могла быть равна
       "0.000", хотя время обработки было больше 1 миллисекунды.


Изменения в nginx 0.5.0                                           04.12.2006

    *) Изменение: параметры в виде "%name" в директиве log_format больше не
       поддерживаются.

    *) Изменение: директивы proxy_upstream_max_fails,
       proxy_upstream_fail_timeout, fastcgi_upstream_max_fails, и
       fastcgi_upstream_fail_timeout, memcached_upstream_max_fails и
       memcached_upstream_fail_timeout больше не поддерживаются.

    *) Добавление: директива server в блоке upstream поддерживает параметры
       max_fails, fail_timeout и down.

    *) Добавление: директива ip_hash в блоке upstream.

    *) Добавление: статус WAIT в строке "Auth-Status" в заголовке ответа
       сервера аутентификации IMAP/POP3 прокси.

    *) Исправление: nginx не собирался на 64-битных платформах; ошибка
       появилась в 0.4.14.


Изменения в nginx 0.4.14                                          27.11.2006

    *) Добавление: директива proxy_pass_error_message в IMAP/POP3 прокси.

    *) Добавление: теперь configure определяет библиотеку PCRE на FreeBSD,
       Linux и NetBSD.

    *) Исправление: ngx_http_perl_module не работал с перлом, собранным с
       поддержкой потоков; ошибка появилась в 0.3.38.

    *) Исправление: ngx_http_perl_module не работал корректно, если перл
       вызывался рекурсивно.

    *) Исправление: nginx игнорировал имя сервера в строке запроса.

    *) Исправление: если FastCGI сервер передавал много в stderr, то рабочий
       процесс мог зациклиться.

    *) Исправление: при изменении системного времени переменная
       $upstream_response_time могла быть отрицательной.

    *) Исправление: при использовании POP3 серверу аутентификации IMAP/POP3
       прокси не передавался параметр Auth-Login-Attempt.

    *) Исправление: при ошибке соединения с сервером аутентификации
       IMAP/POP3 прокси мог произойти segmentation fault.


Изменения в nginx 0.4.13                                          15.11.2006

    *) Добавление: директиву proxy_pass можно использовать внутри блока
       limit_except.

    *) Добавление: директива limit_except поддерживает все WebDAV методы.

    *) Исправление: при использовании директивы add_before_body без
       директивы add_after_body ответ передавался не полностью.

    *) Исправление: большое тело запроса не принималось, если использовались
       метод epoll и deferred accept().

    *) Исправление: для ответов модуля ngx_http_autoindex_module не
       выставлялась кодировка; ошибка появилась в 0.3.50.

    *) Исправление: ошибки "[alert] zero size buf" при работе с
       FastCGI-сервером;

    *) Исправление: параметр конфигурации --group= игнорировался.
       Спасибо Thomas Moschny.

    *) Исправление: 50-й подзапрос в SSI ответе не работал; ошибка появилась
       в 0.3.50.


Изменения в nginx 0.4.12                                          31.10.2006

    *) Добавление: модуль ngx_http_perl_module поддерживает метод
       $r->variable.

    *) Исправление: при включении в ответ большого статического файла с
       помощью SSI ответ мог передаваться не полностью.

    *) Исправление: nginx не убирал "#fragment" в URI.


Изменения в nginx 0.4.11                                          25.10.2006

    *) Добавление: POP3 прокси поддерживает AUTH LOGIN PLAIN и CRAM-MD5.

    *) Добавление: модуль ngx_http_perl_module поддерживает метод
       $r->allow_ranges.

    *) Исправление: при включённой поддержке команды APOP в POP3 прокси
       могли не работать команды USER/PASS; ошибка появилась в 0.4.10.


Изменения в nginx 0.4.10                                          23.10.2006

    *) Добавление: POP3 прокси поддерживает APOP.

    *) Исправление: при использовании методов select, poll и /dev/poll во
       время ожидания ответа от сервера аутентификации IMAP/POP3 прокси
       нагружал процессор.

    *) Исправление: при использовании переменной $server_addr в директиве
       map мог произойти segmentation fault.

    *) Исправление: модуль ngx_http_flv_module не поддерживал byte ranges
       для полных ответов; ошибка появилась в 0.4.7.

    *) Исправление: nginx не собирался на Debian amd64; ошибка появилась в
       0.4.9.


Изменения в nginx 0.4.9                                           13.10.2006

    *) Добавление: параметр set в команде SSI include.

    *) Добавление: модуль ngx_http_perl_module теперь проверяет версию
       модуля nginx.pm.


Изменения в nginx 0.4.8                                           11.10.2006

    *) Исправление: если до команды SSI include с параметром wait
       выполнялась ещё одна команда SSI include, то параметр wait мог не
       работать.

    *) Исправление: модуль ngx_http_flv_module добавлял FLV-заголовок для
       полных ответов.
       Спасибо Алексею Ковырину.


Изменения в nginx 0.4.7                                           10.10.2006

    *) Добавление: модуль ngx_http_flv_module.

    *) Добавление: переменная $request_body_file.

    *) Добавление: директивы charset и source_charset поддерживают
       переменные.

    *) Исправление: если до команды SSI include с параметром wait
       выполнялась ещё одна команда SSI include, то параметр wait мог не
       работать.

    *) Исправление: при использовании директивы "proxy_buffering off" или
       при работе с memcached соединения могли не закрываться по таймауту.

    *) Исправление: nginx не запускался на 64-битных платформах, отличных от
       amd64, sparc64 и ppc64.


Изменения в nginx 0.4.6                                           06.10.2006

    *) Исправление: nginx не запускался на 64-битных платформах, отличных от
       amd64, sparc64 и ppc64.

    *) Исправление: при запросе версии HTTP/1.1 nginx передавал ответ
       chunk'ами, если длина ответа в методе
       $r->headers_out("Content-Length", ...) была задана текстовой строкой.

    *) Исправление: после перенаправления ошибки с помощью директивы
       error_page любая директива модуля ngx_http_rewrite_module возвращала
       эту ошибку; ошибка появилась в 0.4.4.


Изменения в nginx 0.4.5                                           02.10.2006

    *) Исправление: nginx не собирался на Linux и Solaris; ошибка появилась
       в 0.4.4.


Изменения в nginx 0.4.4                                           02.10.2006

    *) Добавление: переменная $scheme.

    *) Добавление: директива expires поддерживает параметр max.

    *) Добавление: директива include поддерживает маску "*".
       Спасибо Jonathan Dance.

    *) Исправление: директива return всегда изменяла код ответа,
       перенаправленного директивой error_page.

    *) Исправление: происходил segmentation fault, если в методе PUT
       передавалось тело нулевой длины.

    *) Исправление: при использовании переменных в директиве proxy_redirect
       редирект изменялся неверно.


Изменения в nginx 0.4.3                                           26.09.2006

    *) Изменение: ошибку 499 теперь нельзя перенаправить с помощью директивы
       error_page.

    *) Добавление: поддержка Solaris 10 event ports.

    *) Добавление: модуль ngx_http_browser_module.

    *) Исправление: при перенаправлении ошибки 400 проксированному серверу
       помощью директивы error_page мог произойти segmentation fault.

    *) Исправление: происходил segmentation fault, если в директиве
       proxy_pass использовался unix domain сокет; ошибка появилась в
       0.3.47.

    *) Исправление: SSI не работал с ответами memcached и
       небуферизированными проксированными ответами.

    *) Изменение: обход ошибки PAUSE hardware capability в Sun Studio.


Изменения в nginx 0.4.2                                           14.09.2006

    *) Исправление: убрана поддержка флага O_NOATIME на Linux; ошибка
       появилась в 0.4.1.


Изменения в nginx 0.4.1                                           14.09.2006

    *) Исправление: совместимость с DragonFlyBSD.
       Спасибо Павлу Назарову.

    *) Изменение: обход ошибки в sendfile() в 64-битном Linux при передаче
       файлов больше 2G.

    *) Добавление: теперь на Linux nginx для статических запросов использует
       флаг O_NOATIME.
       Спасибо Yusuf Goolamabbas.


Изменения в nginx 0.4.0                                           30.08.2006

    *) Изменение во внутреннем API: инициализация модулей HTTP перенесена из
       фазы init module в фазу HTTP postconfiguration.

    *) Изменение: теперь тело запроса в модуле ngx_http_perl_module не
       считывается заранее: нужно явно инициировать чтение с помощью метода
       $r->has_request_body.

    *) Добавление: модуль ngx_http_perl_module поддерживает код возврата
       DECLINED.

    *) Добавление: модуль ngx_http_dav_module поддерживает входящую строку
       заголовка "Date" для метода PUT.

    *) Добавление: директива ssi работает внутри блока if.

    *) Исправление: происходил segmentation fault, если в директиве index
       использовалась переменные и при этом первое имя индексного файла было
       без переменных; ошибка появилась в 0.1.29.


Изменения в nginx 0.3.61                                          28.08.2006

    *) Изменение: директива tcp_nodelay теперь по умолчанию включена.

    *) Добавление: директива msie_refresh.

    *) Добавление: директива recursive_error_pages.

    *) Исправление: директива rewrite возвращала неправильный редирект, если
       редирект включал в себя выделенные закодированные символы из
       оригинального URI.


Изменения в nginx 0.3.60                                          18.08.2006

    *) Исправление: во время перенаправления ошибки рабочий процесс мог
       зациклиться; ошибка появилась в 0.3.59.


Изменения в nginx 0.3.59                                          16.08.2006

    *) Добавление: теперь можно делать несколько перенаправлений через
       директиву error_page.

    *) Исправление: директива dav_access не поддерживала три параметра.

    *) Исправление: директива error_page не изменяла строку "Content-Type"
       после перенаправления с помощью "X-Accel-Redirect"; ошибка появилась
       в 0.3.58.


Изменения в nginx 0.3.58                                          14.08.2006

    *) Добавление: директива error_page поддерживает переменные.

    *) Изменение: теперь на Linux используется интерфейс procfs вместо
       sysctl.

    *) Изменение: теперь при использовании "X-Accel-Redirect" строка
       "Content-Type" наследуется из первоначального ответа.

    *) Исправление: директива error_page не перенаправляла ошибку 413.

    *) Исправление: завершающий "?" не удалял старые аргументы, если в
       переписанном URI не было новых аргументов.

    *) Исправление: nginx не запускался на 64-битной FreeBSD 7.0-CURRENT.


Изменения в nginx 0.3.57                                          09.08.2006

    *) Добавление: переменная $ssl_client_serial.

    *) Исправление: в операторе "!-e" в директиве if.
       Спасибо Андриану Буданцову.

    *) Исправление: при проверке клиентского сертификата nginx не передавал
       клиенту информацию о требуемых сертификатах.

    *) Исправление: переменная $document_root не поддерживала переменные в
       директиве root.


Изменения в nginx 0.3.56                                          04.08.2006

    *) Добавление: директива dav_access.

    *) Добавление: директива if поддерживает операторы "-d", "!-d", "-e",
       "!-e", "-x" и "!-x".

    *) Исправление: при записи в access_log некоторых передаваемых клиенту
       строк заголовков происходил segmentation fault, если запрос возвращал
       редирект.


Изменения в nginx 0.3.55                                          28.07.2006

    *) Добавление: параметр stub в команде SSI include.

    *) Добавление: команда SSI block.

    *) Добавление: скрипт unicode2nginx добавлен в contrib.

    *) Исправление: если root был задан только переменной, то корень
       задавался относительно префикса сервера.

    *) Исправление: если в запросе был "//" или "/.", и после этого
       закодированные символы в виде "%XX", то проксируемый запрос
       передавался незакодированным.

    *) Исправление: метод $r->header_in("Cookie") модуля
       ngx_http_perl_module теперь возвращает все строки "Cookie" в
       заголовке запроса.

    *) Исправление: происходил segmentation fault, если использовался
       "client_body_in_file_only on" и делался переход к следующему бэкенду.

    *) Исправление: при некоторых условиях во время переконфигурации коды
       символов внутри директивы charset_map могли считаться неверными;
       ошибка появилась в 0.3.50.


Изменения в nginx 0.3.54                                          11.07.2006

    *) Добавление: nginx теперь записывает в лог информацию о подзапросах.

    *) Добавление: директивы proxy_next_upstream, fastcgi_next_upstream и
       memcached_next_upstream поддерживают параметр off.

    *) Добавление: директива debug_connection поддерживает запись адресов в
       формате CIDR.

    *) Исправление: при перекодировании ответа проксированного сервера или
       сервера FastCGI в UTF-8 или наоборот ответ мог передаваться не
       полностью.

    *) Исправление: переменная $upstream_response_time содержала время
       только первого обращения к бэкенду.

    *) Исправление: nginx не собирался на платформе amd64; ошибка появилась
       в 0.3.53.


Изменения в nginx 0.3.53                                          07.07.2006

    *) Изменение: директива add_header добавляет строки в ответы с кодом
       204, 301 и 302.

    *) Добавление: директива server в блоке upstream поддерживает параметр
       weight.

    *) Добавление: директива server_name поддерживает маску "*".

    *) Добавление: nginx поддерживает тело запроса больше 2G.

    *) Исправление: если при использовании "satisfy_any on" клиент успешно
       проходил аутентификацию, в лог всё равно записалоcь сообщение "access
       forbidden by rule".

    *) Исправление: метод PUT мог ошибочно не создать файл и вернуть код
       409.

    *) Исправление: если во время аутентификации IMAP/POP3 бэкенд возвращал
       ошибку, nginx продолжал проксирование.


Изменения в nginx 0.3.52                                          03.07.2006

    *) Изменение: восстановлено поведение модуля ngx_http_index_module для
       запросов "POST /": как в версии до 0.3.40, модуль теперь не выдаёт
       ошибку 405.

    *) Исправление: при использовании ограничения скорости рабочий процесс
       мог зациклиться; ошибка появилась в 0.3.37.

    *) Исправление: модуль ngx_http_charset_module записывал в лог ошибку
       "unknown charset", даже если перекодировка не требовалась; ошибка
       появилась в 0.3.50.

    *) Исправление: если в результате запроса PUT возвращался код 409, то
       временный файл не удалялся.


Изменения в nginx 0.3.51                                          30.06.2006

    *) Исправление: при некоторых условиях в SSI мог пропадать символы "<";
       ошибка появилась в 0.3.50.


Изменения в nginx 0.3.50                                          28.06.2006

    *) Изменение: директивы proxy_redirect_errors и fastcgi_redirect_errors
       переименованы соответственно в proxy_intercept_errors и
       fastcgi_intercept_errors.

    *) Добавление: модуль ngx_http_charset_module поддерживает
       перекодирование из однобайтных кодировок в UTF-8 и обратно.

    *) Добавление: в режиме прокси и FastCGI поддерживается строка заголовка
       "X-Accel-Charset" в ответе бэкенда.

    *) Исправление: символ "\" в парах "\"" и "\'" в SSI командах убирался,
       только если также использовался символ "$".

    *) Исправление: при некоторых условиях в SSI после вставки могла быть
       добавлена строка "<!--".

    *) Исправление: если в заголовке ответа была строка "Content-Length: 0",
       то при использовании небуферизированного проксировании не закрывалось
       соединение с клиентом.


Изменения в nginx 0.3.49                                          31.05.2006

    *) Исправление: в директиве set.

    *) Исправление: при включении в ssi двух и более подзапросов,
       обрабатываемых через FastCGI, вместо вывода второго и остальных
       подзапросов в ответ включался вывод первого подзапроса.


Изменения в nginx 0.3.48                                          29.05.2006

    *) Изменение: теперь модуль ngx_http_charset_module работает для
       подзапросов, в ответах которых нет строки заголовка "Content-Type".

    *) Исправление: если в директиве proxy_pass не было URI, то директива
       "proxy_redirect default" добавляла в переписанный редирект в начало
       лишний слэш.

    *) Исправление: внутренний редирект всегда превращал любой HTTP-метод в
       GET, теперь это делается только для редиректов, выполняемых с помощью
       X-Accel-Redirect, и у которых метод не равен HEAD; ошибка появилась в
       0.3.42.

    *) Исправление: модуль ngx_http_perl_module не собирался, если перл был
       с поддержкой потоков; ошибка появилась в 0.3.46.


Изменения в nginx 0.3.47                                          23.05.2006

    *) Добавление: директива upstream.

    *) Изменение: символ "\" в парах "\"" и "\'" в SSI командах теперь
       всегда убирается.


Изменения в nginx 0.3.46                                          11.05.2006

    *) Добавление: директивы proxy_hide_header, proxy_pass_header,
       fastcgi_hide_header и fastcgi_pass_header.

    *) Изменение: директивы proxy_pass_x_powered_by, fastcgi_x_powered_by и
       proxy_pass_server упразднены.

    *) Добавление: в режиме прокси поддерживается строка заголовка
       "X-Accel-Buffering" в ответе бэкенда.

    *) Исправление: ошибок и утечек памяти при переконфигурации в модуле
       ngx_http_perl_module.


Изменения в nginx 0.3.45                                          06.05.2006

    *) Добавление: директивы ssl_verify_client, ssl_verify_depth и
       ssl_client_certificate.

    *) Изменение: теперь переменная $request_method возвращает метод только
       основного запроса.

    *) Изменение: в таблице перекодировки koi-win изменены коды символа
       &deg;.

    *) Добавление: в таблицу перекодировки koi-win добавлены символы евро и
       номера.

    *) Исправление: если nginx распределял запросы на несколько машин, то
       при падении одной из них запросы, предназначенные для этой машины,
       перенаправлялись только на одну машину вместо того, чтобы равномерно
       распределяться между остальными.


Изменения в nginx 0.3.44                                          04.05.2006

    *) Добавление: параметр wait в команде SSI include.

    *) Добавление: в таблицу перекодировки koi-win добавлены украинские и
       белорусские символы.

    *) Исправление: в SSI.


Изменения в nginx 0.3.43                                          26.04.2006

    *) Исправление: в SSI.


Изменения в nginx 0.3.42                                          26.04.2006

    *) Добавление: параметр bind в директиве listen в IMAP/POP3 прокси.

    *) Исправление: ошибки при использовании в директиве rewrite одного и
       того же выделения более одного раза.

    *) Исправление: в лог не записывались переменные
       $sent_http_content_type, $sent_http_content_length,
       $sent_http_last_modified, $sent_http_connection,
       $sent_http_keep_alive и $sent_http_transfer_encoding.

    *) Исправление: переменная $sent_http_cache_control возвращала
       содержимое только одной строки "Cache-Control" в заголовке ответа.


Изменения в nginx 0.3.41                                          21.04.2006

    *) Добавление: ключ -v.

    *) Исправление: при включении в SSI удалённых подзапросов мог произойти
       segmentation fault.

    *) Исправление: в обработке FastCGI.

    *) Исправление: если путь к перловым модулям не был указан с помощью
       --with-perl_modules_path=PATH или директивы perl_modules, то на
       старте происходил segmentation fault.


Изменения в nginx 0.3.40                                          19.04.2006

    *) Добавление: модуль ngx_http_dav_module поддерживает метод MKCOL.

    *) Добавление: директива create_full_put_path.

    *) Добавление: переменная $limit_rate.


Изменения в nginx 0.3.39                                          17.04.2006

    *) Добавление: директива uninitialized_variable_warn; уровень
       логгирования сообщения о неинициализированной переменной понижен с
       уровня alert на warn.

    *) Добавление: директива override_charset.

    *) Изменение: при использовании неизвестной переменной в SSI-командах
       echo и if expr='$name' теперь не записывается в лог сообщение о
       неизвестной переменной.

    *) Исправление: счётчик активных соединений рос при превышении лимита
       соединений, заданного директивой worker_connections; ошибка появилась
       в 0.2.0.

    *) Исправление: при некоторых условия ограничение скорости соединения
       могло не работать; ошибка появилась в 0.3.38.


Изменения в nginx 0.3.38                                          14.04.2006

    *) Добавление: модуль ngx_http_dav_module.

    *) Изменение: оптимизация модуля ngx_http_perl_module.
       Спасибо Сергею Скворцову.

    *) Добавление: модуль ngx_http_perl_module поддерживает метод
       $r->request_body_file.

    *) Добавление: директива client_body_in_file_only.

    *) Изменение: теперь при переполнении диска nginx пытается писать
       access_log'и только раз в секунду.
       Спасибо Антону Южанинову и Максиму Дунину.

    *) Исправление: теперь директива limit_rate точнее ограничивает скорость
       при значениях больше 100 Kbyte/s.
       Спасибо ForJest.

    *) Исправление: IMAP/POP3 прокси теперь передаёт серверу авторизации
       символы "\r" и "\n" в логине и пароле в закодированном виде.
       Спасибо Максиму Дунину.


Изменения в nginx 0.3.37                                          07.04.2006

    *) Добавление: директива limit_except.

    *) Добавление: директива if поддерживает операторы "!~", "!~*", "-f" и
       "!-f".

    *) Добавление: модуль ngx_http_perl_module поддерживает метод
       $r->request_body.

    *) Исправление: в модуле ngx_http_addition_filter_module.


Изменения в nginx 0.3.36                                          05.04.2006

    *) Добавление: модуль ngx_http_addition_filter_module.

    *) Добавление: директивы proxy_pass и fastcgi_pass можно использовать
       внутри блока if.

    *) Добавление: директивы proxy_ignore_client_abort и
       fastcgi_ignore_client_abort.

    *) Добавление: переменная $request_completion.

    *) Добавление: модуль ngx_http_perl_module поддерживает методы
       $r->request_method и $r->remote_addr.

    *) Добавление: модуль ngx_http_ssi_module поддерживает команду elif.

    *) Исправление: строка "\/" в начале выражения команды if модуля
       ngx_http_ssi_module воспринималась неверно.

    *) Исправление: в использовании регулярных выражениях в команде if
       модуля ngx_http_ssi_module.

    *) Исправление: при задании относительного пути в директивах
       client_body_temp_path, proxy_temp_path, fastcgi_temp_path и
       perl_modules использовался каталог относительно текущего каталога, а
       не относительно префикса сервера.


Изменения в nginx 0.3.35                                          22.03.2006

    *) Исправление: accept-фильтр и TCP_DEFER_ACCEPT устанавливались только
       для первой директивы listen; ошибка появилась в 0.3.31.

    *) Исправление: в директиве proxy_pass без URI при использовании в
       подзапросе.


Изменения в nginx 0.3.34                                          21.03.2006

    *) Добавление: директива add_header поддерживает переменные.


Изменения в nginx 0.3.33                                          15.03.2006

    *) Добавление: параметр http_503 в директивах proxy_next_upstream или
       fastcgi_next_upstream.

    *) Исправление: ngx_http_perl_module не работал со встроенным в
       конфигурационный файл кодом, если он не начинался сразу же с "sub".

    *) Исправление: в директиве post_action.


Изменения в nginx 0.3.32                                          11.03.2006

    *) Исправление: удаление отладочного логгирования на старте и при
       переконфигурации; ошибка появилась в 0.3.31.


Изменения в nginx 0.3.31                                          10.03.2006

    *) Изменение: теперь nginx передаёт неверные ответы проксированного
       бэкенда.

    *) Добавление: директивы listen поддерживают адрес в виде "*:порт".

    *) Добавление: поддержка EVFILER_TIMER в MacOSX 10.4.

    *) Изменение: обход ошибки обработки миллисекундных таймаутов kqueue в
       64-битном ядре MacOSX.
       Спасибо Андрею Нигматулину.

    *) Исправление: если внутри одного сервера описаны несколько директив
       listen, слушающих на разных адресах, то имена серверов вида
       "*.domain.tld" работали только для первого адреса; ошибка появилась в
       0.3.18.

    *) Исправление: при использовании протокола HTTPS в директиве proxy_pass
       не передавались запросы с телом, записанным во временный файл.

    *) Исправление: совместимость с perl 5.8.8.


Изменения в nginx 0.3.30                                          22.02.2006

    *) Изменение: уровень записи в лог ошибки ECONNABORTED изменён на error
       с уровня crit.

    *) Исправление: модуль ngx_http_perl_module не собирался без модуля
       ngx_http_ssi_filter_module.

    *) Исправление: nginx не собирался на i386 платформе, если использовался
       PIC; ошибка появилась в 0.3.27.


Изменения в nginx 0.3.29                                          20.02.2006

    *) Добавление: теперь nginx использует меньше памяти, если PHP в режиме
       FastCGI передаёт большое количество предупреждений перед ответом.

    *) Исправление: в ответах 204 для запросов версии HTTP/1.1 выдавалась
       строка заголовка "Transfer-Encoding: chunked".

    *) Исправление: nginx возвращал 502 код ответа, если FastCGI сервер
       передавал полные строки заголовка ответа в отдельных FastCGI записях.

    *) Исправление: если в директиве post_action был указан проксируемый
       URI, то он выполнялся только после успешного завершения запроса.


Изменения в nginx 0.3.28                                          16.02.2006

    *) Добавление: директива restrict_host_names упразднена.

    *) Добавление: параметр конфигурации --with-cpu-opt=ppc64.

    *) Исправление: при некоторых условиях проксированное соединение с
       клиентом завершалось преждевременно.
       Спасибо Владимиру Шутову.

    *) Исправление: строка заголовка "X-Accel-Limit-Rate" не учитывалась для
       запросов, перенаправленных с помощью строки "X-Accel-Redirect".

    *) Исправление: директива post_action работала только после успешного
       завершения запроса.

    *) Исправление: тело проксированного ответа, создаваемого директивой
       post_action, передавалось клиенту.


Изменения в nginx 0.3.27                                          08.02.2006

    *) Изменение: директивы variables_hash_max_size и
       variables_hash_bucket_size.

    *) Добавление: переменная $body_bytes_sent доступна не только в
       директиве log_format.

    *) Добавление: переменные $ssl_protocol и $ssl_cipher.

    *) Добавление: определение размера строки кэша распространённых
       процессоров при старте.

    *) Добавление: директива accept_mutex теперь поддерживается посредством
       fcntl(2) на платформах, отличных от i386, amd64, sparc64 и ppc.

    *) Добавление: директива lock_file и параметр автоконфигурации
       --with-lock-path=PATH.

    *) Исправление: при использовании протокола HTTPS в директиве proxy_pass
       не передавались запросы с телом.


Изменения в nginx 0.3.26                                          03.02.2006

    *) Изменение: директива optimize_host_names переименована в
       optimize_server_names.

    *) Исправление: при проксировании подзапроса в SSI бэкенду передавался
       URI основного запроса, если в директиве proxy_pass отсутствовал URI.


Изменения в nginx 0.3.25                                          01.02.2006

    *) Исправление: при неверной конфигурации на старте или во время
       переконфигурации происходил segmentation fault; ошибка появилась в
       0.3.24.


Изменения в nginx 0.3.24                                          01.02.2006

    *) Изменение: обход ошибки в kqueue во FreeBSD.

    *) Исправление: ответ, создаваемый директивой post_action, теперь не
       передаётся клиенту.

    *) Исправление: при использовании большого количества лог-файлов
       происходила утечка памяти.

    *) Исправление: внутри одного location работала только первая директива
       proxy_redirect.

    *) Исправление: на 64-битных платформах при старте мог произойти
       segmentation fault, если использовалось большое количество имён в
       директивах server_name; ошибка появилась в 0.3.18.


Изменения в nginx 0.3.23                                          24.01.2006

    *) Добавление: директива optimize_host_names.

    *) Исправление: ошибки при использовании переменных в директивах path и
       alias.

    *) Исправление: модуль ngx_http_perl_module неправильно собирался на
       Linux и Solaris.


Изменения в nginx 0.3.22                                          17.01.2006

    *) Добавление: модуль ngx_http_perl_module поддерживает методы $r->args
       и $r->unescape.

    *) Добавление: метод $r->query_string в модуле ngx_http_perl_module
       упразднён.

    *) Исправление: если в директиве valid_referers указаны только none или
       blocked, то происходил segmentation fault; ошибка появилась в 0.3.18.


Изменения в nginx 0.3.21                                          16.01.2006

    *) Добавление: модуль ngx_http_perl_module.

    *) Изменение: директива valid_referers разрешает использовать рефереры
       совсем без URI.


Изменения в nginx 0.3.20                                          11.01.2006

    *) Исправление: ошибки в обработке SSI.

    *) Исправление: модуль ngx_http_memcached_module не поддерживал ключи в
       виде /uri?args.


Изменения в nginx 0.3.19                                          28.12.2005

    *) Добавление: директивы path и alias поддерживают переменные.

    *) Изменение: теперь директива valid_referers опять учитывает URI.

    *) Исправление: ошибки в обработке SSI.


Изменения в nginx 0.3.18                                          26.12.2005

    *) Добавление: директива server_names поддерживает имена вида
       ".domain.tld".

    *) Добавление: директива server_names использует хэш для имён вида
       "*.domain.tld" и более эффективный хэш для обычных имён.

    *) Изменение: директивы server_names_hash_max_size и
       server_names_hash_bucket_size.

    *) Изменение: директивы server_names_hash и server_names_hash_threshold
       упразднены.

    *) Добавление: директива valid_referers использует хэш для имён сайтов.

    *) Изменение: теперь директива valid_referers проверяет только имена
       сайтов без учёта URI.

    *) Исправление: некоторые имена вида ".domain.tld" неверно
       обрабатывались модулем ngx_http_map_module.

    *) Исправление: если конфигурационного файла не было, то происходил
       segmentation fault; ошибка появилась в 0.3.12.

    *) Исправление: на 64-битных платформах при старте мог произойти
       segmentation fault; ошибка появилась в 0.3.16.


Изменения в nginx 0.3.17                                          18.12.2005

    *) Изменение: на Linux configure теперь проверяет наличие epoll и
       sendfile64() в ядре.

    *) Добавление: директива map поддерживает доменные имена в формате
       ".domain.tld".

    *) Исправление: во время SSL handshake не иcпользовались таймауты;
       ошибка появилась в 0.2.4.

    *) Исправление: в использовании протокола HTTPS в директиве proxy_pass.

    *) Исправление: при использовании протокола HTTPS в директиве proxy_pass
       по умолчанию использовался порт 80.


Изменения в nginx 0.3.16                                          16.12.2005

    *) Добавление: модуль ngx_http_map_module.

    *) Добавление: директивы types_hash_max_size и types_hash_bucket_size.

    *) Добавление: директива ssi_value_length.

    *) Добавление: директива worker_rlimit_core.

    *) Изменение: при сборке компиляторами icc 8.1 и 9.0 с оптимизацией для
       Pentium 4 номер соединения в логах всегда был равен 1.

    *) Исправление: команда config timefmt в SSI задавала неверный формат
       времени.

    *) Исправление: nginx не закрывал соединения с IMAP/POP3 бэкендом при
       использовании SSL соединений; ошибка появилась в 0.3.13.
       Спасибо Rob Mueller.

    *) Исправление: segmentation fault мог произойти во время SSL shutdown;
       ошибка появилась в 0.3.13.


Изменения в nginx 0.3.15                                          07.12.2005

    *) Добавление: новой код 444 в директиве return для закрытия соединения.

    *) Добавление: директива so_keepalive в IMAP/POP3 прокси.

    *) Исправление: nginx теперь вызывает abort() при обнаружении незакрытых
       соединений только при плавном выходе и включённой директиве
       debug_points.


Изменения в nginx 0.3.14                                          05.12.2005

    *) Исправление: в ответе 304 передавалось тело ответа; ошибка появилась
       в 0.3.13.


Изменения в nginx 0.3.13                                          05.12.2005

    *) Добавление: IMAP/POP3 прокси поддерживает STARTTLS и STLS.

    *) Исправление: IMAP/POP3 прокси не работала с методами select, poll и
       /dev/poll.

    *) Исправление: ошибки в обработке SSI.

    *) Исправление: sendfilev() в Solaris теперь не используется при
       передаче тела запроса FastCGI-серверу через unix domain сокет.

    *) Исправление: директива auth_basic не запрещала аутентификацию; ошибка
       появилась в 0.3.11.


Изменения в nginx 0.3.12                                          26.11.2005

    *) Безопасность: если nginx был собран с модулем ngx_http_realip_module,
       то при использовании директивы "satisfy_any on" директивы доступа и
       аутентификации не работали. Модуль ngx_http_realip_module не
       собирался и не собирается по умолчанию.

    *) Изменение: имя переменной "$time_gmt" изменено на "$time_local".

    *) Изменение: директивы proxy_header_buffer_size и
       fastcgi_header_buffer_size переименованы соответственно в
       proxy_buffer_size и fastcgi_buffer_size.

    *) Добавление: модуль ngx_http_memcached_module.

    *) Добавление: директива proxy_buffering.

    *) Исправление: изменение в работе с accept mutex при использовании
       метода rtsig; ошибка появилась в 0.3.0.

    *) Исправление: если клиент передал строку "Transfer-Encoding: chunked"
       в заголовке запроса, то nginx теперь выдаёт ошибку 411.

    *) Исправление: при наследовании директивы auth_basic с уровня http в
       строке "WWW-Authenticate" заголовка ответа выводился realm без текста
       "Basic realm".

    *) Исправление: если в директиве access_log был явно указан формат
       combined, то в лог записывались пустые строки; ошибка появилась в
       0.3.8.

    *) Исправление: nginx не работал на платформе sparc под любыми OS, кроме
       Solaris.

    *) Исправление: в директиве if теперь не нужно разделять пробелом строку
       в кавычках и закрывающую скобку.


Изменения в nginx 0.3.11                                          15.11.2005

    *) Исправление: nginx не передавал при проксировании тело запроса и
       строки заголовка клиента; ошибка появилась в 0.3.10.


Изменения в nginx 0.3.10                                          15.11.2005

    *) Изменение: директива valid_referers и переменная $invalid_referer
       перенесены из модуля ngx_http_rewrite_module в новый модуль
       ngx_http_referer_module.

    *) Изменение: имя переменной "$apache_bytes_sent" изменено на
       "$body_bytes_sent".

    *) Добавление: переменные "$sent_http_...".

    *) Добавление: директива if поддерживает операции "=" и "!=".

    *) Добавление: директива proxy_pass поддерживает протокол HTTPS.

    *) Добавление: директива proxy_set_body.

    *) Добавление: директива post_action.

    *) Добавление: модуль ngx_http_empty_gif_module.

    *) Добавление: директива worker_cpu_affinity для Linux.

    *) Исправление: директива rewrite не раскодировала символы в редиректах
       в URI, теперь символы раскодируются, кроме символов %00-%25 и
       %7F-%FF.

    *) Исправление: nginx не собирался компилятором icc 9.0.

    *) Исправление: если для статического файла нулевого размера был
       разрешён SSI, то ответ передавался неверно при кодировании chunk'ами.


Изменения в nginx 0.3.9                                           10.11.2005

    *) Исправление: nginx считал небезопасными URI, в которых между двумя
       слэшами находилось два любых символа; ошибка появилась в 0.3.8.


Изменения в nginx 0.3.8                                           09.11.2005

    *) Безопасность: nginx теперь проверят URI, полученные от бэкенда в
       строке "X-Accel-Redirect" в заголовке ответа, или в SSI файле на
       наличие путей "/../" и нулей.

    *) Изменение: nginx теперь не воспринимает пустое имя как правильное в
       строке "Authorization" в заголовке запроса.

    *) Добавление: директива ssl_session_timeout модулей ngx_http_ssl_module
       и ngx_imap_ssl_module.

    *) Добавление: директива auth_http_header модуля
       ngx_imap_auth_http_module.

    *) Добавление: директива add_header.

    *) Добавление: модуль ngx_http_realip_module.

    *) Добавление: новые переменные для использования в директиве
       log_format: $bytes_sent, $apache_bytes_sent, $status, $time_gmt,
       $uri, $request_time, $request_length, $upstream_status,
       $upstream_response_time, $gzip_ratio, $uid_got, $uid_set,
       $connection, $pipe и $msec. Параметры в виде "%name" скоро будут
       упразднены.

    *) Изменение: в директиве "if" ложными значениями переменных теперь
       являются пустая строка "" и строки, начинающиеся на "0".

    *) Исправление: при работает с проксированными или FastCGI-серверами
       nginx мог оставлять открытыми соединения и временные файлы с
       запросами клиентов.

    *) Исправление: рабочие процессы не сбрасывали буферизированные логи при
       плавном выходе.

    *) Исправление: если URI запроса изменялось с помощью rewrite, а затем
       запрос проксировался в location, заданном регулярным выражением, то
       бэкенду передавался неверный запрос; ошибка появилась в 0.2.6.

    *) Исправление: директива expires не удаляла уже установленную строку
       заголовка "Expires".

    *) Исправление: при использовании метода rtsig и нескольких рабочих
       процессах nginx мог перестать принимать запросы.

    *) Исправление: в SSI командах неверно обрабатывались строки "\"" и
       "\'".

    *) Исправление: если ответ заканчивался сразу же после SSI команды, то
       при использовании сжатия ответ передавался не до конца или не
       передавался вообще.


Изменения в nginx 0.3.7                                           27.10.2005

    *) Добавление: директива access_log поддерживает параметр buffer=.

    *) Исправление: nginx не собирался на платформах, отличных от i386,
       amd64, sparc и ppc; ошибка появилась в 0.3.2.


Изменения в nginx 0.3.6                                           24.10.2005

    *) Изменение: IMAP/POP3 прокси теперь не передаёт серверу авторизации
       пустой логин.

    *) Добавление: директива log_format поддерживает переменные в виде
       $name.

    *) Исправление: если хотя бы в одном сервере не было описано ни одной
       директивы listen, то nginx не слушал на 80 порту; ошибка появилась в
       0.3.3.

    *) Исправление: если в директиве proxy_pass отсутствовал URI, то всегда
       использовался порт 80.


Изменения в nginx 0.3.5                                           21.10.2005

    *) Исправление: если логин IMAP/POP3 менялся сервером авторизации, то
       мог произойти segmentation fault; ошибка появилась в 0.2.2.

    *) Исправление: accept mutex не работал, все соединения обрабатывались
       одним рабочим процессом; ошибка появилась в 0.3.3.

    *) Исправление: при использовании метода rtsig и директивы
       timer_resolution не работали таймауты.


Изменения в nginx 0.3.4                                           19.10.2005

    *) Исправление: nginx не собирался на Linux 2.4+ и MacOS X; ошибка
       появилась в 0.3.3.


Изменения в nginx 0.3.3                                           19.10.2005

    *) Изменение: параметры "bl" и "af" директивы listen переименованы в
       "backlog" и "accept_filter".

    *) Добавление: параметры "rcvbuf" и "sndbuf" в директиве listen.

    *) Изменение: параметр лога $msec теперь не требует дополнительного
       системного вызова gettimeofday().

    *) Добавление: ключ -t теперь проверяет директивы listen.

    *) Исправление: если в директиве listen был указан неверный адрес, то
       nginx после сигнала -HUP оставлял открытый сокет в состоянии CLOSED.

    *) Исправление: для индексных файлов, содержащих в имени переменную, мог
       неверно выставляться тип mime по умолчанию; ошибка появилась в 0.3.0.

    *) Добавление: директива timer_resolution.

    *) Добавление: параметр лога $upstream_response_time в миллисекундах.

    *) Исправление: временный файл с телом запроса клиента теперь удаляется
       сразу после того, как клиенту передан заголовок ответа.

    *) Исправление: совместимость с OpenSSL 0.9.6.

    *) Исправление: пути к файлам с SSL сертификатом и ключом не могли быть
       относительными.

    *) Исправление: директива ssl_prefer_server_ciphers не работала для
       модуля ngx_imap_ssl_module.

    *) Исправление: директива ssl_protocols позволяла задать только один
       протокол.


Изменения в nginx 0.3.2                                           12.10.2005

    *) Добавление: поддержка Sun Studio 10 C compiler.

    *) Добавление: директивы proxy_upstream_max_fails,
       proxy_upstream_fail_timeout, fastcgi_upstream_max_fails и
       fastcgi_upstream_fail_timeout.


Изменения в nginx 0.3.1                                           10.10.2005

    *) Исправление: во время переполнения очереди сигналов при использовании
       метода rtsig происходил segmentation fault; ошибка появилась в 0.2.0.

    *) Изменение: корректная обработка пар "\\", "\"", "\'" и "\$" в SSI.


Изменения в nginx 0.3.0                                           07.10.2005

    *) Изменение: убрано десятидневное ограничение времени работы рабочего
       процесса. Ограничение было введено из-за переполнения миллисекундных
       таймеров.


Изменения в nginx 0.2.6                                           05.10.2005

    *) Изменение: с 60 до 10 секунд уменьшено время повторного обращения к
       бэкенду при использовании распределения нагрузки.

    *) Изменение: директива proxy_pass_unparsed_uri упразднена, оригинальный
       запрос теперь передаётся, если в директиве proxy_pass отсутствует
       URI.

    *) Добавление: директива error_page поддерживает редиректы и позволяет
       более гибко менять код ошибки.

    *) Изменение: в проксированных подзапросах теперь игнорируется
       переданный charset.

    *) Исправление: если после изменения URI в блоке if для запроса не
       находилась новая конфигурация, то правила модуля
       ngx_http_rewrite_module выполнялись снова.

    *) Исправление: если директива set устанавливала переменную модуля
       ngx_http_geo_module в какой-либо части конфигурации, то эта
       переменная не была доступна в других частях конфигурации и выдавалась
       ошибка "using uninitialized variable"; ошибка появилась в 0.2.2.


Изменения в nginx 0.2.5                                           04.10.2005

    *) Изменение: дублирующее значение переменной модуля ngx_http_geo_module
       теперь выдаёт предупреждение и изменяет старое значение.

    *) Добавление: модуль ngx_http_ssi_module поддерживает команду set.

    *) Добавление: модуль ngx_http_ssi_module поддерживает параметр file в
       команде include.

    *) Добавление: модуль ngx_http_ssi_module поддерживает подстановку
       значений переменных в выражениях команды if.


Изменения в nginx 0.2.4                                           03.10.2005

    *) Добавление: модуль ngx_http_ssi_module поддерживает выражения
       "$var=text", "$var!=text", "$var=/text/" и "$var!=/text/" в команде
       if.

    *) Исправление: ошибки при проксировании location без слэша в конце;
       ошибка появилась в 0.1.44.

    *) Исправление: при использовании метода rtsig мог произойти
       segmentation fault; ошибка появилась в 0.2.0.


Изменения в nginx 0.2.3                                           30.09.2005

    *) Исправление: nginx не собирался без параметра --with-debug; ошибка
       появилась в 0.2.2.


Изменения в nginx 0.2.2                                           30.09.2005

    *) Добавление: команда config errmsg в модуле ngx_http_ssi_module.

    *) Изменение: переменные модуля ngx_http_geo_module можно переопределять
       директивой set.

    *) Добавление: директивы ssl_protocols и ssl_prefer_server_ciphers
       модулей ngx_http_ssl_module и ngx_imap_ssl_module.

    *) Исправление: ошибка в модуле ngx_http_autoindex_module при показе
       длинных имён файлов;

    *) Исправление: модуль ngx_http_autoindex_module теперь не показывает
       файлы, начинающиеся на точку.

    *) Исправление: если SSL handshake завершался с ошибкой, то это могло
       привести также к закрытию другого соединения.
       Спасибо Rob Mueller.

    *) Исправление: экспортные версии MSIE 5.x не могли соединиться по
       HTTPS.


Изменения в nginx 0.2.1                                           23.09.2005

    *) Исправление: если все бэкенды, используемые для балансировки
       нагрузки, оказывались в нерабочем состоянии после одной ошибки, то
       nginx мог зациклится; ошибка появилась в 0.2.0.


Изменения в nginx 0.2.0                                           23.09.2005

    *) Изменились имена pid-файлов, используемые во время обновления
       исполняемого файла. Ручное переименование теперь не нужно. Старый
       основной процесс добавляет к своему pid-файл суффикс ".oldbin" и
       запускает новый исполняемый файл. Новый основной процесс создаёт
       обычный pid-файл без суффикса ".newbin". Если новый основной процесс
       выходит, то старый процесс переименовывает свой pid-файл c суффиксом
       ".oldbin" в pid-файл без суффикса. При обновлении с версии 0.1.х до
       0.2.0 нужно учитывать, что оба процесса - старый 0.1.x и новый
       0.2.0 - используют pid-файл без суффиксов.

    *) Изменение: директива worker_connections, новое название директивы
       connections; директива теперь задаёт максимальное число соединений, а
       не максимально возможный номер дескриптора для сокета.

    *) Добавление: SSL поддерживает кэширование сессий в пределах одного
       рабочего процесса.

    *) Добавление: директива satisfy_any.

    *) Изменение: модули ngx_http_access_module и ngx_http_auth_basic_module
       не работают для подзапросов.

    *) Добавление: директивы worker_rlimit_nofile и
       worker_rlimit_sigpending.

    *) Исправление: если все бэкенды, используемые для балансировки
       нагрузки, оказывались в нерабочем состоянии после одной ошибки, то
       nginx не обращался к ним в течение 60 секунд.

    *) Исправление: в парсинге аргументов IMAP/POP3 команд.
       Спасибо Rob Mueller.

    *) Исправление: ошибки при использовании SSL в IMAP/POP3 прокси.

    *) Исправление: ошибки при использовании SSI и сжатия.

    *) Исправление: в ответах 304 не добавлялись строки заголовка ответа
       "Expires" и "Cache-Control".
       Спасибо Александру Кукушкину.


Изменения в nginx 0.1.45                                          08.09.2005

    *) Изменение: директива ssl_engine упразднена в модуле
       ngx_http_ssl_module и перенесена на глобальный уровень.

    *) Исправление: ответы с подзапросами, включённые с помощью SSI, не
       передавались через SSL соединение.

    *) Разные исправления в IMAP/POP3 прокси.


Изменения в nginx 0.1.44                                          06.09.2005

    *) Добавление: IMAP/POP3 прокси поддерживает SSL.

    *) Добавление: директива proxy_timeout модуля ngx_imap_proxy_module.

    *) Добавление: директива userid_mark.

    *) Добавление: значение переменной $remote_user определяется независимо
       от того, используется ли авторизация или нет.


Изменения в nginx 0.1.43                                          30.08.2005

    *) Добавление: listen(2) backlog в директиве listen можно менять по
       сигналу -HUP.

    *) Добавление: скрипт geo2nginx.pl добавлен в contrib.

    *) Изменение: параметры FastCGI с пустым значениями теперь передаются
       серверу.

    *) Исправление: если в ответе проксированного сервера или FastCGI
       сервера была строка "Cache-Control", то при использовании директивы
       expires происходил segmentation fault или рабочий процесс мог
       зациклится; в режиме прокси ошибка появилась в 0.1.29.


Изменения в nginx 0.1.42                                          23.08.2005

    *) Исправление: если URI запроса получался нулевой длины после обработки
       модулем ngx_http_rewrite_module, то в модуле ngx_http_proxy_module
       происходил segmentation fault или bus error.

    *) Исправление: директива limit_rate не работала внутри блока if; ошибка
       появилась в 0.1.38.


Изменения в nginx 0.1.41                                          25.07.2005

    *) Исправление: если переменная использовалась в файле конфигурации, то
       она не могла использоваться в SSI.


Изменения в nginx 0.1.40                                          22.07.2005

    *) Исправление: если клиент слал очень длинную строку заголовка, то в
       логе не помещалась информация, связанная с этим запросом.

    *) Исправление: при использовании "X-Accel-Redirect" не передавалась
       строка "Set-Cookie"; ошибка появилась в 0.1.39.

    *) Исправление: при использовании "X-Accel-Redirect" не передавалась
       строка "Content-Disposition".

    *) Исправление: по сигналу SIGQUIT основной процесс не закрывал сокеты,
       на которых он слушал.

    *) Исправление: после обновления исполняемого файла на лету на Linux и
       Solaris название процесса в команде ps становилось короче.


Изменения в nginx 0.1.39                                          14.07.2005

    *) Изменения в модуле ngx_http_charset_module: директива default_charset
       упразднена; директива charset задаёт кодировку ответа; директива
       source_charset задаёт только исходную кодировку.

    *) Исправление: при перенаправлении ошибки 401, полученной от бэкенда,
       не передавалась строка заголовка "WWW-Authenticate".

    *) Исправление: модули ngx_http_proxy_module и ngx_http_fastcgi_module
       могли закрыть соединение до того, как что-нибудь было передано
       клиенту; ошибка появилась в 0.1.38.

    *) Изменение: обработка ошибки инициализации в crypt_r() в Linux glibc.

    *) Исправление: модуль ngx_http_ssi_module не поддерживал относительные
       URI в команде include virtual.

    *) Исправление: если в строке заголовка ответа бэкенда была строка
       "Location", которую nginx не должен был изменять, то в ответе
       передавалось тело 500 ошибки; ошибка появилась в 0.1.29.

    *) Исправление: некоторые директивы модулей ngx_http_proxy_module и
       ngx_http_fastcgi_module не наследовались с уровня server на уровень
       location; ошибка появилась в 0.1.29.

    *) Исправление: модуль ngx_http_ssl_module не поддерживал цепочки
       сертификатов.

    *) Исправление: ошибка в модуле ngx_http_autoindex_module при показе
       длинных имён файлов; ошибка появилась в 0.1.38.

    *) Исправления в IMAP/POP3 прокси при взаимодействии с бэкендом на
       стадии login.


Изменения в nginx 0.1.38                                          08.07.2005

    *) Добавление: директива limit_rate поддерживается в режиме прокси и
       FastCGI.

    *) Добавление: в режиме прокси и FastCGI поддерживается строка заголовка
       "X-Accel-Limit-Rate" в ответе бэкенда.

    *) Добавление: директива break.

    *) Добавление: директива log_not_found.

    *) Исправление: при перенаправлении запроса с помощью строки заголовка
       "X-Accel-Redirect" не изменялся код ответа.

    *) Исправление: переменные, установленные директивой set не могли
       использоваться в SSI.

    *) Исправление: при включении в SSI более одного удалённого подзапроса
       мог произойти segmentation fault.

    *) Исправление: если статусная строка в ответе бэкенда передавалась в
       двух пакетах, то nginx считал ответ неверным; ошибка появилась в
       0.1.29.

    *) Добавление: директива ssi_types.

    *) Добавление: директива autoindex_exact_size.

    *) Исправление: модуль ngx_http_autoindex_module не поддерживал длинные
       имена файлов в UTF-8.

    *) Добавление: IMAP/POP3 прокси.


Изменения в nginx 0.1.37                                          23.06.2005

    *) Изменение: в конце файла nginx.pid теперь добавляется "\n".

    *) Исправление: при включении большого количества вставок или нескольких
       больших вставок с помощью SSI ответ мог передаваться не полностью.

    *) Исправление: если все бэкенды возвращали ответ 404, то при
       использовании параметра http_404 в директивах proxy_next_upstream или
       fastcgi_next_upstream, nginx начинал запрашивать все бэкенды снова.


Изменения в nginx 0.1.36                                          15.06.2005

    *) Изменение: если в заголовке запроса есть дублирующиеся строки "Host",
       "Connection", "Content-Length" и "Authorization", то nginx теперь
       выдаёт ошибку 400.

    *) Изменение: директива post_accept_timeout упразднена.

    *) Добавление: параметры default, af=, bl=, deferred и bind в директиве
       listen.

    *) Добавление: поддержка accept фильтров во FreeBSD.

    *) Добавление: поддержка TCP_DEFER_ACCEPT в Linux.

    *) Исправление: модуль ngx_http_autoindex_module не поддерживал имена
       файлов в UTF-8.

    *) Исправление: после добавления новый лог-файл ротация этого лога по
       сигналу -USR1 выполнялась, только если переконфигурировать nginx два
       раза по сигналу -HUP.


Изменения в nginx 0.1.35                                          07.06.2005

    *) Добавление: директива working_directory.

    *) Добавление: директива port_in_redirect.

    *) Исправление: если заголовок ответа бэкенда не помещался в один пакет,
       то происходил segmentation fault; ошибка появилась в 0.1.29.

    *) Исправление: если было сконфигурировано более 10 серверов или в
       сервере не описана директива "listen", то при запуске мог произойти
       segmentation fault.

    *) Исправление: если ответ не помещался во временный файл, то мог
       произойти segmentation fault.

    *) Исправление: nginx возвращал ошибку 400 на запросы вида
       "GET http://www.domain.com/uri HTTP/1.0"; ошибка появилась в 0.1.28.


Изменения в nginx 0.1.34                                          26.05.2005

    *) Исправление: при включении больших ответов с помощью SSI рабочий
       процесс мог зациклиться.

    *) Исправление: переменные, устанавливаемые директивой "set", не были
       доступны в SSI.

    *) Добавление: директива autoindex_localtime.

    *) Исправление: пустое значение в директиве proxy_set_header запрещает
       передачу заголовка.


Изменения в nginx 0.1.33                                          23.05.2005

    *) Исправление: nginx не собирался с параметром --without-pcre; ошибка
       появилась в 0.1.29.

    *) Исправление: 3, 5, 7 и 8 директив proxy_set_header на одном уровне
       вызывали bus fault при запуске.

    *) Исправление: в редиректах внутри HTTPS сервера был указан протокол
       HTTP.

    *) Исправление: если директива rewrite использовала выделения внутри
       директивы if, то возвращалась ошибка 500.


Изменения в nginx 0.1.32                                          19.05.2005

    *) Исправление: в редиректах, выдаваемых с помощью директивы rewrite, не
       передавались аргументы; ошибка появилась в 0.1.29.

    *) Добавление: директива if поддерживает выделения в регулярных
       выражениях.

    *) Добавление: директива set поддерживает переменные и выделения из
       регулярных выражений.

    *) Добавление: в режиме прокси и FastCGI поддерживается строка заголовка
       "X-Accel-Redirect" в ответе бэкенда.


Изменения в nginx 0.1.31                                          16.05.2005

    *) Исправление: при использовании SSL ответ мог передаваться не до
       конца.

    *) Исправление: ошибки при обработке SSI в ответе, полученного от
       FastCGI-сервера.

    *) Исправление: ошибки при использовании SSI и сжатия.

    *) Исправление: редирект с кодом 301 передавался без тела ответа; ошибка
       появилась в 0.1.30.


Изменения в nginx 0.1.30                                          14.05.2005

    *) Исправление: при использовании SSI рабочий процесс мог зациклиться.

    *) Исправление: при использовании SSL ответ мог передаваться не до
       конца.

    *) Исправление: если длина части ответа, полученного за один раз от
       проксируемого или FastCGI сервера была равна 500 байт, то nginx
       возвращал код ответа 500; в режиме прокси ошибка появилась только в
       0.1.29.

    *) Исправление: nginx не считал неверными директивы с 8-ю или 9-ю
       параметрами.

    *) Добавление: директива return может возвращать код ответа 204.

    *) Добавление: директива ignore_invalid_headers.


Изменения в nginx 0.1.29                                          12.05.2005

    *) Добавление: модуль ngx_http_ssi_module поддерживает команду include
       virtual.

    *) Добавление: модуль ngx_http_ssi_module поддерживает условную команду
       вида 'if expr="$NAME"' и команды else и endif. Допускается только
       один уровень вложенности.

    *) Добавление: модуль ngx_http_ssi_module поддерживает две переменные
       DATE_LOCAL и DATE_GMT и команду config timefmt.

    *) Добавление: директива ssi_ignore_recycled_buffers.

    *) Исправление: если переменная QUERY_STRING не была определена, то в
       команде echo не ставилось значение по умолчанию.

    *) Изменение: модуль ngx_http_proxy_module полностью переписан.

    *) Добавление: директивы proxy_redirect, proxy_pass_request_headers,
       proxy_pass_request_body и proxy_method.

    *) Добавление: директива proxy_set_header. Директива proxy_x_var
       упразднена и должна быть заменена директивой proxy_set_header.

    *) Изменение: директива proxy_preserve_host упразднена и должна быть
       заменена директивами "proxy_set_header Host $host" и "proxy_redirect
       off" или директивой "proxy_set_header Host $host:$proxy_port" и
       соответствующими ей директивами proxy_redirect.

    *) Изменение: директива proxy_set_x_real_ip упразднена и должна быть
       заменена директивой "proxy_set_header X-Real-IP $remote_addr".

    *) Изменение: директива proxy_add_x_forwarded_for упразднена и должна
       быть заменена директивой
       "proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for".

    *) Изменение: директива proxy_set_x_url упразднена и должна быть
       заменена директивой
       "proxy_set_header X-URL http://$host:$server_port$request_uri".

    *) Добавление: директива fastcgi_param.

    *) Изменение: директивы fastcgi_root, fastcgi_set_var и fastcgi_params
       упразднены и должны быть замены директивами fastcgi_param.

    *) Добавление: директива index может использовать переменные.

    *) Добавление: директива index может быть указана на уровне http и
       server.

    *) Изменение: только последний параметр в директиве index может быть
       абсолютным.

    *) Добавление: в директиве rewrite могут использоваться переменные.

    *) Добавление: директива internal.

    *) Добавление: переменные CONTENT_LENGTH, CONTENT_TYPE, REMOTE_PORT,
       SERVER_ADDR, SERVER_PORT, SERVER_PROTOCOL, DOCUMENT_ROOT,
       SERVER_NAME, REQUEST_METHOD, REQUEST_URI и REMOTE_USER.

    *) Изменение: nginx теперь передаёт неверные строки в заголовках запроса
       клиента и ответа бэкенда.

    *) Исправление: если бэкенд долго не передавал ответ и send_timeout был
       меньше, чем proxy_read_timeout, то клиенту возвращался ответ 408.

    *) Исправление: если бэкенд передавал неверную строку в заголовке
       ответа, то происходил segmentation fault; ошибка появилась в 0.1.26.

    *) Исправление: при использовании отказоустойчивой конфигурации в
       FastCGI мог происходить segmentation fault.

    *) Исправление: директива expires не удаляла уже установленные строки
       заголовка "Expires" и "Cache-Control".

    *) Исправление: nginx не учитывал завершающую точку в строке заголовка
       запроса "Host".

    *) Исправление: модуль ngx_http_auth_module не работал на Linux.

    *) Исправление: директива rewrite неверно работала, если в запросе
       присутствовали аргументы.

    *) Исправление: nginx не собирался на MacOS X.


Изменения в nginx 0.1.28                                          08.04.2005

    *) Исправление: при проксировании больших файлов nginx сильно нагружал
       процессор.

    *) Исправление: nginx не собирался gcc 4.0 на Linux.


Изменения в nginx 0.1.27                                          28.03.2005

    *) Добавление: параметр blocked в директиве valid_referers.

    *) Изменение: ошибки обработки заголовка запроса теперь записываются на
       уровне info, в лог также записывается имя сервера и строки заголовка
       запроса "Host" и "Referer".

    *) Изменение: при записи ошибок в лог записывается также строка
       заголовка запроса "Host".

    *) Добавление: директива proxy_pass_unparsed_uri. Специальная обработка
       символов "://" в URI, введённая в версии 0.1.11, теперь упразднена.

    *) Исправление: nginx не собирался на FreeBSD и Linux, если был указан
       параметр конфигурации --without-ngx_http_auth_basic_module.


Изменения в nginx 0.1.26                                          22.03.2005

    *) Изменение: неверные строки заголовка, переданные клиентом, теперь
       игнорируется и записываются в error_log на уровне info.

    *) Изменение: при записи ошибок в лог записывается также имя сервера,
       при обращении к которому произошла ошибка.

    *) Добавление: модуль ngx_http_auth_basic_module и директивы auth_basic
       и auth_basic_user_file.


Изменения в nginx 0.1.25                                          19.03.2005

    *) Исправление: nginx не работал на Linux parisc.

    *) Добавление: nginx теперь не запускается под FreeBSD, если значение
       sysctl kern.ipc.somaxconn слишком большое.

    *) Исправление: если модуль ngx_http_index_module делал внутреннее
       перенаправление запроса в модули ngx_http_proxy_module или
       ngx_http_fastcgi_module, то файл индекса не закрывался после
       обслуживания запроса.

    *) Добавление: директива proxy_pass может использоваться в location,
       заданных регулярным выражением.

    *) Добавление: модуль ngx_http_rewrite_filter_module поддерживает
       условия вида "if ($HTTP_USER_AGENT ~ MSIE)".

    *) Исправление: nginx очень медленно запускался при большом количестве
       адресов и использовании текстовых значений в директиве geo.

    *) Изменение: имя переменной в директиве geo нужно указывать, как $name.
       Прежний вариант без "$" пока работает, но вскоре будет убран.

    *) Добавление: параметр лога "%{VARIABLE}v".

    *) Добавление: директива "set $name value".

    *) Исправление: совместимость с gcc 4.0.

    *) Добавление: параметр автоконфигурации --with-openssl-opt=OPTIONS.


Изменения в nginx 0.1.24                                          04.03.2005

    *) Добавление: модуль ngx_http_ssi_filter_module поддерживает переменные
       QUERY_STRING и DOCUMENT_URI.

    *) Исправление: модуль ngx_http_autoindex_module мог выдавать ответ 404
       на существующий каталог, если этот каталог был указан как alias.

    *) Исправление: модуль ngx_http_ssi_filter_module неправильно работал
       при больших ответах.

    *) Исправление: отсутствие строки заголовка "Referer" всегда считалось
       правильным referrer'ом.


Изменения в nginx 0.1.23                                          01.03.2005

    *) Добавление: модуль ngx_http_ssi_filter_module и директивы ssi,
       ssi_silent_errors и ssi_min_file_chunk. Поддерживаются команды 'echo
       var="HTTP_..." default=""' и 'echo var="REMOTE_ADDR"'.

    *) Добавление: параметр лога %request_time.

    *) Добавление: если запрос пришёл без строки заголовка "Host", то
       директива proxy_preserve_host устанавливает в качестве этого
       заголовка первое имя сервера из директивы server_name.

    *) Исправление: nginx не собирался на платформах, отличных от i386,
       amd64, sparc и ppc; ошибка появилась в 0.1.22.

    *) Исправление: модуль ngx_http_autoindex_module теперь показывает
       информацию не о символическом линке, а о файле или каталоге, на
       который он указывает.

    *) Исправление: если клиенту ничего не передавалось, то параметр
       %apache_length записывал в лог отрицательную длину заголовка ответа.


Изменения в nginx 0.1.22                                          22.02.2005

    *) Исправление: модуль ngx_http_stub_status_module показывал неверную
       статистику для обработанных соединений, если использовалось
       проксирование или FastCGI-сервер.

    *) Исправление: на Linux и Solaris установочные пути были неверно
       заключены в кавычки; ошибка появилась в 0.1.21.


Изменения в nginx 0.1.21                                          22.02.2005

    *) Исправление: модуль ngx_http_stub_status_module показывал неверную
       статистику при использовании метода rtsig или при использовании
       нескольких рабочих процессов на SMP машине.

    *) Исправление: nginx не собирался компилятором icc под Линуксом или
       если библиотека zlib-1.2.x собиралась из исходных текстов.

    *) Исправление: nginx не собирался под NetBSD 2.0.


Изменения в nginx 0.1.20                                          17.02.2005

    *) Добавление: новые параметры script_filename и remote_port в директиве
       fastcgi_params.

    *) Исправление: неправильно обрабатывался поток stderr от
       FastCGI-сервера.


Изменения в nginx 0.1.19                                          16.02.2005

    *) Исправление: если в запросе есть нуль, то для локальных запросов
       теперь возвращается ошибка 404.

    *) Исправление: nginx не собирался под NetBSD 2.0.

    *) Исправление: во время чтения тела запроса клиента в SSL соединении
       мог произойти таймаут.


Изменения в nginx 0.1.18                                          09.02.2005

    *) Изменение: для совместимости с Solaris 10 в директивах devpoll_events
       и devpoll_changes значения по умолчанию уменьшены с 512 до 32.

    *) Исправление: директивы proxy_set_x_var и fastcgi_set_var не
       наследовались.

    *) Исправление: в директиве rewrite, возвращающей редирект, аргументы
       присоединялись к URI через символ "&" вместо "?".

    *) Исправление: строки для модуля ngx_http_geo_module без символа ";" во
       включённом файле игнорировались.

    *) Добавление: модуль ngx_http_stub_status_module.

    *) Исправление: неизвестный формат лог-файла в директиве access_log
       вызывал segmentation fault.

    *) Добавление: новый параметр document_root в директиве fastcgi_params.

    *) Добавление: директива fastcgi_redirect_errors.

    *) Добавление: новый модификатор break в директиве rewrite позволяет
       прекратить цикл rewrite/location и устанавливает текущую конфигурацию
       для запроса.


Изменения в nginx 0.1.17                                          03.02.2005

    *) Изменение: модуль ngx_http_rewrite_module полностью переписан. Теперь
       можно делать редиректы, возвращать коды ошибок и проверять переменные
       и рефереры. Эти директивы можно использовать внутри location.
       Директива redirect упразднена.

    *) Добавление: модуль ngx_http_geo_module.

    *) Добавление: директивы proxy_set_x_var и fastcgi_set_var.

    *) Исправление: конфигурация location с модификатором "=" могла
       использоваться в другом location.

    *) Исправление: правильный тип ответа выставлялся только для запросов, у
       которых в расширении были только маленькие буквы.

    *) Исправление: если для location установлен proxy_pass или
       fastcgi_pass, и доступ к нему запрещался, а ошибка перенаправлялась
       на статическую страницу, то происходил segmentation fault.

    *) Исправление: если в проксированном ответе в заголовке "Location"
       передавался относительный URL, то к нему добавлялось имя хоста и
       слэш; ошибка появилась в 0.1.14.

    *) Исправление: на Linux в лог не записывался текст системной ошибки.


Изменения в nginx 0.1.16                                          25.01.2005

    *) Исправление: если ответ передавался chunk'ами, то при запросе HEAD
       выдавался завершающий chunk.

    *) Исправление: заголовок "Connection: keep-alive" выдавался, даже если
       директива keepalive_timeout запрещала использование keep-alive.

    *) Исправление: ошибки в модуле ngx_http_fastcgi_module вызывали
       segmentation fault.

    *) Исправление: при использовании SSL сжатый ответ мог передаваться не
       до конца.

    *) Исправление: опции TCP_NODELAY, TCP_NOPUSH и TCP_CORK, специфичные
       для TCP сокетов, не используются для unix domain сокетов.

    *) Добавление: директива rewrite поддерживает перезаписывание
       аргументов.

    *) Исправление: на запрос POST с заголовком "Content-Length: 0"
       возвращался ответ 400; ошибка появилась в 0.1.14.


Изменения в nginx 0.1.15                                          19.01.2005

    *) Исправление: ошибка соединения с FastCGI-сервером вызывала
       segmentation fault.

    *) Исправление: корректная обработка регулярного выражения, в котором
       число выделенных частей не совпадает с числом подстановок.

    *) Добавление: location, который передаётся FastCGI-серверу, может быть
       задан с помощью регулярного выражения.

    *) Исправление: параметр FastCGI REQUEST_URI теперь передаётся вместе с
       аргументами и в том виде, в котором был получен от клиента.

    *) Исправление: для использования регулярных выражений в location нужно
       было собирать nginx вместе с ngx_http_rewrite_module.

    *) Исправление: если бэкенд слушал на 80-ом порту, то при использовании
       директивы "proxy_preserve_host on" в заголовке "Host" указывался
       также порт 80; ошибка появилась в 0.1.14.

    *) Исправление: если задать одинаковые пути в параметрах
       автоконфигурации --http-client-body-temp-path=PATH и
       --http-proxy-temp-path=PATH или --http-client-body-temp-path=PATH и
       --http-fastcgi-temp-path=PATH, то происходил segmentation fault.


Изменения в nginx 0.1.14                                          18.01.2005

    *) Добавление: параметры автоконфигурации
       --http-client-body-temp-path=PATH, --http-proxy-temp-path=PATH и
       --http-fastcgi-temp-path=PATH

    *) Изменение: имя каталога с временными файлами, содержащие тело запроса
       клиента, задаётся директивой client_body_temp_path, по умолчанию
       <prefix>/client_body_temp.

    *) Добавление: модуль ngx_http_fastcgi_module и директивы fastcgi_pass,
       fastcgi_root, fastcgi_index, fastcgi_params, fastcgi_connect_timeout,
       fastcgi_send_timeout, fastcgi_read_timeout, fastcgi_send_lowat,
       fastcgi_header_buffer_size, fastcgi_buffers,
       fastcgi_busy_buffers_size, fastcgi_temp_path,
       fastcgi_max_temp_file_size, fastcgi_temp_file_write_size,
       fastcgi_next_upstream и fastcgi_x_powered_by.

    *) Исправление: ошибка "[alert] zero size buf"; ошибка появилась в
       0.1.3.

    *) Изменение: в директиве proxy_pass нужно обязательно указывать URI
       после имени хоста.

    *) Изменение: если в URI встречался символ %3F, то он считался началом
       строки аргументов.

    *) Добавление: поддержка unix domain сокетов в модуле
       ngx_http_proxy_module.

    *) Добавление: директивы ssl_engine и ssl_ciphers.
       Спасибо Сергею Скворцову за SSL-акселератор.


Изменения в nginx 0.1.13                                          21.12.2004

    *) Добавление: директивы server_names_hash и
       server_names_hash_threshold.

    *) Исправление: имена *.domain.tld в директиве server_name не работали.

    *) Исправление: параметр лога %request_length записывал неверную длину.


Изменения в nginx 0.1.12                                          06.12.2004

    *) Добавление: параметр лога %request_length.

    *) Исправление: при использовании /dev/poll, select и poll на
       платформах, где возможны ложные срабатывания указанных методов, могли
       быть длительные задержки при обработке запроса по keep-alive
       соединению. Наблюдалось по крайней мере на Solaris с использованием
       /dev/poll.

    *) Исправление: директива send_lowat игнорируется на Linux, так как
       Linux не поддерживает опцию SO_SNDLOWAT.


Изменения в nginx 0.1.11                                          02.12.2004

    *) Добавление: директива worker_priority.

    *) Изменение: под FreeBSD директивы tcp_nopush и tcp_nodelay вместе
       влияют на передачу ответа.

    *) Исправление: nginx не вызывал initgroups().
       Спасибо Андрею Ситникову и Андрею Нигматулину.

    *) Изменение: ngx_http_auto_index_module теперь выдаёт размер файлов в
       байтах.

    *) Исправление: ngx_http_auto_index_module возвращал ошибку 500, если в
       каталоге есть битый symlink.

    *) Исправление: файлы больше 4G не передавались с использованием
       sendfile.

    *) Исправление: если бэкенд резолвился в несколько адресов и при
       ожидании от него ответа происходила ошибка, то процесс зацикливался.

    *) Исправление: при использовании метода /dev/poll рабочий процесс мог
       завершиться с сообщением "unknown cycle".

    *) Исправление: ошибки "close() channel failed".

    *) Исправление: автоматическое определение групп nobody и nogroup.

    *) Исправление: директива send_lowat не работала на Linux.

    *) Исправление: если в конфигурации не было раздела events, то
       происходил segmentation fault.

    *) Исправление: nginx не собирался под OpenBSD.

    *) Исправление: двойные слэшы в "://" в URI превращались в ":/".


Изменения в nginx 0.1.10                                          26.11.2004

    *) Исправление: если в запросе без аргументов есть "//", "/./", "/../"
       или "%XX", то терялся последний символ в строке запроса; ошибка
       появилась в 0.1.9.

    *) Исправление: исправление в версии 0.1.9 для файлов больше 2G на Linux
       не работало.


Изменения в nginx 0.1.9                                           25.11.2004

    *) Исправление: если в запросе есть "//", "/./", "/../" или "%XX", то
       проксируемый запрос передавался без аргументов.

    *) Исправление: при сжатии больших ответов иногда они передавались не
       полностью.

    *) Исправление: не передавались файлы больше 2G на Linux,
       неподдерживающем sendfile64().

    *) Исправление: на Linux при конфигурации сборки нужно было обязательно
       использовать параметр --with-poll_module; ошибка появилась в 0.1.8.


Изменения в nginx 0.1.8                                           20.11.2004

    *) Исправление: ошибка в модуле ngx_http_autoindex_module при показе
       длинных имён файлов.

    *) Добавление: модификатор "^~" в директиве location.

    *) Добавление: директива proxy_max_temp_file_size.


Изменения в nginx 0.1.7                                           12.11.2004

    *) Исправление: при использовании sendfile, если передаваемый файл
       менялся, то мог произойти segmentation fault на FreeBSD; ошибка
       появилась в 0.1.5.


Изменения в nginx 0.1.6                                           11.11.2004

    *) Исправление: при некоторых комбинациях директив location c
       регулярными выражениями использовалась конфигурация не из того
       location.


Изменения в nginx 0.1.5                                           11.11.2004

    *) Исправление: на Solaris и Linux могло быть очень много сообщений
       "recvmsg() returned not enough data".

    *) Исправление: в режиме прокси без использования sendfile на Solaris
       возникала ошибка "writev() failed (22: Invalid argument)". На других
       платформах, не поддерживающих sendfile, процесс зацикливался.

    *) Исправление: при использовании sendfile в режиме прокси на Solaris
       возникал segmentation fault.

    *) Исправление: segmentation fault на Solaris.

    *) Исправление: обновление исполняемого файла на лету не работало на
       Linux.

    *) Исправление: в списке файлов, выдаваемом модулем
       ngx_http_autoindex_module, не перекодировались пробелы, кавычки и
       знаки процента.

    *) Изменение: уменьшение операций копирования.

    *) Добавление: директива userid_p3p.


Изменения в nginx 0.1.4                                           26.10.2004

    *) Исправление: ошибка в модуле ngx_http_autoindex_module.


Изменения в nginx 0.1.3                                           25.10.2004

    *) Добавление: модуль ngx_http_autoindex_module и директива autoindex.

    *) Добавление: директива proxy_set_x_url.

    *) Исправление: модуль проксировании мог привести к зацикливанию, если
       не использовался sendfile.


Изменения в nginx 0.1.2                                           21.10.2004

    *) Добавление: параметры --user=USER, --group=GROUP и
       --with-ld-opt=OPTIONS в configure.

    *) Добавление: директива server_name поддерживает *.domain.tld.

    *) Исправление: улучшена переносимость на неизвестные платформы.

    *) Исправление: нельзя переконфигурировать nginx, если конфигурационный
       файл указан в командной строке; ошибка появилась в 0.1.1.

    *) Исправление: модуль проксировании мог привести к зацикливанию, если
       не использовался sendfile.

    *) Исправление: при использовании sendfile текст ответа не
       перекодировался согласно директивам модуля charset; ошибка появилась
       в 0.1.1.

    *) Исправление: очень редкая ошибка при обработке kqueue.

    *) Исправление: модуль сжатия сжимал уже сжатые ответы, полученные при
       проксировании.


Изменения в nginx 0.1.1                                           11.10.2004

    *) Добавление: директива gzip_types.

    *) Добавление: директива tcp_nodelay.

    *) Добавление: директива send_lowat работает не только на платформах,
       поддерживающих kqueue NOTE_LOWAT, но и на всех, поддерживающих
       SO_SNDLOWAT.

    *) Добавление: эмуляция setproctitle() для Linux и Solaris.

    *) Исправление: ошибка при переписывании заголовка "Location" при
       проксировании.

    *) Исправление: ошибка в модуле ngx_http_chunked_module, приводившая к
       зацикливанию.

    *) Исправление: ошибки в модуле /dev/poll.

    *) Исправление: при проксировании и использовании временных файлов
       ответы портились.

    *) Исправление: бэкенду передавались запросы с неперекодированными
       символами.

    *) Исправление: на Linux 2.4 при конфигурации сборки нужно было
       обязательно использовать параметр --with-poll_module.


Изменения в nginx 0.1.0                                           04.10.2004

    *) Первая публично доступная версия.

