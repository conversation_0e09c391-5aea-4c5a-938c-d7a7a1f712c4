<!doctype html>
<html>
<head>
	<title></title>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<meta http-equiv="Pragma" content="no-cache" />
	<meta http-equiv="Cache-Control" content="no-cache, must-revalidate" />
	<meta http-equiv="Expires" content="0" />
</head>
<body>
</body>
<script>
	var szLanguage = (navigator.language || navigator.browserLanguage).toLowerCase();
	szLanguage = szLanguage.substring(0, 2);
	if ("zh" === szLanguage) {
		window.location.href = "cn/demo.html";
	} else {
		window.location.href = "en/demo.html";
	}
</script>
</html>